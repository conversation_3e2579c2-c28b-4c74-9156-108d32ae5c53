"""
Advanced Data Quality Optimizer for Live Trading

Provides sophisticated NaN handling, outlier detection, and data quality
optimization specifically designed for real-time trading feature generation.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import logging
import yaml

# Import logging from existing system
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin


@dataclass
class DataQualityMetrics:
    """Data quality metrics for monitoring."""
    
    total_features: int = 0
    nan_count_before: int = 0
    nan_count_after: int = 0
    infinite_count_before: int = 0
    infinite_count_after: int = 0
    outlier_count: int = 0
    features_with_issues: List[str] = field(default_factory=list)
    quality_score: float = 0.0
    processing_time_ms: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


class AdvancedDataQualityOptimizer(LoggerMixin):
    """
    Advanced data quality optimizer for live trading features.
    
    Provides sophisticated NaN handling, outlier detection, and feature-specific
    optimization strategies to ensure high-quality model inputs.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize data quality optimizer.

        Args:
            config: Configuration dictionary for optimization parameters
        """
        self.config = config or self._get_default_config()

        # Load feature quality configuration
        self.quality_config = self._load_quality_config()

        # Feature-specific handling strategies (now loaded from config)
        self.feature_strategies = self._initialize_feature_strategies_from_config()
        
        # Quality monitoring
        self.quality_history: List[DataQualityMetrics] = []
        self.feature_quality_scores: Dict[str, float] = {}
        
        # Performance tracking
        self.processing_stats = {
            'total_processed': 0,
            'avg_processing_time_ms': 0.0,
            'avg_quality_improvement': 0.0,
            'problematic_features': set()
        }
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for data quality optimization."""
        return {
            'nan_handling': {
                'max_nan_ratio': 0.1,  # 10% max NaN values per feature
                'forward_fill_limit': 5,
                'backward_fill_limit': 3,
                'interpolation_limit': 10,
                'use_feature_specific_defaults': True
            },
            'outlier_detection': {
                'z_score_threshold': 3.0,
                'iqr_multiplier': 1.5,
                'use_robust_scaling': True,
                'clip_extreme_values': True
            },
            'feature_validation': {
                'min_variance_threshold': 1e-6,
                'max_correlation_threshold': 0.99,
                'check_infinite_values': True,
                'validate_ranges': True
            },
            'performance': {
                'enable_caching': True,
                'cache_size': 1000,
                'parallel_processing': False
            }
        }

    def _load_quality_config(self) -> Dict[str, Any]:
        """Load feature quality configuration from YAML file."""
        try:
            config_path = Path(__file__).parent / "feature_quality_config.yaml"
            if config_path.exists():
                with open(config_path, 'r') as f:
                    return yaml.safe_load(f)
            else:
                self.logger.warning(f"Quality config file not found: {config_path}")
                return {}
        except Exception as e:
            self.logger.error(f"Error loading quality config: {str(e)}")
            return {}

    def _initialize_feature_strategies_from_config(self) -> Dict[str, Dict[str, Any]]:
        """Initialize feature strategies from loaded configuration."""
        try:
            if not self.quality_config or 'nan_handling_strategies' not in self.quality_config:
                self.logger.warning("Using fallback feature strategies")
                return self._initialize_feature_strategies_fallback()

            strategies = {}
            nan_strategies = self.quality_config['nan_handling_strategies']

            for strategy_name, strategy_config in nan_strategies.items():
                strategies[strategy_name] = {
                    'patterns': strategy_config.get('patterns', []),
                    'nan_strategy': strategy_config.get('strategy', 'zero_fill'),
                    'outlier_strategy': 'clip',  # Default outlier strategy
                    'default_value': strategy_config.get('default_value', 0.0),
                    'valid_range': strategy_config.get('validation', {}).get('valid_range'),
                    'interpolation_method': strategy_config.get('interpolation_method', 'linear'),
                    'max_interpolation_gap': strategy_config.get('max_interpolation_gap', 10),
                    'fallback_method': strategy_config.get('fallback_method', 'zero_fill')
                }

            return strategies

        except Exception as e:
            self.logger.error(f"Error initializing strategies from config: {str(e)}")
            return self._initialize_feature_strategies_fallback()

    def _initialize_feature_strategies_fallback(self) -> Dict[str, Dict[str, Any]]:
        """Initialize feature-specific handling strategies."""
        return {
            # Price-based features
            'price_features': {
                'patterns': ['return', 'roc', 'momentum', 'gap', 'intraday'],
                'nan_strategy': 'zero_fill',  # Neutral return
                'outlier_strategy': 'clip',
                'default_value': 0.0,
                'valid_range': (-0.1, 0.1),  # ±10% returns
                'interpolation_method': 'linear'
            },
            
            # Technical indicators (bounded 0-100)
            'bounded_indicators': {
                'patterns': ['rsi', 'stoch', 'williams', 'cci'],
                'nan_strategy': 'neutral_fill',
                'outlier_strategy': 'clip',
                'default_value': 50.0,  # Neutral value
                'valid_range': (0, 100),
                'interpolation_method': 'linear'
            },
            
            # Correlation features (-1 to 1)
            'correlation_features': {
                'patterns': ['corr_', 'correlation'],
                'nan_strategy': 'zero_fill',  # No correlation
                'outlier_strategy': 'clip',
                'default_value': 0.0,
                'valid_range': (-1.0, 1.0),
                'interpolation_method': 'linear'
            },
            
            # Volatility features
            'volatility_features': {
                'patterns': ['vol', 'atr', 'bb_width', 'volatility'],
                'nan_strategy': 'median_fill',
                'outlier_strategy': 'winsorize',
                'default_value': None,  # Use median
                'valid_range': (0, None),  # Non-negative
                'interpolation_method': 'linear'
            },
            
            # Volume features
            'volume_features': {
                'patterns': ['volume', 'vwap', 'obv'],
                'nan_strategy': 'forward_fill',
                'outlier_strategy': 'winsorize',
                'default_value': None,  # Use forward fill
                'valid_range': (0, None),  # Non-negative
                'interpolation_method': 'forward'
            },
            
            # Regime and pattern features
            'regime_features': {
                'patterns': ['regime', 'trend', 'pattern', 'session'],
                'nan_strategy': 'mode_fill',
                'outlier_strategy': 'none',
                'default_value': 0,  # Neutral regime
                'valid_range': None,
                'interpolation_method': 'nearest'
            }
        }
    
    def optimize_features(self, features: pd.DataFrame, 
                         market_data: Optional[pd.DataFrame] = None) -> Tuple[pd.DataFrame, DataQualityMetrics]:
        """
        Optimize feature quality with comprehensive NaN handling and validation.
        
        Args:
            features: Input features DataFrame
            market_data: Optional market data for context-aware optimization
            
        Returns:
            Tuple of (optimized_features, quality_metrics)
        """
        start_time = datetime.now()
        
        # Initialize metrics
        metrics = DataQualityMetrics(
            total_features=len(features.columns),
            nan_count_before=features.isnull().sum().sum(),
            infinite_count_before=np.isinf(features.select_dtypes(include=[np.number])).sum().sum()
        )
        
        try:
            self.logger.info(f"Starting data quality optimization for {metrics.total_features} features")
            self.logger.info(f"Initial quality issues: {metrics.nan_count_before} NaN, {metrics.infinite_count_before} infinite")
            
            # Step 1: Handle infinite values
            optimized_features = self._handle_infinite_values(features.copy())
            
            # Step 2: Feature-specific NaN handling
            optimized_features = self._handle_nan_values_advanced(optimized_features, market_data)
            
            # Step 3: Outlier detection and handling
            optimized_features = self._handle_outliers(optimized_features)
            
            # Step 4: Feature validation and range checking
            optimized_features = self._validate_feature_ranges(optimized_features)
            
            # Step 5: Final quality checks
            optimized_features = self._final_quality_checks(optimized_features)
            
            # Calculate final metrics
            metrics.nan_count_after = optimized_features.isnull().sum().sum()
            metrics.infinite_count_after = np.isinf(optimized_features.select_dtypes(include=[np.number])).sum().sum()
            metrics.quality_score = self._calculate_quality_score(optimized_features)
            metrics.processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            
            # Update statistics
            self._update_processing_stats(metrics)
            self.quality_history.append(metrics)
            
            self.logger.info(f"Data quality optimization complete:")
            self.logger.info(f"  NaN values: {metrics.nan_count_before} → {metrics.nan_count_after}")
            self.logger.info(f"  Infinite values: {metrics.infinite_count_before} → {metrics.infinite_count_after}")
            self.logger.info(f"  Quality score: {metrics.quality_score:.3f}")
            self.logger.info(f"  Processing time: {metrics.processing_time_ms:.1f}ms")
            
            return optimized_features, metrics
            
        except Exception as e:
            self.logger.error(f"Data quality optimization failed: {str(e)}")
            # Return original features with error metrics
            metrics.processing_time_ms = (datetime.now() - start_time).total_seconds() * 1000
            return features, metrics
    
    def _handle_infinite_values(self, features: pd.DataFrame) -> pd.DataFrame:
        """Handle infinite values in features."""
        try:
            # Replace infinite values with NaN for proper handling
            features = features.replace([np.inf, -np.inf], np.nan)
            
            infinite_count = np.isinf(features.select_dtypes(include=[np.number])).sum().sum()
            if infinite_count > 0:
                self.logger.warning(f"Replaced {infinite_count} infinite values with NaN")
            
            return features
            
        except Exception as e:
            self.logger.error(f"Error handling infinite values: {str(e)}")
            return features
    
    def _handle_nan_values_advanced(self, features: pd.DataFrame, 
                                  market_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """Advanced NaN handling with feature-specific strategies."""
        try:
            optimized_features = features.copy()
            
            for feature_name in features.columns:
                if features[feature_name].isnull().sum() == 0:
                    continue
                
                # Determine feature strategy
                strategy = self._get_feature_strategy(feature_name)
                
                # Apply strategy-specific handling
                optimized_features[feature_name] = self._apply_nan_strategy(
                    features[feature_name], strategy, market_data
                )
            
            return optimized_features
            
        except Exception as e:
            self.logger.error(f"Error in advanced NaN handling: {str(e)}")
            return features.fillna(0.0)  # Fallback
    
    def _get_feature_strategy(self, feature_name: str) -> Dict[str, Any]:
        """Get appropriate strategy for a feature based on its name."""
        feature_name_lower = feature_name.lower()
        
        for strategy_name, strategy_config in self.feature_strategies.items():
            for pattern in strategy_config['patterns']:
                if pattern in feature_name_lower:
                    return strategy_config
        
        # Default strategy for unknown features
        return {
            'nan_strategy': 'zero_fill',
            'outlier_strategy': 'clip',
            'default_value': 0.0,
            'valid_range': None,
            'interpolation_method': 'linear'
        }
    
    def _apply_nan_strategy(self, series: pd.Series, strategy: Dict[str, Any], 
                          market_data: Optional[pd.DataFrame] = None) -> pd.Series:
        """Apply specific NaN handling strategy to a series."""
        try:
            strategy_type = strategy['nan_strategy']
            
            if strategy_type == 'zero_fill':
                return series.fillna(strategy['default_value'])
            
            elif strategy_type == 'neutral_fill':
                return series.fillna(strategy['default_value'])
            
            elif strategy_type == 'median_fill':
                median_val = series.median()
                if pd.isna(median_val):
                    # Ensure we never pass None to fillna - use 0.0 as safe fallback
                    fallback_val = strategy.get('default_value')
                    median_val = fallback_val if fallback_val is not None else 0.0
                # Double-check median_val is not None before fillna
                if median_val is None:
                    median_val = 0.0
                return series.fillna(median_val)

            elif strategy_type == 'forward_fill':
                filled = series.ffill(limit=self.config['nan_handling']['forward_fill_limit'])
                fallback_val = strategy.get('default_value', 0.0)
                # Ensure fallback_val is not None
                if fallback_val is None:
                    fallback_val = 0.0
                return filled.fillna(fallback_val)
            
            elif strategy_type == 'interpolation':
                method = strategy.get('interpolation_method', 'linear')
                interpolated = series.interpolate(method=method, limit=self.config['nan_handling']['interpolation_limit'])
                fallback_val = strategy.get('default_value', 0.0)
                # Ensure fallback_val is not None
                if fallback_val is None:
                    fallback_val = 0.0
                return interpolated.fillna(fallback_val)
            
            elif strategy_type == 'mode_fill':
                mode_val = series.mode()
                if len(mode_val) > 0:
                    return series.fillna(mode_val.iloc[0])
                else:
                    fallback_val = strategy.get('default_value', 0)
                    # Ensure fallback_val is not None
                    if fallback_val is None:
                        fallback_val = 0.0
                    return series.fillna(fallback_val)
            
            else:
                # Default to zero fill
                fallback_val = strategy.get('default_value', 0.0)
                # Ensure fallback_val is not None
                if fallback_val is None:
                    fallback_val = 0.0
                return series.fillna(fallback_val)
                
        except Exception as e:
            self.logger.warning(f"Error applying NaN strategy {strategy_type}: {str(e)}")
            return series.fillna(0.0)

    def _handle_outliers(self, features: pd.DataFrame) -> pd.DataFrame:
        """Handle outliers in features using robust methods."""
        try:
            optimized_features = features.copy()
            outlier_count = 0

            for feature_name in features.columns:
                if not pd.api.types.is_numeric_dtype(features[feature_name]):
                    continue

                strategy = self._get_feature_strategy(feature_name)
                outlier_strategy = strategy['outlier_strategy']

                if outlier_strategy == 'clip':
                    # Clip to reasonable range based on feature type
                    valid_range = strategy.get('valid_range')
                    if valid_range:
                        before_count = len(optimized_features[feature_name])
                        optimized_features[feature_name] = optimized_features[feature_name].clip(
                            valid_range[0], valid_range[1]
                        )
                        after_count = len(optimized_features[feature_name])
                        outlier_count += before_count - after_count

                elif outlier_strategy == 'winsorize':
                    # Winsorize extreme values (5th and 95th percentiles)
                    q05 = optimized_features[feature_name].quantile(0.05)
                    q95 = optimized_features[feature_name].quantile(0.95)
                    optimized_features[feature_name] = optimized_features[feature_name].clip(q05, q95)

                elif outlier_strategy == 'z_score':
                    # Remove values beyond z-score threshold
                    z_scores = np.abs((optimized_features[feature_name] - optimized_features[feature_name].mean()) /
                                    optimized_features[feature_name].std())
                    threshold = self.config['outlier_detection']['z_score_threshold']
                    outlier_mask = z_scores > threshold
                    optimized_features.loc[outlier_mask, feature_name] = np.nan
                    outlier_count += outlier_mask.sum()

            if outlier_count > 0:
                self.logger.info(f"Handled {outlier_count} outlier values")

            return optimized_features

        except Exception as e:
            self.logger.error(f"Error handling outliers: {str(e)}")
            return features

    def _validate_feature_ranges(self, features: pd.DataFrame) -> pd.DataFrame:
        """Validate and correct feature ranges."""
        try:
            validated_features = features.copy()

            for feature_name in features.columns:
                strategy = self._get_feature_strategy(feature_name)
                valid_range = strategy.get('valid_range')

                if valid_range and pd.api.types.is_numeric_dtype(features[feature_name]):
                    # Check for values outside valid range
                    min_val, max_val = valid_range

                    if min_val is not None:
                        invalid_mask = validated_features[feature_name] < min_val
                        if invalid_mask.any():
                            validated_features.loc[invalid_mask, feature_name] = min_val

                    if max_val is not None:
                        invalid_mask = validated_features[feature_name] > max_val
                        if invalid_mask.any():
                            validated_features.loc[invalid_mask, feature_name] = max_val

            return validated_features

        except Exception as e:
            self.logger.error(f"Error validating feature ranges: {str(e)}")
            return features

    def _final_quality_checks(self, features: pd.DataFrame) -> pd.DataFrame:
        """Perform final quality checks and cleanup."""
        try:
            final_features = features.copy()

            # Ensure no NaN values remain
            remaining_nan = final_features.isnull().sum().sum()
            if remaining_nan > 0:
                self.logger.warning(f"Final cleanup: filling {remaining_nan} remaining NaN values with 0")
                final_features = final_features.fillna(0.0)

            # Ensure no infinite values remain
            numeric_cols = final_features.select_dtypes(include=[np.number]).columns
            infinite_mask = np.isinf(final_features[numeric_cols])
            if infinite_mask.any().any():
                self.logger.warning("Final cleanup: replacing remaining infinite values")
                final_features[numeric_cols] = final_features[numeric_cols].replace([np.inf, -np.inf], 0.0)

            # Check for constant features (zero variance)
            for col in numeric_cols:
                try:
                    variance = final_features[col].var()
                    if pd.notna(variance) and variance == 0:
                        self.logger.warning(f"Feature {col} has zero variance")
                        self.processing_stats['problematic_features'].add(col)
                except Exception as e:
                    self.logger.debug(f"Could not calculate variance for {col}: {str(e)}")

            return final_features

        except Exception as e:
            self.logger.error(f"Error in final quality checks: {str(e)}")
            return features.fillna(0.0)

    def _calculate_quality_score(self, features: pd.DataFrame) -> float:
        """Calculate overall quality score (0-1 scale)."""
        try:
            score = 1.0

            # Penalize NaN values
            nan_ratio = features.isnull().sum().sum() / (len(features) * len(features.columns))
            score -= nan_ratio * 0.5

            # Penalize infinite values
            numeric_cols = features.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                inf_ratio = np.isinf(features[numeric_cols]).sum().sum() / (len(features) * len(numeric_cols))
                score -= inf_ratio * 0.3

            # Penalize zero variance features
            zero_var_count = 0
            for col in numeric_cols:
                try:
                    variance = features[col].var()
                    if pd.notna(variance) and variance == 0:
                        zero_var_count += 1
                except:
                    pass  # Skip problematic columns

            if len(numeric_cols) > 0:
                zero_var_ratio = zero_var_count / len(numeric_cols)
                score -= zero_var_ratio * 0.2

            return max(0.0, min(1.0, score))

        except Exception as e:
            self.logger.error(f"Error calculating quality score: {str(e)}")
            return 0.5

    def _update_processing_stats(self, metrics: DataQualityMetrics):
        """Update processing statistics."""
        try:
            self.processing_stats['total_processed'] += 1

            # Update average processing time
            total_time = (self.processing_stats['avg_processing_time_ms'] *
                         (self.processing_stats['total_processed'] - 1) +
                         metrics.processing_time_ms)
            self.processing_stats['avg_processing_time_ms'] = total_time / self.processing_stats['total_processed']

            # Update average quality improvement
            if metrics.nan_count_before > 0:
                improvement = (metrics.nan_count_before - metrics.nan_count_after) / metrics.nan_count_before
                total_improvement = (self.processing_stats['avg_quality_improvement'] *
                                   (self.processing_stats['total_processed'] - 1) + improvement)
                self.processing_stats['avg_quality_improvement'] = total_improvement / self.processing_stats['total_processed']

        except Exception as e:
            self.logger.error(f"Error updating processing stats: {str(e)}")

    def get_quality_report(self) -> Dict[str, Any]:
        """Get comprehensive quality report."""
        try:
            if not self.quality_history:
                return {'status': 'no_data', 'message': 'No quality metrics available'}

            latest_metrics = self.quality_history[-1]

            return {
                'status': 'success',
                'latest_metrics': {
                    'total_features': latest_metrics.total_features,
                    'nan_reduction': latest_metrics.nan_count_before - latest_metrics.nan_count_after,
                    'infinite_reduction': latest_metrics.infinite_count_before - latest_metrics.infinite_count_after,
                    'quality_score': latest_metrics.quality_score,
                    'processing_time_ms': latest_metrics.processing_time_ms
                },
                'processing_stats': self.processing_stats,
                'problematic_features': list(self.processing_stats['problematic_features']),
                'history_length': len(self.quality_history)
            }

        except Exception as e:
            self.logger.error(f"Error generating quality report: {str(e)}")
            return {'status': 'error', 'message': str(e)}
