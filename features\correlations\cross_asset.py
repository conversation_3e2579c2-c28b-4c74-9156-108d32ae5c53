"""
Cross-Asset Correlation Engine

Generates comprehensive cross-asset correlation features including:
- Rolling correlations with major assets (DXY, SPY, TLT, VIX, QQQ, IEF)
- Correlation regime detection and change points
- Multi-asset momentum analysis
- Divergence detection between price and correlations
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pathlib import Path
import glob

# Import base classes
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class CrossAssetCorrelations(BaseFeatureEngine):
    """
    Cross-asset correlation analysis engine.
    
    Generates comprehensive correlation features between XAUUSD and major assets
    including rolling correlations, regime detection, and divergence analysis.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize cross-asset correlation engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.correlation_config = feature_config.correlations
        
        # Asset configuration
        self.assets = self.correlation_config.get('assets', ['DXY', 'SPY', 'TLT', 'VIX', 'QQQ', 'IEF'])
        self.correlation_windows = self.correlation_config.get('windows', [20, 50, 100])
        
        # Regime detection configuration
        self.regime_config = self.correlation_config.get('regime_detection', {})
        self.regime_threshold = self.regime_config.get('threshold', 0.3)
        self.regime_lookback = self.regime_config.get('lookback_periods', 50)
        
        # Data paths
        self.external_data_path = Path("data/external")
        
        # Cache for loaded external data
        self._external_data_cache = {}

        # External data service reference (will be set by live trading system)
        self._external_data_service = None
    
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        return "cross_asset_correlations"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns for correlation analysis."""
        return ['close']

    def set_external_data_service(self, service):
        """Set external data service for live data access."""
        self._external_data_service = service
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        feature_names = []
        
        # Rolling correlation features
        for asset in self.assets:
            for window in self.correlation_windows:
                feature_names.extend([
                    f"corr_{asset.lower()}_{window}",
                    f"corr_{asset.lower()}_{window}_rank",
                    f"corr_{asset.lower()}_{window}_change"
                ])
        
        # Regime detection features
        for asset in self.assets:
            feature_names.extend([
                f"corr_regime_{asset.lower()}",
                f"corr_regime_change_{asset.lower()}"
            ])
        
        # Multi-asset features
        feature_names.extend([
            "avg_correlation",
            "correlation_dispersion",
            "correlation_momentum",
            "risk_on_off_indicator"
        ])
        
        # Divergence features
        for asset in self.assets:
            feature_names.extend([
                f"price_corr_divergence_{asset.lower()}",
                f"momentum_corr_divergence_{asset.lower()}"
            ])
        
        return feature_names
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input XAUUSD data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated cross-asset correlation features
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Initialize features DataFrame
        features = pd.DataFrame(index=data.index)
        
        # Load external asset data
        external_data = self._load_external_assets()
        
        if not external_data:
            self.logger.warning("No external asset data available for correlation analysis")
            return features
        
        # Generate rolling correlation features
        correlation_features = self._generate_rolling_correlations(data, external_data)
        features = pd.concat([features, correlation_features], axis=1)
        
        # Generate regime detection features
        regime_features = self._generate_regime_features(data, external_data, correlation_features)
        features = pd.concat([features, regime_features], axis=1)
        
        # Generate multi-asset features
        multi_asset_features = self._generate_multi_asset_features(correlation_features)
        features = pd.concat([features, multi_asset_features], axis=1)
        
        # Generate divergence features
        divergence_features = self._generate_divergence_features(data, external_data, correlation_features)
        features = pd.concat([features, divergence_features], axis=1)

        # Clean up infinite and extreme values
        features = self._clean_features(features)

        self.logger.info(f"Generated {len(features.columns)} cross-asset correlation features")
        return features
    
    def _load_external_assets(self) -> Dict[str, pd.DataFrame]:
        """Load external asset data from service or files."""
        external_data = {}

        # Try to use external data service first (for live trading)
        if self._external_data_service is not None:
            try:
                cached_data = self._external_data_service.get_all_cached_data()
                for asset in self.assets:
                    if asset in cached_data:
                        asset_data = cached_data[asset]
                        if asset_data is not None and not asset_data.empty:
                            external_data[asset] = asset_data
                            self.logger.debug(f"Loaded {asset} data from service: {len(asset_data)} records")
                        else:
                            self.logger.warning(f"Empty data for asset {asset} from service")
                    else:
                        self.logger.warning(f"Asset {asset} not available in external data service")

                if external_data:
                    self.logger.info(f"Loaded external data for {len(external_data)} assets from service")
                    return external_data
                else:
                    self.logger.warning("No data available from external data service, falling back to files")
            except Exception as e:
                self.logger.warning(f"Error loading data from external service: {str(e)}, falling back to files")

        # Fallback to file-based loading
        for asset in self.assets:
            try:
                asset_data = self._load_asset_data(asset)
                if asset_data is not None and not asset_data.empty:
                    external_data[asset] = asset_data
                    self.logger.debug(f"Loaded {asset} data from file: {len(asset_data)} records")
                else:
                    self.logger.warning(f"No data available for asset: {asset}")
            except Exception as e:
                self.logger.warning(f"Error loading {asset} data: {str(e)}")

        self.logger.info(f"Loaded external data for {len(external_data)} assets")
        return external_data
    
    def _load_asset_data(self, asset: str) -> Optional[pd.DataFrame]:
        """Load data for a specific asset."""
        if asset in self._external_data_cache:
            return self._external_data_cache[asset]
        
        asset_path = self.external_data_path / asset.lower() / "5m"
        
        if not asset_path.exists():
            return None
        
        # Find the most recent CSV file
        csv_files = list(asset_path.glob("*.csv"))
        if not csv_files:
            return None
        
        # Get the most recent file
        latest_file = max(csv_files, key=lambda x: x.stat().st_mtime)
        
        try:
            # Load data
            data = pd.read_csv(latest_file)
            
            # Standardize datetime column
            datetime_cols = ['Datetime', 'datetime', 'Date', 'date', 'time']
            datetime_col = None
            for col in datetime_cols:
                if col in data.columns:
                    datetime_col = col
                    break
            
            if datetime_col is None:
                self.logger.warning(f"No datetime column found in {asset} data")
                return None
            
            # Convert to datetime and set as index
            data[datetime_col] = pd.to_datetime(data[datetime_col])

            # Remove timezone information to match XAUUSD data
            if data[datetime_col].dt.tz is not None:
                data[datetime_col] = data[datetime_col].dt.tz_localize(None)

            data = data.set_index(datetime_col)
            
            # Ensure we have close price
            if 'close' not in data.columns:
                self.logger.warning(f"No close price column found in {asset} data")
                return None
            
            # Cache the data
            self._external_data_cache[asset] = data
            
            return data
            
        except Exception as e:
            self.logger.warning(f"Error reading {asset} data from {latest_file}: {str(e)}")
            return None
    
    def _generate_rolling_correlations(self, xauusd_data: pd.DataFrame, external_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Generate rolling correlation features."""
        corr_df = pd.DataFrame(index=xauusd_data.index)
        
        # Calculate returns for XAUUSD
        xauusd_returns = xauusd_data['close'].pct_change()
        
        for asset, asset_data in external_data.items():
            try:
                # Calculate returns for external asset
                asset_returns = asset_data['close'].pct_change()
                
                # Align data by reindexing to XAUUSD timeframe
                # Ensure both indices are timezone-naive for proper alignment
                if asset_returns.index.tz is not None:
                    asset_returns.index = asset_returns.index.tz_localize(None)

                # Ensure XAUUSD index is also timezone-naive
                xauusd_index = xauusd_data.index
                if hasattr(xauusd_index, 'tz') and xauusd_index.tz is not None:
                    xauusd_index = xauusd_index.tz_localize(None)

                # Align with forward fill, but limit fill to avoid excessive extrapolation
                asset_returns_aligned = asset_returns.reindex(xauusd_index, method='ffill', limit=12)  # Max 1 hour forward fill
                
                for window in self.correlation_windows:
                    # Rolling correlation
                    correlation = xauusd_returns.rolling(window=window).corr(asset_returns_aligned)
                    corr_df[f"corr_{asset.lower()}_{window}"] = correlation
                    
                    # Correlation rank (percentile within rolling window)
                    # Use min(window, 50) to avoid excessive NaN values
                    rank_window = min(window, 50)
                    corr_rank = correlation.rolling(window=rank_window).rank(pct=True)
                    corr_df[f"corr_{asset.lower()}_{window}_rank"] = corr_rank
                    
                    # Correlation change (momentum)
                    corr_change = correlation.diff(5)  # 5-period change
                    corr_df[f"corr_{asset.lower()}_{window}_change"] = corr_change
                
            except Exception as e:
                self.logger.warning(f"Error calculating correlations for {asset}: {str(e)}")
                # Log additional debug info for troubleshooting
                self.logger.debug(f"Asset data shape: {asset_data.shape if 'asset_data' in locals() else 'N/A'}")
                self.logger.debug(f"Asset returns shape: {asset_returns.shape if 'asset_returns' in locals() else 'N/A'}")
                self.logger.debug(f"XAUUSD data shape: {xauusd_data.shape}")
                import traceback
                self.logger.debug(f"Full traceback: {traceback.format_exc()}")
        
        self.logger.debug(f"Generated {len(corr_df.columns)} rolling correlation features")
        return corr_df
    
    def _generate_regime_features(self, xauusd_data: pd.DataFrame, external_data: Dict[str, pd.DataFrame], 
                                correlation_features: pd.DataFrame) -> pd.DataFrame:
        """Generate correlation regime detection features."""
        regime_df = pd.DataFrame(index=xauusd_data.index)
        
        for asset in external_data.keys():
            try:
                # Use the longest correlation window for regime detection
                corr_col = f"corr_{asset.lower()}_{max(self.correlation_windows)}"
                
                if corr_col in correlation_features.columns:
                    correlation = correlation_features[corr_col]
                    
                    # Detect regime changes using rolling standard deviation
                    corr_volatility = correlation.rolling(window=self.regime_lookback).std()
                    regime_threshold = corr_volatility.rolling(window=self.regime_lookback*2).quantile(0.8)
                    
                    # Regime classification
                    regime = pd.Series(0, index=xauusd_data.index)  # 0 = stable
                    regime[corr_volatility > regime_threshold] = 1  # 1 = volatile/changing
                    regime[correlation.abs() > 0.7] = 2  # 2 = strong correlation
                    regime[correlation.abs() < 0.1] = -1  # -1 = weak correlation
                    
                    regime_df[f"corr_regime_{asset.lower()}"] = regime
                    
                    # Regime change detection
                    regime_change = (regime.diff() != 0).astype(int)
                    regime_df[f"corr_regime_change_{asset.lower()}"] = regime_change
                
            except Exception as e:
                self.logger.warning(f"Error calculating regime features for {asset}: {str(e)}")
        
        self.logger.debug(f"Generated {len(regime_df.columns)} regime detection features")
        return regime_df
    
    def _generate_multi_asset_features(self, correlation_features: pd.DataFrame) -> pd.DataFrame:
        """Generate multi-asset correlation features."""
        multi_df = pd.DataFrame(index=correlation_features.index)
        
        try:
            # Get all correlation columns (using medium window)
            medium_window = sorted(self.correlation_windows)[len(self.correlation_windows)//2]
            corr_cols = [col for col in correlation_features.columns if f"_{medium_window}" in col and "corr_" in col and "_rank" not in col and "_change" not in col]
            
            if corr_cols:
                correlations = correlation_features[corr_cols]
                
                # Average correlation across all assets
                multi_df['avg_correlation'] = correlations.mean(axis=1)
                
                # Correlation dispersion (standard deviation)
                multi_df['correlation_dispersion'] = correlations.std(axis=1)
                
                # Correlation momentum (change in average correlation)
                multi_df['correlation_momentum'] = multi_df['avg_correlation'].diff(5)
                
                # Risk-on/Risk-off indicator
                # Based on correlations with SPY (positive) and VIX (negative)
                risk_on_off = pd.Series(0, index=correlation_features.index)
                
                spy_corr_col = f"corr_spy_{medium_window}"
                vix_corr_col = f"corr_vix_{medium_window}"
                
                if spy_corr_col in correlation_features.columns and vix_corr_col in correlation_features.columns:
                    spy_corr = correlation_features[spy_corr_col]
                    vix_corr = correlation_features[vix_corr_col]
                    
                    # Risk-on: positive correlation with SPY, negative with VIX
                    risk_on_off[(spy_corr > 0.3) & (vix_corr < -0.3)] = 1
                    # Risk-off: negative correlation with SPY, positive with VIX
                    risk_on_off[(spy_corr < -0.3) & (vix_corr > 0.3)] = -1
                
                multi_df['risk_on_off_indicator'] = risk_on_off
            
        except Exception as e:
            self.logger.warning(f"Error calculating multi-asset features: {str(e)}")
        
        self.logger.debug(f"Generated {len(multi_df.columns)} multi-asset features")
        return multi_df
    
    def _generate_divergence_features(self, xauusd_data: pd.DataFrame, external_data: Dict[str, pd.DataFrame],
                                    correlation_features: pd.DataFrame) -> pd.DataFrame:
        """Generate price-correlation divergence features."""
        divergence_df = pd.DataFrame(index=xauusd_data.index)
        
        # Calculate XAUUSD price momentum
        xauusd_momentum = xauusd_data['close'].pct_change(10)  # 10-period momentum
        
        for asset in external_data.keys():
            try:
                # Get correlation for this asset
                corr_col = f"corr_{asset.lower()}_{self.correlation_windows[0]}"  # Use shortest window
                
                if corr_col in correlation_features.columns:
                    correlation = correlation_features[corr_col]
                    
                    # Price divergence: when price moves but correlation doesn't follow
                    expected_correlation_change = xauusd_momentum.rolling(window=20).corr(correlation.shift(1))
                    actual_correlation_change = correlation.diff()
                    
                    price_corr_divergence = (expected_correlation_change - actual_correlation_change).abs()
                    divergence_df[f"price_corr_divergence_{asset.lower()}"] = price_corr_divergence
                    
                    # Momentum divergence: when momentum and correlation diverge
                    momentum_corr_divergence = xauusd_momentum.rolling(window=20).corr(correlation)
                    divergence_df[f"momentum_corr_divergence_{asset.lower()}"] = momentum_corr_divergence
                
            except Exception as e:
                self.logger.warning(f"Error calculating divergence features for {asset}: {str(e)}")
        
        self.logger.debug(f"Generated {len(divergence_df.columns)} divergence features")
        return divergence_df

    def _clean_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Clean features by handling infinite and extreme values."""
        cleaned_features = features.copy()

        # Replace infinite values with NaN
        cleaned_features = cleaned_features.replace([np.inf, -np.inf], np.nan)

        # For correlation features, clip extreme values to reasonable range
        for col in cleaned_features.columns:
            if 'corr_' in col and not col.endswith('_rank'):
                # Clip correlations to [-1, 1] range
                cleaned_features[col] = cleaned_features[col].clip(-1.0, 1.0)
            elif col.endswith('_change'):
                # Clip correlation changes to reasonable range
                cleaned_features[col] = cleaned_features[col].clip(-2.0, 2.0)

        # Log cleaning statistics
        inf_count = np.isinf(features.values).sum()
        if inf_count > 0:
            self.logger.warning(f"Cleaned {inf_count} infinite values in correlation features")

        return cleaned_features
