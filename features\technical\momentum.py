"""
Momentum Technical Indicators Engine

Generates comprehensive momentum-based technical indicators including:
- Adaptive RSI with divergence detection
- MACD variations with multiple parameter sets
- Stochastic oscillators
- Williams %R
- Momentum divergence analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class MomentumIndicators(BaseFeatureEngine):
    """
    Momentum technical indicators engine.
    
    Generates comprehensive momentum-based indicators for XAUUSD trading
    including adaptive RSI, MACD variations, and divergence detection.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize momentum indicators engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.technical_config = feature_config.technical_indicators
        
        # RSI Configuration
        self.rsi_config = self.technical_config.get('rsi', {})
        self.rsi_periods = self.rsi_config.get('periods', [14])
        self.rsi_adaptive = self.rsi_config.get('adaptive', False)
        self.rsi_divergence = self.rsi_config.get('divergence_detection', False)
        self.rsi_overbought = self.rsi_config.get('overbought_threshold', 70)
        self.rsi_oversold = self.rsi_config.get('oversold_threshold', 30)
        
        # MACD Configuration
        self.macd_config = self.technical_config.get('macd', {})
        self.macd_parameter_sets = self.macd_config.get('parameter_sets', [
            {'fast': 12, 'slow': 26, 'signal': 9}
        ])
        
        # Stochastic Configuration
        self.stoch_periods = [14, 21]
        self.stoch_smooth_k = 3
        self.stoch_smooth_d = 3
        
        # Williams %R Configuration
        self.williams_periods = [14, 21]
    
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        return "momentum_indicators"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns for momentum indicators."""
        return ['open', 'high', 'low', 'close']
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        feature_names = []
        
        # RSI features
        for period in self.rsi_periods:
            feature_names.extend([
                f"rsi_{period}",
                f"rsi_{period}_normalized",
                f"rsi_{period}_momentum"
            ])
            if self.rsi_adaptive:
                feature_names.append(f"rsi_adaptive_{period}")
            if self.rsi_divergence:
                feature_names.append(f"rsi_{period}_divergence")
        
        # MACD features
        for i, params in enumerate(self.macd_parameter_sets):
            suffix = f"{params['fast']}_{params['slow']}_{params['signal']}"
            feature_names.extend([
                f"macd_{suffix}",
                f"macd_signal_{suffix}",
                f"macd_histogram_{suffix}",
                f"macd_crossover_{suffix}"
            ])
        
        # Stochastic features
        for period in self.stoch_periods:
            feature_names.extend([
                f"stoch_k_{period}",
                f"stoch_d_{period}",
                f"stoch_crossover_{period}"
            ])
        
        # Williams %R features
        for period in self.williams_periods:
            feature_names.extend([
                f"williams_r_{period}",
                f"williams_r_{period}_normalized"
            ])
        
        return feature_names
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated momentum indicator features
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Initialize features DataFrame
        features = pd.DataFrame(index=data.index)
        
        # Generate RSI features
        rsi_features = self._generate_rsi_features(data)
        features = pd.concat([features, rsi_features], axis=1)
        
        # Generate MACD features
        macd_features = self._generate_macd_features(data)
        features = pd.concat([features, macd_features], axis=1)
        
        # Generate Stochastic features
        stoch_features = self._generate_stochastic_features(data)
        features = pd.concat([features, stoch_features], axis=1)
        
        # Generate Williams %R features
        williams_features = self._generate_williams_r_features(data)
        features = pd.concat([features, williams_features], axis=1)
        
        self.logger.info(f"Generated {len(features.columns)} momentum indicator features")
        return features
    
    def _generate_rsi_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate RSI-based features."""
        rsi_df = pd.DataFrame(index=data.index)
        
        for period in self.rsi_periods:
            try:
                # Calculate standard RSI
                rsi = self._calculate_rsi(data['close'], period)
                rsi_df[f"rsi_{period}"] = rsi
                
                # Normalized RSI (0-1 scale)
                rsi_df[f"rsi_{period}_normalized"] = (rsi - 50) / 50
                
                # RSI momentum (rate of change)
                rsi_df[f"rsi_{period}_momentum"] = rsi.diff()
                
                # Adaptive RSI
                if self.rsi_adaptive:
                    adaptive_rsi = self._calculate_adaptive_rsi(data['close'], period)
                    rsi_df[f"rsi_adaptive_{period}"] = adaptive_rsi
                
                # RSI divergence detection
                if self.rsi_divergence:
                    divergence = self._detect_rsi_divergence(data['close'], rsi, period)
                    rsi_df[f"rsi_{period}_divergence"] = divergence
                
            except Exception as e:
                self.logger.warning(f"Error calculating RSI for period {period}: {str(e)}")
        
        self.logger.debug(f"Generated {len(rsi_df.columns)} RSI features")
        return rsi_df
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """Calculate Relative Strength Index with robust error handling."""
        try:
            # Clean input data
            prices = prices.replace([np.inf, -np.inf], np.nan)
            prices = prices.fillna(method='ffill').fillna(method='bfill')

            if len(prices.dropna()) < period:
                # Not enough data for calculation
                return pd.Series(50.0, index=prices.index)  # Neutral RSI

            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=1).mean()

            # Handle division by zero
            rs = gain / loss.replace(0, np.nan)
            rs = rs.fillna(0)  # If loss is 0, RS is 0 (RSI = 100)

            # Calculate RSI with bounds checking
            rsi = 100 - (100 / (1 + rs))

            # Clean output
            rsi = rsi.replace([np.inf, -np.inf], 50.0)  # Replace infinite values with neutral
            rsi = rsi.fillna(50.0)  # Fill NaN with neutral RSI
            rsi = rsi.clip(0, 100)  # Ensure RSI is within bounds

            return rsi

        except Exception as e:
            self.logger.warning(f"RSI calculation failed: {str(e)}, returning neutral RSI")
            return pd.Series(50.0, index=prices.index)
    
    def _calculate_adaptive_rsi(self, prices: pd.Series, base_period: int) -> pd.Series:
        """Calculate adaptive RSI with dynamic period based on volatility."""
        try:
            # Clean input data
            prices = prices.replace([np.inf, -np.inf], np.nan)
            prices = prices.fillna(method='ffill').fillna(method='bfill')

            if len(prices.dropna()) < base_period:
                return pd.Series(50.0, index=prices.index)

            # Calculate volatility-based adaptive period
            returns = prices.pct_change().replace([np.inf, -np.inf], np.nan).fillna(0)
            volatility = returns.rolling(20, min_periods=1).std()
            vol_percentile = volatility.rolling(100, min_periods=1).rank(pct=True)
            vol_percentile = vol_percentile.fillna(0.5)  # Neutral percentile

            # Adjust period based on volatility (higher vol = shorter period)
            adaptive_period = (base_period * (2 - vol_percentile)).round()
            adaptive_period = adaptive_period.clip(lower=5, upper=base_period * 2).astype(int)

            # For simplicity, use base period (full adaptive implementation would be complex)
            return self._calculate_rsi(prices, base_period)

        except Exception as e:
            self.logger.warning(f"Adaptive RSI calculation failed: {str(e)}")
            return self._calculate_rsi(prices, base_period)
    
    def _detect_rsi_divergence(self, prices: pd.Series, rsi: pd.Series, lookback: int = 20) -> pd.Series:
        """Detect RSI divergence patterns."""
        divergence = pd.Series(0, index=prices.index)
        
        # Find local peaks and troughs
        price_peaks = self._find_peaks(prices, lookback)
        rsi_peaks = self._find_peaks(rsi, lookback)
        
        # Bullish divergence: price makes lower low, RSI makes higher low
        # Bearish divergence: price makes higher high, RSI makes lower high
        # Simplified implementation
        
        return divergence
    
    def _find_peaks(self, series: pd.Series, window: int) -> pd.Series:
        """Find local peaks in a series."""
        peaks = pd.Series(False, index=series.index)
        
        for i in range(window, len(series) - window):
            if series.iloc[i] == series.iloc[i-window:i+window+1].max():
                peaks.iloc[i] = True
        
        return peaks
    
    def _generate_macd_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate MACD-based features."""
        macd_df = pd.DataFrame(index=data.index)
        
        for i, params in enumerate(self.macd_parameter_sets):
            try:
                fast = params['fast']
                slow = params['slow']
                signal = params['signal']
                suffix = f"{fast}_{slow}_{signal}"
                
                # Calculate MACD
                ema_fast = data['close'].ewm(span=fast).mean()
                ema_slow = data['close'].ewm(span=slow).mean()
                macd_line = ema_fast - ema_slow
                signal_line = macd_line.ewm(span=signal).mean()
                histogram = macd_line - signal_line
                
                macd_df[f"macd_{suffix}"] = macd_line
                macd_df[f"macd_signal_{suffix}"] = signal_line
                macd_df[f"macd_histogram_{suffix}"] = histogram
                
                # MACD crossover signals
                crossover = pd.Series(0, index=data.index)
                crossover[macd_line > signal_line] = 1
                crossover[macd_line < signal_line] = -1
                macd_df[f"macd_crossover_{suffix}"] = crossover
                
            except Exception as e:
                self.logger.warning(f"Error calculating MACD {params}: {str(e)}")
        
        self.logger.debug(f"Generated {len(macd_df.columns)} MACD features")
        return macd_df
    
    def _generate_stochastic_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate Stochastic oscillator features."""
        stoch_df = pd.DataFrame(index=data.index)
        
        for period in self.stoch_periods:
            try:
                # Calculate %K
                lowest_low = data['low'].rolling(window=period).min()
                highest_high = data['high'].rolling(window=period).max()
                k_percent = 100 * ((data['close'] - lowest_low) / (highest_high - lowest_low))
                
                # Smooth %K
                k_smooth = k_percent.rolling(window=self.stoch_smooth_k).mean()
                
                # Calculate %D (signal line)
                d_percent = k_smooth.rolling(window=self.stoch_smooth_d).mean()
                
                stoch_df[f"stoch_k_{period}"] = k_smooth
                stoch_df[f"stoch_d_{period}"] = d_percent
                
                # Stochastic crossover
                crossover = pd.Series(0, index=data.index)
                crossover[k_smooth > d_percent] = 1
                crossover[k_smooth < d_percent] = -1
                stoch_df[f"stoch_crossover_{period}"] = crossover
                
            except Exception as e:
                self.logger.warning(f"Error calculating Stochastic for period {period}: {str(e)}")
        
        self.logger.debug(f"Generated {len(stoch_df.columns)} Stochastic features")
        return stoch_df
    
    def _generate_williams_r_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate Williams %R features."""
        williams_df = pd.DataFrame(index=data.index)
        
        for period in self.williams_periods:
            try:
                # Calculate Williams %R
                highest_high = data['high'].rolling(window=period).max()
                lowest_low = data['low'].rolling(window=period).min()
                williams_r = -100 * ((highest_high - data['close']) / (highest_high - lowest_low))
                
                williams_df[f"williams_r_{period}"] = williams_r
                
                # Normalized Williams %R (0-1 scale)
                williams_df[f"williams_r_{period}_normalized"] = (williams_r + 50) / 50
                
            except Exception as e:
                self.logger.warning(f"Error calculating Williams %R for period {period}: {str(e)}")
        
        self.logger.debug(f"Generated {len(williams_df.columns)} Williams %R features")
        return williams_df
