"""
Live Model Inference Engine

Provides real-time model inference with ensemble predictions,
confidence analysis, and model-driven trading signals.

UPDATED: Enhanced for Specialized Ensemble Architecture Integration
- Supports 4-model ensemble (LightGBM, CatBoost, XGBoost, Linear)
- Proper prediction format handling for hierarchical decision framework
- Model-driven TP/SL extraction from ensemble outputs
- 354-feature compatibility validation
"""

import joblib
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import time
import threading
from collections import deque

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin
from models.specialized_ensemble import SpecializedEnsembleOrchestrator
from .feature_alignment import FeatureAlignmentSystem


class LiveModelEngine(LoggerMixin):
    """
    Live model inference engine for real-time trading predictions.
    
    Integrates with existing ensemble models and provides model-driven
    trading signals with confidence analysis and consensus tracking.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize live model engine with enhanced ensemble support.

        Args:
            config: Configuration dictionary
        """
        self.config = config

        # UPDATED: Enhanced model path configuration for ensemble architecture
        self.ensemble_models_dir = config.get('ensemble_models_dir', 'data/models')
        self.model_path = config.get('model_path', 'data/models/retrained_lightgbm_signal_generator_20250922_001127.pkl')
        self.use_specialized_ensemble = config.get('use_specialized_ensemble', True)
        self.feature_pipeline_path = config.get('feature_pipeline_path', 'data/models/xauusd_feature_pipeline.joblib')

        # UPDATED: Model components with enhanced ensemble support
        self.ensemble_model = None
        self.feature_pipeline = None
        # Use the fixed feature alignment system
        from .fixed_feature_alignment import FixedFeatureAlignmentSystem
        self.feature_alignment = FixedFeatureAlignmentSystem()
        self.is_loaded = False

        # UPDATED: Enhanced prediction parameters for ensemble
        self.min_confidence = config.get('min_signal_confidence', 0.50)  # Lowered for ensemble
        self.high_confidence_threshold = config.get('high_confidence_threshold', 0.75)  # Lowered for ensemble
        self.consensus_threshold = config.get('ensemble_consensus_threshold', 0.60)  # Lowered for ensemble

        # UPDATED: Enhanced performance tracking for ensemble
        self.prediction_history = deque(maxlen=1000)
        self.model_stats = {
            'total_predictions': 0,
            'high_confidence_predictions': 0,
            'trading_signals_generated': 0,
            'avg_prediction_time_ms': 0.0,
            'avg_confidence': 0.0,
            'model_consensus_rate': 0.0,
            'ensemble_agreement_rate': 0.0,  # NEW: Track ensemble agreement
            'individual_model_performance': {},  # NEW: Track individual model performance
            'last_prediction_time': None,
            'errors': 0
        }

        # UPDATED: Enhanced output names for ensemble predictions
        self.output_names = [
            'signal_probability',
            'tp1_distance',
            'tp2_distance',
            'sl_distance',
            'hold_time_estimate',
            'volatility_adjustment',
            'market_regime',
            'position_size_category'
        ]

        # NEW: Expected feature count for validation
        self.expected_feature_count = 354
    
    def load_models(self) -> bool:
        """
        Load ensemble model and feature pipeline with enhanced validation.

        UPDATED: Enhanced for specialized ensemble architecture with proper error handling.
        """
        try:
            self.logger.info("🚀 LOADING ENSEMBLE MODEL AND FEATURE PIPELINE...")

            if self.use_specialized_ensemble:
                # UPDATED: Load specialized ensemble with enhanced validation
                self.ensemble_model = self._load_specialized_ensemble()
                self.logger.info("✅ Loaded specialized ensemble with 4 models")

                # Validate ensemble model interface
                if not self._validate_ensemble_interface():
                    self.logger.error("❌ Ensemble model interface validation failed")
                    return False

            else:
                # Fallback to single model
                self.ensemble_model = joblib.load(self.model_path)
                self.logger.info(f"✅ Loaded single model from {self.model_path}")

            # UPDATED: Enhanced feature pipeline loading with validation
            if Path(self.feature_pipeline_path).exists():
                pipeline_data = joblib.load(self.feature_pipeline_path)

                # Extract actual pipeline from metadata structure
                if isinstance(pipeline_data, dict) and 'processing_pipeline' in pipeline_data:
                    self.feature_pipeline = pipeline_data['processing_pipeline']
                    self.logger.info("✅ Loaded feature pipeline from metadata structure")
                else:
                    # Fallback for direct pipeline objects
                    self.feature_pipeline = pipeline_data
                    self.logger.info("✅ Loaded feature pipeline (direct)")

                # NEW: Validate feature pipeline compatibility
                if not self._validate_feature_pipeline():
                    self.logger.warning("⚠️ Feature pipeline validation failed - proceeding with caution")

            else:
                self.logger.warning(f"⚠️ Feature pipeline not found: {self.feature_pipeline_path}")

            self.is_loaded = True
            self.logger.info("🎉 MODEL LOADING COMPLETE - SYSTEM READY")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to load models: {str(e)}")
            import traceback
            self.logger.error(f"❌ Model loading traceback: {traceback.format_exc()}")
            self.model_stats['errors'] += 1
            return False

    def _load_specialized_ensemble(self) -> SpecializedEnsembleOrchestrator:
        """
        Load the specialized ensemble models with enhanced validation.

        UPDATED: Enhanced model loading with better error handling and validation.
        """
        try:
            self.logger.info("🔧 LOADING SPECIALIZED ENSEMBLE MODELS...")

            # UPDATED: Enhanced model paths with fallback options
            models_dir = Path(self.ensemble_models_dir)
            model_files = {
                'lightgbm': models_dir / "retrained_lightgbm_signal_generator_20250922_001127.pkl",
                'catboost': models_dir / "fixed_catboost_market_regime_analyst_20250921_044615.pkl",
                'xgboost': models_dir / "fixed_xgboost_risk_manager_20250921_044032.pkl",
                'linear': models_dir / "specialized_linear_stability_monitor_20250921_042620.pkl"
            }

            # UPDATED: Enhanced model loading with detailed validation
            models = {}
            loaded_models_info = {}

            for model_name, model_path in model_files.items():
                if model_path.exists():
                    try:
                        self.logger.info(f"📂 Loading {model_name} from {model_path}")

                        # Load the model file (contains metadata and multi-output models)
                        model_data = joblib.load(model_path)

                        # UPDATED: Enhanced model extraction with better validation
                        if isinstance(model_data, dict):
                            if 'model' in model_data:
                                model_obj = model_data['model']
                                if isinstance(model_obj, dict):
                                    # Multi-output structure: {'model': {target: model, ...}}
                                    models[model_name] = model_obj
                                    loaded_models_info[model_name] = {
                                        'type': 'multi_output_dict',
                                        'outputs': len(model_obj),
                                        'targets': list(model_obj.keys())
                                    }
                                    self.logger.info(f"✅ Loaded {model_name} multi-output model with {len(model_obj)} outputs: {list(model_obj.keys())}")
                                else:
                                    # Single model object (like MultiOutputRegressor)
                                    models[model_name] = model_obj
                                    if hasattr(model_obj, 'estimators_'):
                                        loaded_models_info[model_name] = {
                                            'type': 'multi_output_regressor',
                                            'outputs': len(model_obj.estimators_)
                                        }
                                        self.logger.info(f"✅ Loaded {model_name} MultiOutputRegressor with {len(model_obj.estimators_)} outputs")
                                    else:
                                        loaded_models_info[model_name] = {
                                            'type': 'single_model',
                                            'outputs': 1
                                        }
                                        self.logger.info(f"✅ Loaded {model_name} single model")
                            elif 'models' in model_data:
                                # CatBoost structure: {'models': {target: model, ...}}
                                model_dict = model_data['models']
                                models[model_name] = model_dict
                                loaded_models_info[model_name] = {
                                    'type': 'models_dict',
                                    'outputs': len(model_dict),
                                    'targets': list(model_dict.keys())
                                }
                                self.logger.info(f"✅ Loaded {model_name} models dict with {len(model_dict)} outputs: {list(model_dict.keys())}")
                            else:
                                # Direct dictionary of models
                                models[model_name] = model_data
                                loaded_models_info[model_name] = {
                                    'type': 'direct_dict',
                                    'outputs': len(model_data) if isinstance(model_data, dict) else 1
                                }
                                self.logger.info(f"✅ Loaded {model_name} model (direct dict)")
                        else:
                            # Fallback for direct model objects
                            models[model_name] = model_data
                            loaded_models_info[model_name] = {
                                'type': 'direct_object',
                                'outputs': 1
                            }
                            self.logger.info(f"✅ Loaded {model_name} model (direct object)")

                    except Exception as e:
                        self.logger.error(f"❌ Failed to load {model_name}: {str(e)}")
                        import traceback
                        self.logger.error(f"❌ {model_name} loading traceback: {traceback.format_exc()}")
                else:
                    self.logger.error(f"❌ Model file not found: {model_path}")

            # UPDATED: Enhanced validation of loaded models
            if len(models) == 0:
                raise ValueError("❌ No models could be loaded - ensemble cannot be created")

            # Log loaded models summary
            self.logger.info(f"📊 LOADED MODELS SUMMARY:")
            for model_name, info in loaded_models_info.items():
                self.logger.info(f"   {model_name}: {info['type']} with {info['outputs']} outputs")

            # UPDATED: Enhanced ensemble configuration with loaded model info
            ensemble_config = {
                'lightgbm_config': {'role': 'signal_generator', 'weight': 0.40},
                'catboost_config': {'role': 'market_regime_analyst', 'weight': 0.25},
                'xgboost_config': {'role': 'risk_manager', 'weight': 0.25},
                'linear_config': {'role': 'stability_monitor', 'weight': 0.10},
                'loaded_models_info': loaded_models_info  # NEW: Include model info for debugging
            }

            # Initialize specialized ensemble
            self.logger.info("🔧 Initializing SpecializedEnsembleOrchestrator...")
            ensemble = SpecializedEnsembleOrchestrator(models, ensemble_config)

            self.logger.info(f"🎉 SPECIALIZED ENSEMBLE LOADED SUCCESSFULLY")
            self.logger.info(f"   Total models: {len(models)}")
            self.logger.info(f"   Available models: {list(models.keys())}")

            return ensemble

        except Exception as e:
            self.logger.error(f"❌ Failed to load specialized ensemble: {str(e)}")
            import traceback
            self.logger.error(f"❌ Ensemble loading traceback: {traceback.format_exc()}")
            raise

    def predict(self, features: pd.DataFrame) -> Dict[str, Any]:
        """
        Make ensemble prediction with enhanced confidence analysis.

        UPDATED: Enhanced prediction method for specialized ensemble architecture.

        Args:
            features: Feature DataFrame for prediction

        Returns:
            Dictionary with prediction results and metadata
        """
        if not self.is_loaded:
            if not self.load_models():
                return self._create_error_result("Models not loaded")

        try:
            start_time = time.time()

            self.logger.info("🚀 LIVE MODEL ENGINE PREDICTION STARTING")
            self.logger.info(f"📊 Input features shape: {features.shape}, columns: {len(features.columns)}")

            # UPDATED: Enhanced feature validation
            if features.shape[1] != self.expected_feature_count:
                self.logger.warning(f"⚠️ Feature count mismatch: {features.shape[1]} vs expected {self.expected_feature_count}")

            # UPDATED: Enhanced market data extraction
            market_data = self._extract_market_data(features)

            # UPDATED: Enhanced feature alignment with validation
            features_aligned = self.feature_alignment.align_features(features, market_data)
            self.logger.info(f"📊 Aligned features shape: {features_aligned.shape}")

            # Validate alignment
            alignment_results = self.feature_alignment.validate_alignment(features_aligned)
            if not alignment_results['feature_count_match']:
                self.logger.warning(f"⚠️ Feature count mismatch after alignment: {features_aligned.shape[1]} vs {self.expected_feature_count} expected")

            features_processed = features_aligned

            # UPDATED: Enhanced ensemble prediction with better error handling
            self.logger.info("🤖 CALLING ENSEMBLE MODEL PREDICTION...")
            prediction_dict = self._make_ensemble_prediction(features_processed)

            # Calculate prediction time
            prediction_time_ms = (time.time() - start_time) * 1000

            # UPDATED: Enhanced prediction analysis
            analysis = self._analyze_prediction(prediction_dict)

            # UPDATED: Enhanced trading signal generation
            trading_signal = self._generate_trading_signal(prediction_dict, analysis)

            # UPDATED: Enhanced statistics tracking
            self._update_prediction_stats(prediction_dict, analysis, prediction_time_ms)

            # Store prediction history
            prediction_record = {
                'timestamp': datetime.now(),
                'predictions': prediction_dict,
                'analysis': analysis,
                'trading_signal': trading_signal,
                'prediction_time_ms': prediction_time_ms
            }
            self.prediction_history.append(prediction_record)

            self.logger.info(f"🎯 PREDICTION COMPLETE: signal_prob={prediction_dict.get('signal_probability', 0):.3f}, "
                           f"tp1={prediction_dict.get('tp1_distance', 0):.1f}, "
                           f"tp2={prediction_dict.get('tp2_distance', 0):.1f}, "
                           f"sl={prediction_dict.get('sl_distance', 0):.1f}")

            return {
                'success': True,
                'predictions': prediction_dict,
                'analysis': analysis,
                'trading_signal': trading_signal,
                'metadata': {
                    'prediction_time_ms': prediction_time_ms,
                    'timestamp': datetime.now().isoformat(),
                    'model_count': self._get_model_count(),
                    'feature_count': features_processed.shape[1],
                    'ensemble_type': 'specialized' if self.use_specialized_ensemble else 'single'
                }
            }

        except Exception as e:
            self.logger.error(f"❌ Prediction failed: {str(e)}")
            import traceback
            self.logger.error(f"❌ Prediction traceback: {traceback.format_exc()}")
            self.model_stats['errors'] += 1
            return self._create_error_result(f"Prediction error: {str(e)}")
    
    def _analyze_prediction(self, predictions: Dict[str, float]) -> Dict[str, Any]:
        """Analyze prediction quality and confidence."""
        signal_prob = predictions.get('signal_probability', 0.0)
        
        # Confidence classification
        if signal_prob >= self.high_confidence_threshold:
            confidence_level = 'high'
        elif signal_prob >= self.min_confidence:
            confidence_level = 'medium'
        else:
            confidence_level = 'low'
        
        # Market regime analysis
        market_regime = predictions.get('market_regime', 0.0)
        regime_name = self._interpret_market_regime(market_regime)
        
        # Volatility adjustment analysis
        vol_adjustment = predictions.get('volatility_adjustment', 1.0)
        volatility_level = 'high' if vol_adjustment > 1.2 else 'low' if vol_adjustment < 0.8 else 'normal'
        
        return {
            'confidence_level': confidence_level,
            'signal_strength': signal_prob,
            'market_regime': regime_name,
            'volatility_level': volatility_level,
            'vol_adjustment_factor': vol_adjustment,
            'should_trade': signal_prob >= self.min_confidence,
            'high_confidence': signal_prob >= self.high_confidence_threshold
        }
    
    def _generate_trading_signal(self, predictions: Dict[str, float], 
                                analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive trading signal."""
        if not analysis['should_trade']:
            return {
                'should_enter': False,
                'reason': 'Low confidence signal',
                'confidence': predictions.get('signal_probability', 0.0)
            }
        
        # Determine direction with symmetric logic
        signal_prob = predictions.get('signal_probability', 0.0)
        if signal_prob > 0.5:
            direction = 'long'
        elif signal_prob < 0.5:
            direction = 'short'
        else:
            # Exactly 0.5 - use a small random factor to avoid bias
            direction = 'long' if np.random.random() > 0.5 else 'short'
        
        # Model-driven TP/SL levels
        tp1_distance = predictions.get('tp1_distance', 15.0)
        tp2_distance = predictions.get('tp2_distance', 30.0)
        sl_distance = predictions.get('sl_distance', 20.0)
        
        # Apply volatility adjustment
        vol_adjustment = predictions.get('volatility_adjustment', 1.0)
        tp1_distance *= vol_adjustment
        tp2_distance *= vol_adjustment
        sl_distance *= vol_adjustment
        
        # Position sizing based on confidence
        position_size_category = predictions.get('position_size_category', 1.0)
        confidence_multiplier = 1.0
        if analysis['high_confidence']:
            confidence_multiplier = 1.5
        elif analysis['confidence_level'] == 'low':
            confidence_multiplier = 0.7
        
        return {
            'should_enter': True,
            'direction': direction,
            'confidence': signal_prob,
            'tp1_distance': tp1_distance,
            'tp2_distance': tp2_distance,
            'sl_distance': sl_distance,
            'hold_time_estimate': predictions.get('hold_time_estimate', 60.0),
            'position_size_multiplier': confidence_multiplier * position_size_category,
            'market_regime': analysis['market_regime'],
            'volatility_adjustment': vol_adjustment,
            'entry_urgency': 'high' if analysis['high_confidence'] else 'normal'
        }
    
    def _interpret_market_regime(self, regime_value: float) -> str:
        """Interpret market regime from model output."""
        if regime_value < 0.25:
            return 'trending_down'
        elif regime_value < 0.5:
            return 'ranging_low'
        elif regime_value < 0.75:
            return 'ranging_high'
        else:
            return 'trending_up'
    
    def _update_prediction_stats(self, predictions: Dict[str, float], 
                                analysis: Dict[str, Any], prediction_time_ms: float):
        """Update prediction statistics."""
        self.model_stats['total_predictions'] += 1
        
        if analysis['high_confidence']:
            self.model_stats['high_confidence_predictions'] += 1
        
        if analysis['should_trade']:
            self.model_stats['trading_signals_generated'] += 1
        
        # Update averages
        signal_prob = predictions.get('signal_probability', 0.0)
        total_preds = self.model_stats['total_predictions']
        
        self.model_stats['avg_confidence'] = (
            (self.model_stats['avg_confidence'] * (total_preds - 1) + signal_prob) / total_preds
        )
        
        self.model_stats['avg_prediction_time_ms'] = (
            (self.model_stats['avg_prediction_time_ms'] * (total_preds - 1) + prediction_time_ms) / total_preds
        )
        
        self.model_stats['last_prediction_time'] = datetime.now()
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """Create error result structure."""
        return {
            'success': False,
            'error': error_message,
            'predictions': {},
            'analysis': {'should_trade': False, 'confidence_level': 'error'},
            'trading_signal': {'should_enter': False, 'reason': error_message},
            'metadata': {'timestamp': datetime.now().isoformat()}
        }
    
    def get_model_performance(self) -> Dict[str, Any]:
        """Get model performance statistics."""
        total_preds = self.model_stats['total_predictions']
        
        performance = {
            **self.model_stats,
            'high_confidence_rate': (
                self.model_stats['high_confidence_predictions'] / max(total_preds, 1)
            ),
            'signal_generation_rate': (
                self.model_stats['trading_signals_generated'] / max(total_preds, 1)
            ),
            'prediction_history_size': len(self.prediction_history),
            'is_loaded': self.is_loaded
        }
        
        # Recent performance (last 100 predictions)
        if len(self.prediction_history) > 0:
            recent_predictions = list(self.prediction_history)[-100:]
            recent_confidences = [
                p['predictions'].get('signal_probability', 0.0) 
                for p in recent_predictions
            ]
            
            if recent_confidences:
                performance['recent_avg_confidence'] = np.mean(recent_confidences)
                performance['recent_confidence_std'] = np.std(recent_confidences)
        
        return performance
    
    def get_recent_predictions(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recent prediction history."""
        return list(self.prediction_history)[-count:]
    
    def _convert_ensemble_decision_to_predictions(self, decision_result) -> Dict[str, float]:
        """Convert EnsembleDecision to prediction dictionary format."""
        try:
            # Extract values from EnsembleDecision object
            prediction_dict = {
                'signal_probability': getattr(decision_result, 'confidence', 0.5),
                'tp1_distance': getattr(decision_result, 'tp1_distance', 15.0),
                'tp2_distance': getattr(decision_result, 'tp2_distance', 30.0),
                'sl_distance': getattr(decision_result, 'sl_distance', 20.0),
                'hold_time_estimate': getattr(decision_result, 'hold_time_estimate', 60.0),
                'volatility_adjustment': getattr(decision_result, 'volatility_adjustment', 1.0),
                'market_regime': getattr(decision_result, 'market_regime', 0.5),
                'position_size_category': getattr(decision_result, 'position_size_multiplier', 1.0)
            }

            self.logger.info(f"🔄 Converted EnsembleDecision: {prediction_dict}")
            return prediction_dict

        except Exception as e:
            self.logger.error(f"❌ Failed to convert EnsembleDecision: {str(e)}")
            # Return safe defaults
            return {
                'signal_probability': 0.5,
                'tp1_distance': 15.0,
                'tp2_distance': 30.0,
                'sl_distance': 20.0,
                'hold_time_estimate': 60.0,
                'volatility_adjustment': 1.0,
                'market_regime': 0.5,
                'position_size_category': 1.0
            }

    def reset_stats(self):
        """Reset performance statistics."""
        self.model_stats = {
            'total_predictions': 0,
            'high_confidence_predictions': 0,
            'trading_signals_generated': 0,
            'avg_prediction_time_ms': 0.0,
            'avg_confidence': 0.0,
            'model_consensus_rate': 0.0,
            'last_prediction_time': None,
            'errors': 0
        }
        self.prediction_history.clear()
        self.logger.info("Model statistics reset")

    def _validate_ensemble_interface(self) -> bool:
        """
        Validate that the ensemble model has the required interface.

        NEW: Validation method for ensemble model interface.
        """
        try:
            if self.ensemble_model is None:
                self.logger.error("❌ Ensemble model is None")
                return False

            # Check for required methods
            required_methods = ['predict']
            for method in required_methods:
                if not hasattr(self.ensemble_model, method):
                    self.logger.error(f"❌ Ensemble model missing required method: {method}")
                    return False

            # Check if it's a SpecializedEnsembleOrchestrator
            if hasattr(self.ensemble_model, 'signal_generator'):
                self.logger.info("✅ Detected SpecializedEnsembleOrchestrator interface")

                # Validate individual components
                components = ['signal_generator', 'regime_analyst', 'risk_manager', 'stability_monitor']
                available_components = []
                for component in components:
                    if hasattr(self.ensemble_model, component) and getattr(self.ensemble_model, component) is not None:
                        available_components.append(component)

                self.logger.info(f"✅ Available ensemble components: {available_components}")

                if len(available_components) < 2:
                    self.logger.warning("⚠️ Less than 2 ensemble components available - reduced functionality")

            else:
                self.logger.info("✅ Standard model interface detected")

            return True

        except Exception as e:
            self.logger.error(f"❌ Ensemble interface validation failed: {str(e)}")
            return False

    def _validate_feature_pipeline(self) -> bool:
        """
        Validate that the feature pipeline is compatible.

        NEW: Validation method for feature pipeline compatibility.
        """
        try:
            if self.feature_pipeline is None:
                self.logger.warning("⚠️ No feature pipeline loaded")
                return True  # Not critical

            # Check if pipeline has transform method
            if not hasattr(self.feature_pipeline, 'transform'):
                self.logger.warning("⚠️ Feature pipeline missing transform method")
                return False

            self.logger.info("✅ Feature pipeline interface validated")
            return True

        except Exception as e:
            self.logger.error(f"❌ Feature pipeline validation failed: {str(e)}")
            return False

    def _extract_market_data(self, features: pd.DataFrame) -> Optional[pd.DataFrame]:
        """
        Extract market data from features for OHLCV processing.

        NEW: Enhanced market data extraction method.
        """
        try:
            if hasattr(features, 'index') and len(features) > 0:
                # Create basic market data from features if available
                market_data_cols = {}
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in features.columns:
                        market_data_cols[col] = features[col]
                    elif f'price_{col}' in features.columns:
                        market_data_cols[col] = features[f'price_{col}']
                    elif f'{col}_price' in features.columns:
                        market_data_cols[col] = features[f'{col}_price']

                if market_data_cols:
                    market_data = pd.DataFrame(market_data_cols, index=features.index)
                    self.logger.debug(f"📊 Extracted market data: {list(market_data_cols.keys())}")
                    return market_data

            return None

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to extract market data: {str(e)}")
            return None

    def _make_ensemble_prediction(self, features: pd.DataFrame) -> Dict[str, float]:
        """
        Make prediction using the ensemble model with enhanced error handling.

        NEW: Enhanced ensemble prediction method.
        """
        try:
            if hasattr(self.ensemble_model, 'predict'):
                # SpecializedEnsembleOrchestrator.predict() interface
                self.logger.info("🔧 Using SpecializedEnsembleOrchestrator.predict() method")
                prediction_result = self.ensemble_model.predict(features)

                # Handle different prediction result formats
                if hasattr(prediction_result, 'values'):
                    # DataFrame format
                    predictions = prediction_result.values
                    prediction_dict = {}
                    for i, name in enumerate(self.output_names):
                        if i < predictions.shape[1]:
                            prediction_dict[name] = float(predictions[0, i])
                        else:
                            prediction_dict[name] = 0.0
                    self.logger.info(f"✅ Extracted predictions from DataFrame: {prediction_dict}")

                elif isinstance(prediction_result, dict):
                    # Direct dictionary format
                    prediction_dict = prediction_result
                    self.logger.info(f"✅ Using direct dictionary predictions: {prediction_dict}")

                elif isinstance(prediction_result, np.ndarray):
                    # NumPy array format
                    prediction_dict = {}
                    for i, name in enumerate(self.output_names):
                        if i < len(prediction_result):
                            prediction_dict[name] = float(prediction_result[i])
                        else:
                            prediction_dict[name] = 0.0
                    self.logger.info(f"✅ Extracted predictions from array: {prediction_dict}")

                else:
                    self.logger.error(f"❌ Unexpected prediction result type: {type(prediction_result)}")
                    raise ValueError(f"Unexpected prediction result type: {type(prediction_result)}")

            elif hasattr(self.ensemble_model, 'make_trading_decision'):
                # SpecializedEnsembleOrchestrator.make_trading_decision() interface
                self.logger.info("🔧 Using SpecializedEnsembleOrchestrator.make_trading_decision() method")
                decision_result = self.ensemble_model.make_trading_decision(features)

                # Convert EnsembleDecision to prediction format
                prediction_dict = self._convert_ensemble_decision_to_predictions(decision_result)
                self.logger.info(f"✅ Converted ensemble decision to predictions: {prediction_dict}")

            else:
                self.logger.error("❌ Ensemble model has no compatible prediction method")
                raise ValueError("Ensemble model has no compatible prediction method")

            return prediction_dict

        except Exception as e:
            self.logger.error(f"❌ Ensemble prediction failed: {str(e)}")
            raise

    def _get_model_count(self) -> int:
        """
        Get the number of models in the ensemble.

        NEW: Helper method to get model count.
        """
        try:
            if hasattr(self.ensemble_model, 'signal_generator'):
                # SpecializedEnsembleOrchestrator
                count = 0
                components = ['signal_generator', 'regime_analyst', 'risk_manager', 'stability_monitor']
                for component in components:
                    if hasattr(self.ensemble_model, component) and getattr(self.ensemble_model, component) is not None:
                        count += 1
                return count
            elif hasattr(self.ensemble_model, 'models'):
                # Dictionary of models
                return len(self.ensemble_model.models)
            elif hasattr(self.ensemble_model, 'working_models'):
                # Alternative model storage
                return len(self.ensemble_model.working_models)
            else:
                return 1  # Single model

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to get model count: {str(e)}")
            return 1
