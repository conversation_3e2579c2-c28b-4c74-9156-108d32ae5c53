"""
Base Feature Engineering Classes

Provides abstract base classes and interfaces for all feature engineering components
following the established architecture patterns in the XAUUSD trading system.
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from pathlib import Path

# Import existing system components
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from data_collection.error_handling.logger import LoggerMixin, get_logger, log_execution_time
from data_collection.config import Config


@dataclass
class FeatureResult:
    """
    Container for feature engineering results.
    
    Attributes:
        feature_type: Type of features generated
        features: DataFrame containing the generated features
        feature_names: List of feature column names
        feature_metadata: Metadata about each feature
        statistics: Statistical summary of features
        processing_info: Information about processing steps
        warnings: List of warnings during processing
        errors: List of errors encountered
    """
    feature_type: str
    features: pd.DataFrame
    feature_names: List[str]
    feature_metadata: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    statistics: Dict[str, Any] = field(default_factory=dict)
    processing_info: Dict[str, Any] = field(default_factory=dict)
    warnings: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Validate the result after initialization."""
        if self.features is not None and not self.features.empty:
            # Ensure feature_names matches DataFrame columns
            if not self.feature_names:
                self.feature_names = list(self.features.columns)
            
            # Generate basic statistics if not provided
            if not self.statistics:
                self.statistics = self._generate_basic_statistics()
    
    def _generate_basic_statistics(self) -> Dict[str, Any]:
        """Generate basic statistics for the features."""
        if self.features is None or self.features.empty:
            return {}
        
        return {
            'total_features': len(self.feature_names),
            'total_records': len(self.features),
            'missing_values': self.features.isnull().sum().to_dict(),
            'feature_ranges': {
                col: {'min': self.features[col].min(), 'max': self.features[col].max()}
                for col in self.features.select_dtypes(include=[np.number]).columns
            },
            'generation_timestamp': datetime.now().isoformat()
        }


class FeatureEngineInterface(ABC):
    """Interface for all feature engineering components."""
    
    @abstractmethod
    def generate_features(self, data: pd.DataFrame, **kwargs) -> FeatureResult:
        """
        Generate features from the input data.
        
        Args:
            data: Input price data with OHLCV columns
            **kwargs: Additional parameters for feature generation
            
        Returns:
            FeatureResult containing generated features and metadata
        """
        pass
    
    @abstractmethod
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        pass
    
    @abstractmethod
    def get_required_columns(self) -> List[str]:
        """Get list of required columns in input data."""
        pass
    
    @abstractmethod
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        pass


class BaseFeatureEngine(FeatureEngineInterface):
    """
    Base class for all feature engineering components.

    Provides common functionality including validation, error handling,
    and integration with the existing logging system.
    """

    def __init__(self, config: Config):
        """
        Initialize base feature engine.

        Args:
            config: Configuration object from the main system
        """
        self.config = config
        self._logger = get_logger(self.__class__.__name__)
        self.feature_config = self._get_feature_config()

        # Initialize feature metadata
        self.feature_metadata = {}
        self.processing_stats = {}

    @property
    def logger(self):
        """Get logger instance."""
        return self._logger
    
    def _get_feature_config(self) -> Dict[str, Any]:
        """Get feature-specific configuration."""
        # Try to get feature config from main config, fallback to defaults
        return getattr(self.config, 'feature_engineering', {})
    
    @log_execution_time
    def generate_features(self, data: pd.DataFrame, **kwargs) -> FeatureResult:
        """
        Generate features with comprehensive validation and error handling.
        
        Args:
            data: Input price data
            **kwargs: Additional parameters
            
        Returns:
            FeatureResult with generated features
        """
        self.logger.info(f"Starting {self.get_feature_type()} feature generation")
        
        try:
            # Validate input data
            self._validate_input_data(data)
            
            # Perform feature generation
            features_df = self._generate_features_impl(data, **kwargs)
            
            # Validate generated features
            self._validate_generated_features(features_df)
            
            # Generate metadata
            metadata = self._generate_feature_metadata(features_df, data)
            
            # Create result
            result = FeatureResult(
                feature_type=self.get_feature_type(),
                features=features_df,
                feature_names=list(features_df.columns),
                feature_metadata=metadata,
                processing_info=self.processing_stats
            )
            
            self.logger.info(f"Generated {len(result.feature_names)} {self.get_feature_type()} features")
            return result
            
        except Exception as e:
            self.logger.error(f"Error generating {self.get_feature_type()} features: {str(e)}")
            # Return empty result with error information
            return FeatureResult(
                feature_type=self.get_feature_type(),
                features=pd.DataFrame(),
                feature_names=[],
                errors=[str(e)]
            )
    
    def _validate_input_data(self, data: pd.DataFrame) -> None:
        """
        Validate input data format and required columns.
        
        Args:
            data: Input data to validate
            
        Raises:
            ValueError: If data validation fails
        """
        if data is None or data.empty:
            raise ValueError("Input data is empty or None")
        
        required_columns = self.get_required_columns()
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Check for datetime index or column
        if not isinstance(data.index, pd.DatetimeIndex) and 'datetime' not in data.columns:
            raise ValueError("Data must have datetime index or 'datetime' column")
        
        self.logger.debug(f"Input data validation passed: {len(data)} records, {len(data.columns)} columns")
    
    def _validate_generated_features(self, features: pd.DataFrame) -> None:
        """
        Validate generated features.
        
        Args:
            features: Generated features to validate
            
        Raises:
            ValueError: If feature validation fails
        """
        if features is None or features.empty:
            raise ValueError("No features were generated")
        
        # Check for infinite or NaN values
        inf_cols = features.columns[features.isin([np.inf, -np.inf]).any()].tolist()
        if inf_cols:
            self.logger.warning(f"Infinite values found in features: {inf_cols}")
        
        # Log feature statistics with smart NaN analysis
        nan_counts = features.isnull().sum()
        if nan_counts.sum() > 0:
            unexpected_nan = self._identify_unexpected_nan(nan_counts)
            if unexpected_nan:
                self.logger.warning(f"Unexpected NaN values in features: {unexpected_nan}")
            else:
                self.logger.debug(f"Expected NaN values in features: {nan_counts[nan_counts > 0].to_dict()}")
    
    def _identify_unexpected_nan(self, nan_counts: pd.Series) -> Dict[str, int]:
        """
        Identify NaN values that are unexpected (vs mathematically expected).

        Expected NaN patterns:
        - return_simple_N: N NaN values (need N previous values)
        - rolling_mean_N: N-1 NaN values (need N values for first calculation)
        - momentum indicators: period-1 NaN values
        - correlation features: window-1 NaN values
        """
        unexpected_nan = {}

        for feature_name, nan_count in nan_counts[nan_counts > 0].items():
            expected_nan = self._get_expected_nan_count(feature_name)

            # Allow flexible tolerance for expected NaN
            if expected_nan is None:
                # Unknown pattern - only flag if excessive (>100 NaN)
                if nan_count > 100:
                    unexpected_nan[feature_name] = nan_count
            else:
                # Known pattern - allow reasonable tolerance
                tolerance = max(2, int(expected_nan * 0.1))  # At least 2, or 10% of expected
                if nan_count > expected_nan + tolerance:
                    unexpected_nan[feature_name] = nan_count

        return unexpected_nan

    def _get_expected_nan_count(self, feature_name: str) -> Optional[int]:
        """Get expected NaN count for a feature based on its name pattern."""
        import re

        # Return features: return_simple_N should have N NaN
        if match := re.search(r'return_(?:simple|log)_(\d+)', feature_name):
            return int(match.group(1))

        # Rolling statistics: close_mean_N should have N-1 NaN
        if match := re.search(r'(?:close|high|low)_(?:mean|std|min|max|skew|kurt)_(\d+)', feature_name):
            return int(match.group(1)) - 1

        # Volatility features - comprehensive patterns
        if match := re.search(r'volatility_simple_(\d+)', feature_name):
            return int(match.group(1))
        if match := re.search(r'volatility_ewma_(\d+)', feature_name):
            return 2  # EWMA typically needs 2-3 periods for stability
        if match := re.search(r'volatility_(?:parkinson|garman_klass)_(\d+)', feature_name):
            return int(match.group(1)) - 1  # Need N-1 for calculation

        # ATR features: atr_N should have N NaN
        if match := re.search(r'atr_(\d+)(?:_|$)', feature_name):
            return int(match.group(1))

        # ATR percentile features need large lookback
        if match := re.search(r'atr_(\d+)_percentile', feature_name):
            return 100  # Observed in logs: exactly 100 NaN for all ATR percentile features

        # Bollinger Bands: bb_*_N_multiplier should have N-1 NaN (rolling window produces N-1 NaN)
        if match := re.search(r'bb_\w+_(\d+)_[\d.]+', feature_name):
            return int(match.group(1)) - 1  # rolling(N) produces N-1 NaN values

        # RSI momentum: should have 1 NaN (diff operation)
        if 'rsi_' in feature_name and '_momentum' in feature_name:
            return 1

        # Stochastic: stoch_k_N should have N+smooth-1 NaN
        if match := re.search(r'stoch_k_(\d+)', feature_name):
            return int(match.group(1)) + 2  # period + smooth_k - 1

        # Stochastic D: stoch_d_N should have N+smooth_k+smooth_d-1 NaN
        if match := re.search(r'stoch_d_(\d+)', feature_name):
            return int(match.group(1)) + 4  # period + smooth_k + smooth_d - 1

        # Williams %R: williams_r_N should have N-1 NaN
        if match := re.search(r'williams_r_(\d+)', feature_name):
            return int(match.group(1)) - 1

        # VWAP and VWAP deviation: vwap_N should have N-1 NaN
        if match := re.search(r'vwap(?:_deviation)?_(\d+)', feature_name):
            return int(match.group(1)) - 1

        # Volume features - comprehensive patterns
        if match := re.search(r'(?:obv_sma|volume_sma)_(\d+)', feature_name):
            return int(match.group(1)) - 1
        if match := re.search(r'(?:volume_ratio|volume_trend)_(\d+)', feature_name):
            return int(match.group(1)) - 1
        if feature_name == 'obv_momentum':
            return 5  # Typically 5-period momentum
        if feature_name == 'volume_z_score':
            return 19  # Typically 20-period z-score
        if match := re.search(r'(?:price_volume_corr|volume_price_divergence)_(\d+)', feature_name):
            return int(match.group(1))

        # Correlation features - more comprehensive patterns with alignment tolerance
        if match := re.search(r'corr_(\w+)_(\d+)(?:_rank|_change)?$', feature_name):
            asset = match.group(1)
            base_period = int(match.group(2))

            # VIX has more alignment issues than other assets
            vix_multiplier = 1.5 if asset == 'vix' else 1.0

            if '_rank' in feature_name:
                # Rank uses min(window, 50) + alignment issues
                expected = min(base_period, 50) + int(base_period * vix_multiplier)
                return min(expected, 100)  # Cap at 100 to be reasonable
            elif '_change' in feature_name:
                # Change calculation needs base + change period + alignment buffer
                return base_period + int(25 * vix_multiplier)  # More tolerance for VIX
            else:
                # Basic correlation + alignment buffer for external data issues
                return base_period + int(25 * vix_multiplier)  # Allow for data alignment problems

        # Cross-asset divergence features
        if match := re.search(r'(?:price_corr_divergence|momentum_corr_divergence)_\w+', feature_name):
            return 75  # Typically needs substantial lookback + alignment buffer

        # Aggregate correlation features
        if feature_name in ['avg_correlation', 'correlation_dispersion']:
            return 50  # Needs substantial data for stability
        if feature_name == 'correlation_momentum':
            return 55  # Momentum of correlation

        # Regime detection features
        if match := re.search(r'vol_regime_strength_(\d+)', feature_name):
            return 100  # Regime strength needs large lookback
        if match := re.search(r'trend_(?:strength|consistency)_(\d+)', feature_name):
            return int(match.group(1)) - 1
        if feature_name == 'regime_momentum':
            return 19  # Typically 20-period momentum

        # Volatility expansion features
        if match := re.search(r'vol_expansion_(\d+)', feature_name):
            return int(match.group(1)) + 5  # Base period + expansion lookback

        # Price features - comprehensive patterns
        if feature_name == 'gap_open_pct':
            return 1  # Needs previous close
        if feature_name == 'price_acceleration':
            return 2  # Second derivative needs 2 periods
        if match := re.search(r'roc_(\d+)', feature_name):
            return int(match.group(1))  # Rate of change over N periods
        if match := re.search(r'momentum_(\d+)_(\d+)', feature_name):
            return int(match.group(1)) - 1  # Momentum calculation

        # Volume percentile: needs large window
        if feature_name == 'volume_percentile':
            return 99  # Typically uses 100-period window

        # Price position in range: needs large window
        if feature_name == 'price_position_in_range':
            return 99  # Typically uses 100-period window

        # Daily range percentage: needs daily data
        if feature_name == 'daily_range_pct':
            return 99  # Needs historical daily data

        # Seasonal features
        if 'seasonal_momentum_weekly' in feature_name:
            return 99  # Needs weekly data
        if 'seasonal_strength' in feature_name:
            return 1  # Basic seasonal calculation
        if 'seasonal_momentum_daily' in feature_name:
            return 24  # Daily momentum
        if 'seasonal_volatility' in feature_name:
            return 20  # Volatility calculation

        # Time momentum features
        if feature_name in ['time_momentum', 'time_mean_reversion']:
            return 47  # Typically 48-period calculation

        # Default: unknown pattern, allow any NaN count
        return None

    def _generate_feature_metadata(self, features: pd.DataFrame, original_data: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """
        Generate metadata for each feature.
        
        Args:
            features: Generated features
            original_data: Original input data
            
        Returns:
            Dictionary with metadata for each feature
        """
        metadata = {}
        
        for col in features.columns:
            metadata[col] = {
                'feature_type': self.get_feature_type(),
                'data_type': str(features[col].dtype),
                'non_null_count': features[col].count(),
                'null_percentage': (features[col].isnull().sum() / len(features)) * 100,
                'generation_method': self.__class__.__name__,
                'generation_timestamp': datetime.now().isoformat()
            }
            
            # Add statistical info for numeric features
            if features[col].dtype in ['int64', 'float64']:
                metadata[col].update({
                    'min_value': features[col].min(),
                    'max_value': features[col].max(),
                    'mean_value': features[col].mean(),
                    'std_value': features[col].std()
                })
        
        return metadata
    
    @abstractmethod
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated features
        """
        pass
    
    def get_required_columns(self) -> List[str]:
        """Default required columns for OHLCV data."""
        return ['open', 'high', 'low', 'close']
    
    def _ensure_datetime_index(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Ensure data has datetime index.
        
        Args:
            data: Input data
            
        Returns:
            Data with datetime index
        """
        if not isinstance(data.index, pd.DatetimeIndex):
            if 'datetime' in data.columns:
                data = data.set_index('datetime')
            else:
                raise ValueError("Cannot create datetime index from available columns")
        
        return data
    
    def _calculate_returns(self, prices: pd.Series, periods: int = 1, method: str = 'simple') -> pd.Series:
        """
        Calculate returns for a price series.
        
        Args:
            prices: Price series
            periods: Number of periods for return calculation
            method: 'simple' or 'log' returns
            
        Returns:
            Returns series
        """
        if method == 'simple':
            return prices.pct_change(periods=periods)
        elif method == 'log':
            return np.log(prices / prices.shift(periods))
        else:
            raise ValueError(f"Unknown return method: {method}")
