"""
Temporal Pattern Features Engine

Generates comprehensive temporal pattern features including:
- Cyclical time features (sine/cosine transformations)
- Market regime temporal patterns
- Seasonal effects and calendar anomalies
- Time-based momentum and mean reversion patterns
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, time
import calendar

# Import base classes
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))
from features.core.base_feature import BaseFeatureEngine, FeatureResult
from features.config.feature_config import FeatureConfig
from data_collection.config import Config


class TemporalPatternFeatures(BaseFeatureEngine):
    """
    Temporal pattern features engine.
    
    Generates comprehensive temporal pattern features for XAUUSD trading
    including cyclical patterns, seasonal effects, and time-based anomalies.
    """
    
    def __init__(self, config: Config, feature_config: FeatureConfig, **kwargs):
        """
        Initialize temporal pattern features engine.
        
        Args:
            config: Main system configuration
            feature_config: Feature-specific configuration
            **kwargs: Additional parameters
        """
        super().__init__(config)
        self.feature_config = feature_config
        self.temporal_config = feature_config.context_features.get('temporal_patterns', {})
        
        # Cyclical feature configuration
        self.cyclical_features = self.temporal_config.get('cyclical_features', True)
        
        # Seasonal analysis configuration
        self.seasonal_analysis = self.temporal_config.get('seasonal_analysis', True)
        
        # Market anomalies configuration
        self.market_anomalies = self.temporal_config.get('market_anomalies', True)
    
    def get_feature_type(self) -> str:
        """Get the type of features this engine generates."""
        return "temporal_patterns"
    
    def get_required_columns(self) -> List[str]:
        """Get required columns for temporal analysis."""
        return ['open', 'high', 'low', 'close']
    
    def get_feature_names(self) -> List[str]:
        """Get list of feature names this engine generates."""
        feature_names = []
        
        # Cyclical features
        if self.cyclical_features:
            feature_names.extend([
                'hour_sin', 'hour_cos',
                'day_sin', 'day_cos',
                'week_sin', 'week_cos',
                'month_sin', 'month_cos',
                'quarter_sin', 'quarter_cos'
            ])
        
        # Seasonal patterns
        if self.seasonal_analysis:
            feature_names.extend([
                'seasonal_strength_daily',
                'seasonal_strength_weekly',
                'seasonal_strength_monthly',
                'seasonal_momentum_daily',
                'seasonal_momentum_weekly',
                'seasonal_volatility_daily',
                'seasonal_volatility_weekly'
            ])
        
        # Market anomalies
        if self.market_anomalies:
            feature_names.extend([
                'monday_effect',
                'friday_effect',
                'month_end_effect',
                'quarter_end_effect',
                'year_end_effect',
                'first_trading_day',
                'last_trading_day',
                'mid_month_effect'
            ])
        
        # Time-based patterns
        feature_names.extend([
            'intraday_pattern',
            'weekly_pattern',
            'monthly_pattern',
            'time_momentum',
            'time_mean_reversion'
        ])
        
        return feature_names
    
    def _generate_features_impl(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """
        Implementation-specific feature generation logic.
        
        Args:
            data: Input OHLCV data
            **kwargs: Additional parameters
            
        Returns:
            DataFrame with generated temporal pattern features
        """
        # Ensure datetime index
        data = self._ensure_datetime_index(data)
        
        # Initialize features DataFrame
        features = pd.DataFrame(index=data.index)
        
        # Generate cyclical features
        if self.cyclical_features:
            cyclical_features = self._generate_cyclical_features(data)
            features = pd.concat([features, cyclical_features], axis=1)
        
        # Generate seasonal pattern features
        if self.seasonal_analysis:
            seasonal_features = self._generate_seasonal_features(data)
            features = pd.concat([features, seasonal_features], axis=1)
        
        # Generate market anomaly features
        if self.market_anomalies:
            anomaly_features = self._generate_anomaly_features(data)
            features = pd.concat([features, anomaly_features], axis=1)
        
        # Generate time-based pattern features
        pattern_features = self._generate_pattern_features(data)
        features = pd.concat([features, pattern_features], axis=1)
        
        self.logger.info(f"Generated {len(features.columns)} temporal pattern features")
        return features
    
    def _generate_cyclical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate cyclical time features using sine/cosine transformations."""
        cyclical_df = pd.DataFrame(index=data.index)
        
        try:
            # Hour of day (0-23)
            hour = data.index.hour
            cyclical_df['hour_sin'] = np.sin(2 * np.pi * hour / 24)
            cyclical_df['hour_cos'] = np.cos(2 * np.pi * hour / 24)
            
            # Day of week (0-6)
            day_of_week = data.index.dayofweek
            cyclical_df['day_sin'] = np.sin(2 * np.pi * day_of_week / 7)
            cyclical_df['day_cos'] = np.cos(2 * np.pi * day_of_week / 7)
            
            # Week of year (1-52)
            week_of_year = data.index.isocalendar().week
            cyclical_df['week_sin'] = np.sin(2 * np.pi * week_of_year / 52)
            cyclical_df['week_cos'] = np.cos(2 * np.pi * week_of_year / 52)
            
            # Month of year (1-12)
            month = data.index.month
            cyclical_df['month_sin'] = np.sin(2 * np.pi * month / 12)
            cyclical_df['month_cos'] = np.cos(2 * np.pi * month / 12)
            
            # Quarter of year (1-4)
            quarter = data.index.quarter
            cyclical_df['quarter_sin'] = np.sin(2 * np.pi * quarter / 4)
            cyclical_df['quarter_cos'] = np.cos(2 * np.pi * quarter / 4)
            
        except Exception as e:
            self.logger.warning(f"Error calculating cyclical features: {str(e)}")
        
        return cyclical_df
    
    def _generate_seasonal_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate seasonal pattern features."""
        seasonal_df = pd.DataFrame(index=data.index)
        
        try:
            # Calculate returns for seasonal analysis
            returns = data['close'].pct_change()
            
            # Daily seasonal strength (how much current hour deviates from daily average)
            hourly_returns = returns.groupby(data.index.hour).mean()
            current_hour_avg = data.index.hour.map(hourly_returns)
            seasonal_df['seasonal_strength_daily'] = abs(returns - current_hour_avg)
            
            # Weekly seasonal strength
            daily_returns = returns.groupby(data.index.dayofweek).mean()
            current_day_avg = data.index.dayofweek.map(daily_returns)
            seasonal_df['seasonal_strength_weekly'] = abs(returns - current_day_avg)
            
            # Monthly seasonal strength
            monthly_returns = returns.groupby(data.index.month).mean()
            current_month_avg = data.index.month.map(monthly_returns)
            seasonal_df['seasonal_strength_monthly'] = abs(returns - current_month_avg)
            
            # Seasonal momentum (rolling average of seasonal effects)
            seasonal_df['seasonal_momentum_daily'] = seasonal_df['seasonal_strength_daily'].rolling(24).mean()
            seasonal_df['seasonal_momentum_weekly'] = seasonal_df['seasonal_strength_weekly'].rolling(24*7).mean()
            
            # Seasonal volatility patterns
            volatility = returns.rolling(20).std()
            hourly_vol = volatility.groupby(data.index.hour).mean()
            current_hour_vol = data.index.hour.map(hourly_vol)
            seasonal_df['seasonal_volatility_daily'] = volatility / current_hour_vol
            
            daily_vol = volatility.groupby(data.index.dayofweek).mean()
            current_day_vol = data.index.dayofweek.map(daily_vol)
            seasonal_df['seasonal_volatility_weekly'] = volatility / current_day_vol
            
        except Exception as e:
            self.logger.warning(f"Error calculating seasonal features: {str(e)}")
        
        return seasonal_df
    
    def _generate_anomaly_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate market anomaly features."""
        anomaly_df = pd.DataFrame(index=data.index)
        
        try:
            # Monday effect (tendency for different behavior on Mondays)
            anomaly_df['monday_effect'] = (data.index.dayofweek == 0).astype(int)
            
            # Friday effect (tendency for different behavior on Fridays)
            anomaly_df['friday_effect'] = (data.index.dayofweek == 4).astype(int)
            
            # Month-end effect (last 3 trading days of month)
            month_end_dates = data.index + pd.offsets.MonthEnd(0)
            days_to_month_end = (month_end_dates - data.index).days
            anomaly_df['month_end_effect'] = (days_to_month_end <= 3).astype(int)
            
            # Quarter-end effect (last 5 trading days of quarter)
            quarter_end_dates = data.index + pd.offsets.QuarterEnd(0)
            days_to_quarter_end = (quarter_end_dates - data.index).days
            anomaly_df['quarter_end_effect'] = (days_to_quarter_end <= 5).astype(int)
            
            # Year-end effect (last 10 trading days of year)
            year_end_dates = data.index + pd.offsets.YearEnd(0)
            days_to_year_end = (year_end_dates - data.index).days
            anomaly_df['year_end_effect'] = (days_to_year_end <= 10).astype(int)
            
            # First trading day of month
            first_of_month = data.index.day <= 3
            anomaly_df['first_trading_day'] = first_of_month.astype(int)
            
            # Last trading day of month
            last_of_month = days_to_month_end <= 1
            anomaly_df['last_trading_day'] = last_of_month.astype(int)
            
            # Mid-month effect (days 10-20 of month)
            mid_month = (data.index.day >= 10) & (data.index.day <= 20)
            anomaly_df['mid_month_effect'] = mid_month.astype(int)
            
        except Exception as e:
            self.logger.warning(f"Error calculating anomaly features: {str(e)}")
        
        return anomaly_df
    
    def _generate_pattern_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate time-based pattern features."""
        pattern_df = pd.DataFrame(index=data.index)
        
        try:
            returns = data['close'].pct_change()
            
            # Intraday pattern (position within trading day)
            # Normalize hour to 0-1 scale within active trading hours
            hour_normalized = pd.Series((data.index.hour - 0) / 23, index=data.index)  # Convert to Series
            pattern_df['intraday_pattern'] = hour_normalized

            # Weekly pattern (position within trading week)
            # Monday=0, Friday=1 (excluding weekends)
            day_of_week = data.index.dayofweek
            weekly_pattern = pd.Series(day_of_week / 6, index=data.index)  # Convert to Series
            pattern_df['weekly_pattern'] = weekly_pattern

            # Monthly pattern (position within month)
            day_of_month = data.index.day
            days_in_month = data.index.map(lambda x: calendar.monthrange(x.year, x.month)[1])
            monthly_pattern = pd.Series((day_of_month - 1) / (days_in_month - 1), index=data.index)  # Convert to Series
            pattern_df['monthly_pattern'] = monthly_pattern

            # Time momentum (tendency for patterns to persist)
            # Rolling correlation between time patterns and returns
            time_momentum = returns.rolling(48).corr(hour_normalized)  # Now both are Series
            pattern_df['time_momentum'] = time_momentum

            # Time mean reversion (tendency for patterns to reverse)
            # Negative correlation indicates mean reversion
            time_mean_reversion = -returns.rolling(48).corr(1 - hour_normalized)  # Now both are Series
            pattern_df['time_mean_reversion'] = time_mean_reversion
            
        except Exception as e:
            self.logger.warning(f"Error calculating pattern features: {str(e)}")
        
        return pattern_df
