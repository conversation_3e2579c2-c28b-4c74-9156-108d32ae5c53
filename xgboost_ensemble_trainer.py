#!/usr/bin/env python3
"""
XGBoost Ensemble Trainer for XAUUSD Hierarchical Decision-Making System
Level 4: Final Decision Integration

This trainer creates specialized XGBoost models for final decision integration,
focusing on risk management, feature attribution, and decision validation.
"""

import os
import sys
import json
import logging
import warnings
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path

import xgboost as xgb
import optuna
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, mean_squared_error, r2_score
from sklearn.preprocessing import LabelEncoder
# import matplotlib.pyplot as plt  # Removed to avoid circular import issues
# import seaborn as sns  # Removed to avoid circular import issues

# Suppress warnings
warnings.filterwarnings('ignore')
optuna.logging.set_verbosity(optuna.logging.WARNING)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class XGBoostEnsembleTrainer:
    """
    XGBoost Ensemble Trainer for Level 4: Final Decision Integration
    
    Specializes in:
    - Risk management integration
    - Feature importance analysis
    - Decision validation and confirmation
    - Non-linear relationship modeling
    """
    
    def __init__(self, data_path: str = "data/final_training_ready"):
        """Initialize XGBoost ensemble trainer"""
        self.data_path = Path(data_path)
        self.models = {}
        self.model_metadata = {}
        self.feature_columns = []
        self.label_encoders = {}
        
        # XGBoost model specifications for hierarchical system
        self.model_specs = {
            'signal_direction': {
                'type': 'classifier',
                'objective': 'multi:softprob',
                'eval_metric': 'mlogloss',
                'num_class': 3
            },
            'signal_probability': {
                'type': 'regressor', 
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            },
            'optimal_entry_long': {
                'type': 'regressor',
                'objective': 'reg:squarederror', 
                'eval_metric': 'rmse'
            },
            'optimal_entry_short': {
                'type': 'regressor',
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            },
            'tp1_long': {
                'type': 'regressor',
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            },
            'tp1_short': {
                'type': 'regressor', 
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            },
            'tp2_long': {
                'type': 'regressor',
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            },
            'tp2_short': {
                'type': 'regressor',
                'objective': 'reg:squarederror', 
                'eval_metric': 'rmse'
            },
            'sl_long': {
                'type': 'regressor',
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            },
            'sl_short': {
                'type': 'regressor',
                'objective': 'reg:squarederror',
                'eval_metric': 'rmse'
            }
        }
        
        logger.info("XGBoost Ensemble Trainer initialized for Final Decision Integration")
    
    def load_data(self) -> None:
        """Load and prepare training data"""
        logger.info("📊 LOADING TRAINING-READY DATASET...")
        
        # Find the latest training file
        train_files = list(self.data_path.glob("train_final_*.csv"))
        if not train_files:
            raise FileNotFoundError(f"No training files found in {self.data_path}")
        
        latest_file = max(train_files, key=lambda x: x.stat().st_mtime)
        
        # Load data
        self.data = pd.read_csv(latest_file)
        
        # Split data temporally (80% train, 10% val, 10% test)
        n_total = len(self.data)
        n_train = int(0.8 * n_total)
        n_val = int(0.9 * n_total)
        
        self.train_data = self.data.iloc[:n_train].copy()
        self.val_data = self.data.iloc[n_train:n_val].copy()
        self.test_data = self.data.iloc[n_val:].copy()
        
        # Identify feature columns (exclude labels and metadata)
        label_columns = list(self.model_specs.keys())
        metadata_columns = ['timestamp', 'datetime', 'date', 'time']
        
        self.feature_columns = [col for col in self.data.columns 
                               if col not in label_columns + metadata_columns]
        
        logger.info(f"✅ Loaded training data:")
        logger.info(f"   Train: {len(self.train_data):,} records")
        logger.info(f"   Validation: {len(self.val_data):,} records") 
        logger.info(f"   Test: {len(self.test_data):,} records")
        logger.info(f"   Features: {len(self.feature_columns)}")
        logger.info(f"   Labels: {len(label_columns)}")
    
    def _optimize_hyperparameters(self, model_name: str, X_train: pd.DataFrame,
                                 y_train: pd.Series, X_val: pd.DataFrame,
                                 y_val: pd.Series) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna"""
        
        def objective(trial):
            # XGBoost hyperparameter space
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                'reg_lambda': trial.suggest_float('reg_lambda', 1, 10),
                'early_stopping_rounds': 50,  # Add early stopping to model params
                'random_state': 42,
                'n_jobs': -1
            }
            
            # Add model-specific parameters
            params.update({
                'objective': self.model_specs[model_name]['objective'],
                'eval_metric': self.model_specs[model_name]['eval_metric']
            })
            
            if self.model_specs[model_name]['type'] == 'classifier':
                params['num_class'] = self.model_specs[model_name]['num_class']
            
            # Train model
            model = xgb.XGBClassifier(**params) if self.model_specs[model_name]['type'] == 'classifier' else xgb.XGBRegressor(**params)
            
            # XGBoost API change: early_stopping_rounds moved to model initialization
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                verbose=False
            )
            
            # Get validation score
            y_pred = model.predict(X_val)
            
            if self.model_specs[model_name]['type'] == 'classifier':
                return -accuracy_score(y_val, y_pred)  # Negative for minimization
            else:
                return mean_squared_error(y_val, y_pred)
        
        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=100, show_progress_bar=True)
        
        return study.best_params
    
    def train_ensemble(self) -> None:
        """Train all XGBoost ensemble models"""
        logger.info("🚀 STARTING XGBOOST ENSEMBLE TRAINING")
        logger.info("=" * 70)
        
        for model_name in self.model_specs.keys():
            logger.info(f"📊 Processing {model_name} ({self.model_specs[model_name]['type']})...")
            
            # Prepare data
            X_train = self.train_data[self.feature_columns].copy()
            X_val = self.val_data[self.feature_columns].copy()
            y_train = self.train_data[model_name].copy()
            y_val = self.val_data[model_name].copy()
            
            # Handle classification label encoding
            if self.model_specs[model_name]['type'] == 'classifier':
                le = LabelEncoder()
                y_train_encoded = le.fit_transform(y_train.dropna())
                y_val_encoded = le.transform(y_val.dropna())
                self.label_encoders[model_name] = le
                
                # Update training data
                valid_train_mask = ~pd.isna(y_train)
                valid_val_mask = ~pd.isna(y_val)
                X_train = X_train[valid_train_mask]
                X_val = X_val[valid_val_mask]
                y_train = pd.Series(y_train_encoded, index=X_train.index)
                y_val = pd.Series(y_val_encoded, index=X_val.index)
            else:
                # Remove invalid targets for regression
                valid_train_mask = ~(pd.isna(y_train) | np.isinf(y_train))
                valid_val_mask = ~(pd.isna(y_val) | np.isinf(y_val))
                
                X_train = X_train[valid_train_mask]
                X_val = X_val[valid_val_mask]
                y_train = y_train[valid_train_mask]
                y_val = y_val[valid_val_mask]
            
            logger.info(f"   Valid targets: {len(y_train):,}")
            
            # Optimize hyperparameters
            logger.info(f"🔧 OPTIMIZING HYPERPARAMETERS FOR {model_name.upper()}...")
            best_params = self._optimize_hyperparameters(
                model_name, X_train, y_train, X_val, y_val
            )
            
            logger.info(f"✅ Best parameters for {model_name}: {best_params}")
            
            # Train final model
            logger.info(f"🎯 TRAINING {model_name.upper()} MODEL...")
            start_time = datetime.now()
            
            # Create model with best parameters
            model_params = best_params.copy()
            model_params.update({
                'objective': self.model_specs[model_name]['objective'],
                'eval_metric': self.model_specs[model_name]['eval_metric'],
                'early_stopping_rounds': 50,  # Add early stopping to final model
                'random_state': 42,
                'n_jobs': -1
            })
            
            if self.model_specs[model_name]['type'] == 'classifier':
                model_params['num_class'] = self.model_specs[model_name]['num_class']
                model = xgb.XGBClassifier(**model_params)
            else:
                model = xgb.XGBRegressor(**model_params)
            
            # Train model (XGBoost API change: early_stopping_rounds moved to model initialization)
            model.fit(
                X_train, y_train,
                eval_set=[(X_val, y_val)],
                verbose=False
            )
            
            training_time = (datetime.now() - start_time).total_seconds()
            
            # Evaluate model
            y_pred = model.predict(X_val)
            
            if self.model_specs[model_name]['type'] == 'classifier':
                val_score = accuracy_score(y_val, y_pred)
                metric_name = "Accuracy"
            else:
                val_score = r2_score(y_val, y_pred)
                val_rmse = np.sqrt(mean_squared_error(y_val, y_pred))
                metric_name = "R²"
            
            # Store model and metadata
            self.models[model_name] = model
            self.model_metadata[model_name] = {
                'model_type': 'xgboost',
                'task_type': self.model_specs[model_name]['type'],
                'training_samples': len(X_train),
                'validation_samples': len(X_val),
                'training_time_seconds': training_time,
                'best_iteration': model.best_iteration,
                'hyperparameters': best_params,
                'validation_score': val_score
            }
            
            if self.model_specs[model_name]['type'] == 'regressor':
                self.model_metadata[model_name]['validation_rmse'] = val_rmse
            
            logger.info(f"✅ {model_name} training completed:")
            logger.info(f"   Training samples: {len(X_train):,}")
            logger.info(f"   Validation samples: {len(X_val):,}")
            logger.info(f"   Training time: {training_time:.2f}s")
            logger.info(f"   Best iteration: {model.best_iteration}")
            logger.info(f"   Validation {metric_name}: {val_score:.4f}")
            if self.model_specs[model_name]['type'] == 'regressor':
                logger.info(f"   Validation RMSE: {val_rmse:.6f}")
            logger.info(f"✅ {model_name} model trained successfully")
    
    def save_models(self) -> str:
        """Save trained models and metadata"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_dir = Path("models/xgboost_ensemble")
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # Save individual models
        for model_name, model in self.models.items():
            model_path = save_dir / f"{model_name}_{timestamp}.json"
            model.save_model(str(model_path))
        
        # Save label encoders
        if self.label_encoders:
            import pickle
            encoders_path = save_dir / f"label_encoders_{timestamp}.pkl"
            with open(encoders_path, 'wb') as f:
                pickle.dump(self.label_encoders, f)
        
        # Save metadata
        metadata = {
            'timestamp': timestamp,
            'model_type': 'xgboost_ensemble',
            'level': 'Level 4: Final Decision Integration',
            'total_models': len(self.models),
            'feature_count': len(self.feature_columns),
            'training_data_path': str(self.data_path),
            'models': self.model_metadata
        }
        
        metadata_path = save_dir / f"ensemble_metadata_{timestamp}.json"
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"✅ Models saved to: {save_dir}")
        logger.info(f"   Metadata: {metadata_path}")
        
        return str(save_dir)

def main():
    """Main training function"""
    logger.info("🎯 XGBOOST ENSEMBLE TRAINING FOR XAUUSD HIERARCHICAL SYSTEM")
    logger.info("🏛️ LEVEL 4: FINAL DECISION INTEGRATION")
    logger.info("=" * 80)
    
    try:
        # Initialize trainer
        trainer = XGBoostEnsembleTrainer()
        
        # Load data
        trainer.load_data()
        
        # Train ensemble
        trainer.train_ensemble()
        
        # Save models
        save_path = trainer.save_models()
        
        logger.info("🎉 XGBOOST ENSEMBLE TRAINING COMPLETED SUCCESSFULLY!")
        logger.info(f"📁 Models saved to: {save_path}")
        
    except Exception as e:
        logger.error(f"❌ Training failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
