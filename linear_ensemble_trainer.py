#!/usr/bin/env python3
"""
Linear Ensemble Trainer for XAUUSD Hierarchical Decision System
Level 1: Safety Check - Linear Models for Stability and Risk Management

This trainer creates 10 specialized linear models for the hierarchical decision framework:
- signal_direction (classifier): Logistic regression for directional signals
- signal_probability (regressor): Linear regression for confidence scores
- optimal_entry_long/short (regressor): Linear models for entry price optimization
- tp1_long/short, tp2_long/short (regressor): Linear models for take profit levels
- sl_long/short (regressor): Linear models for stop loss levels

Key Features:
- Regularized linear models (Ridge, Lasso, ElasticNet)
- Temporal validation with walk-forward analysis
- Comprehensive validation and performance assessment
- Integration with hierarchical decision framework
- Professional validation reports and model persistence

Author: AI Trading System
Date: 2025-09-23
"""

import os
import sys
import logging
import warnings
import time
import json
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path

import numpy as np
import pandas as pd
from sklearn.model_selection import TimeSeriesSplit
from sklearn.linear_model import LogisticRegression, <PERSON>, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, 
    mean_squared_error, r2_score, classification_report
)
import optuna
import joblib

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')
optuna.logging.set_verbosity(optuna.logging.WARNING)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LinearEnsembleTrainer:
    """
    Linear Ensemble Trainer for Level 1: Safety Check
    
    Specializes in:
    - Stability monitoring and risk assessment
    - Linear relationship modeling for interpretability
    - Safety checks and anomaly detection
    - Baseline performance benchmarking
    """
    
    def __init__(self, data_path: str = "data/final_training_ready"):
        """Initialize the Linear Ensemble Trainer."""
        
        self.data_path = data_path
        self.models = {}
        self.scalers = {}
        self.model_metadata = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Model specifications for ensemble
        self.model_specs = {
            'signal_direction': {
                'type': 'classifier',
                'target': 'signal_direction',
                'model_class': LogisticRegression,
                'description': 'Linear classification for directional signals'
            },
            'signal_probability': {
                'type': 'regressor', 
                'target': 'signal_probability',
                'model_class': Ridge,
                'description': 'Linear regression for signal confidence'
            },
            'optimal_entry_long': {
                'type': 'regressor',
                'target': 'optimal_entry_long', 
                'model_class': Ridge,
                'description': 'Linear model for long entry optimization'
            },
            'optimal_entry_short': {
                'type': 'regressor',
                'target': 'optimal_entry_short',
                'model_class': Ridge, 
                'description': 'Linear model for short entry optimization'
            },
            'tp1_long': {
                'type': 'regressor',
                'target': 'tp1_long',
                'model_class': Ridge,
                'description': 'Linear model for long TP1 levels'
            },
            'tp1_short': {
                'type': 'regressor', 
                'target': 'tp1_short',
                'model_class': Ridge,
                'description': 'Linear model for short TP1 levels'
            },
            'tp2_long': {
                'type': 'regressor',
                'target': 'tp2_long', 
                'model_class': Ridge,
                'description': 'Linear model for long TP2 levels'
            },
            'tp2_short': {
                'type': 'regressor',
                'target': 'tp2_short',
                'model_class': Ridge, 
                'description': 'Linear model for short TP2 levels'
            },
            'sl_long': {
                'type': 'regressor',
                'target': 'sl_long',
                'model_class': Ridge,
                'description': 'Linear model for long stop loss levels'
            },
            'sl_short': {
                'type': 'regressor',
                'target': 'sl_short', 
                'model_class': Ridge,
                'description': 'Linear model for short stop loss levels'
            }
        }
        
        logger.info("🎯 LINEAR ENSEMBLE TRAINING FOR XAUUSD HIERARCHICAL SYSTEM")
        logger.info("🛡️ LEVEL 1: SAFETY CHECK")
        logger.info("=" * 80)
        logger.info("Linear Ensemble Trainer initialized for Safety Check and Risk Management")
        
    def load_data(self) -> bool:
        """Load and prepare training data."""
        
        try:
            logger.info("📊 LOADING TRAINING-READY DATASET...")
            
            # Find the latest training file
            data_files = list(Path(self.data_path).glob("train_final_*.csv"))
            if not data_files:
                raise FileNotFoundError(f"No training files found in {self.data_path}")
            
            latest_file = max(data_files, key=lambda x: x.stat().st_mtime)
            logger.info(f"   Loading: {latest_file}")
            
            # Load data
            df = pd.read_csv(latest_file)
            
            # Sort by timestamp for temporal consistency
            if 'timestamp' in df.columns:
                df = df.sort_values('timestamp').reset_index(drop=True)
            
            # Split data temporally
            total_size = len(df)
            train_size = int(0.7 * total_size)
            val_size = int(0.15 * total_size)
            
            self.train_data = df.iloc[:train_size].copy()
            self.val_data = df.iloc[train_size:train_size + val_size].copy()
            self.test_data = df.iloc[train_size + val_size:].copy()
            
            # Identify feature columns (exclude targets and metadata)
            target_columns = [spec['target'] for spec in self.model_specs.values()]
            exclude_columns = target_columns + ['timestamp', 'datetime', 'close', 'open', 'high', 'low', 'volume']
            self.feature_columns = [col for col in df.columns if col not in exclude_columns]
            
            logger.info(f"✅ Loaded training data:")
            logger.info(f"   Train: {len(self.train_data):,} records")
            logger.info(f"   Validation: {len(self.val_data):,} records") 
            logger.info(f"   Test: {len(self.test_data):,} records")
            logger.info(f"   Features: {len(self.feature_columns)}")
            logger.info(f"   Labels: {len(target_columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load data: {e}")
            return False
    
    def optimize_hyperparameters(self, model_name: str, X_train: pd.DataFrame, 
                                y_train: pd.Series, X_val: pd.DataFrame, 
                                y_val: pd.Series) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna."""
        
        def objective(trial):
            spec = self.model_specs[model_name]
            model_class = spec['model_class']
            
            if spec['type'] == 'classifier':
                # Logistic Regression hyperparameters
                params = {
                    'C': trial.suggest_float('C', 0.01, 100.0, log=True),
                    'penalty': trial.suggest_categorical('penalty', ['l1', 'l2', 'elasticnet']),
                    'solver': 'saga',  # Supports all penalties
                    'max_iter': 1000,
                    'random_state': 42
                }
                
                if params['penalty'] == 'elasticnet':
                    params['l1_ratio'] = trial.suggest_float('l1_ratio', 0.0, 1.0)
                    
            else:
                # Regression hyperparameters
                reg_type = trial.suggest_categorical('reg_type', ['ridge', 'lasso', 'elasticnet'])
                
                if reg_type == 'ridge':
                    model_class = Ridge
                    params = {
                        'alpha': trial.suggest_float('alpha', 0.01, 100.0, log=True),
                        'random_state': 42
                    }
                elif reg_type == 'lasso':
                    model_class = Lasso
                    params = {
                        'alpha': trial.suggest_float('alpha', 0.01, 10.0, log=True),
                        'max_iter': 2000,
                        'random_state': 42
                    }
                else:  # elasticnet
                    model_class = ElasticNet
                    params = {
                        'alpha': trial.suggest_float('alpha', 0.01, 10.0, log=True),
                        'l1_ratio': trial.suggest_float('l1_ratio', 0.0, 1.0),
                        'max_iter': 2000,
                        'random_state': 42
                    }
            
            # Create and train model
            model = model_class(**params)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            model.fit(X_train_scaled, y_train)
            
            # Evaluate
            if spec['type'] == 'classifier':
                y_pred = model.predict(X_val_scaled)
                score = accuracy_score(y_val, y_pred)
                return score  # Maximize accuracy
            else:
                y_pred = model.predict(X_val_scaled)
                score = mean_squared_error(y_val, y_pred)
                return score  # Minimize MSE
        
        # Run optimization
        study = optuna.create_study(
            direction='maximize' if self.model_specs[model_name]['type'] == 'classifier' else 'minimize'
        )
        study.optimize(objective, n_trials=50, show_progress_bar=True)
        
        return study.best_params

    def train_model(self, model_name: str) -> bool:
        """Train a single model in the ensemble."""

        try:
            logger.info(f"📊 Processing {model_name} ({self.model_specs[model_name]['type']})...")

            spec = self.model_specs[model_name]
            target_col = spec['target']

            # Prepare data
            X_train = self.train_data[self.feature_columns]
            y_train = self.train_data[target_col]
            X_val = self.val_data[self.feature_columns]
            y_val = self.val_data[target_col]

            # Remove invalid targets
            valid_mask_train = ~(pd.isna(y_train) | np.isinf(y_train))
            valid_mask_val = ~(pd.isna(y_val) | np.isinf(y_val))

            X_train_clean = X_train[valid_mask_train]
            y_train_clean = y_train[valid_mask_train]
            X_val_clean = X_val[valid_mask_val]
            y_val_clean = y_val[valid_mask_val]

            logger.info(f"   Valid targets: {len(y_train_clean):,}")

            if len(y_train_clean) < 100:
                logger.warning(f"   ⚠️ Insufficient valid data for {model_name}")
                return False

            # Optimize hyperparameters
            logger.info(f"🔧 OPTIMIZING HYPERPARAMETERS FOR {model_name.upper()}...")
            best_params = self.optimize_hyperparameters(
                model_name, X_train_clean, y_train_clean, X_val_clean, y_val_clean
            )

            logger.info(f"✅ Best parameters for {model_name}: {best_params}")

            # Create final model with best parameters
            if spec['type'] == 'classifier':
                model = LogisticRegression(**best_params)
            else:
                # Determine model type from best params
                if 'reg_type' in best_params:
                    reg_type = best_params.pop('reg_type')
                    if reg_type == 'ridge':
                        model = Ridge(**best_params)
                    elif reg_type == 'lasso':
                        model = Lasso(**best_params)
                    else:  # elasticnet
                        model = ElasticNet(**best_params)
                else:
                    model = Ridge(**best_params)

            # Create and fit scaler
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train_clean)
            X_val_scaled = scaler.transform(X_val_clean)

            # Train model
            logger.info(f"🎯 TRAINING {model_name.upper()} MODEL...")
            start_time = time.time()

            model.fit(X_train_scaled, y_train_clean)

            training_time = time.time() - start_time

            # Validate model
            if spec['type'] == 'classifier':
                y_pred = model.predict(X_val_scaled)
                accuracy = accuracy_score(y_val_clean, y_pred)
                logger.info(f"✅ {model_name} training completed:")
                logger.info(f"   Training samples: {len(y_train_clean):,}")
                logger.info(f"   Validation samples: {len(y_val_clean):,}")
                logger.info(f"   Training time: {training_time:.2f}s")
                logger.info(f"   Validation accuracy: {accuracy:.4f}")
            else:
                y_pred = model.predict(X_val_scaled)
                mse = mean_squared_error(y_val_clean, y_pred)
                r2 = r2_score(y_val_clean, y_pred)
                rmse = np.sqrt(mse)
                logger.info(f"✅ {model_name} training completed:")
                logger.info(f"   Training samples: {len(y_train_clean):,}")
                logger.info(f"   Validation samples: {len(y_val_clean):,}")
                logger.info(f"   Training time: {training_time:.2f}s")
                logger.info(f"   Validation R²: {r2:.4f}")
                logger.info(f"   Validation RMSE: {rmse:.6f}")

            # Store model and scaler
            self.models[model_name] = model
            self.scalers[model_name] = scaler

            # Store metadata
            self.model_metadata[model_name] = {
                'type': spec['type'],
                'target': target_col,
                'training_samples': len(y_train_clean),
                'validation_samples': len(y_val_clean),
                'training_time': training_time,
                'best_params': best_params,
                'model_class': type(model).__name__
            }

            logger.info(f"✅ {model_name} model trained successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to train {model_name}: {e}")
            return False

    def train_ensemble(self) -> bool:
        """Train all models in the ensemble."""

        logger.info("🚀 STARTING LINEAR ENSEMBLE TRAINING")
        logger.info("=" * 70)

        success_count = 0
        total_models = len(self.model_specs)

        for model_name in self.model_specs.keys():
            if self.train_model(model_name):
                success_count += 1
            else:
                logger.error(f"❌ Failed to train {model_name}")

        logger.info("=" * 70)
        logger.info("🎉 ENSEMBLE TRAINING COMPLETED")
        logger.info(f"   Models trained: {success_count}/{total_models}")
        logger.info(f"   Total features: {len(self.feature_columns)}")

        return success_count == total_models

    def save_models(self) -> bool:
        """Save trained models and metadata."""

        try:
            # Create output directory
            output_dir = Path(f"models/linear_ensemble")
            output_dir.mkdir(parents=True, exist_ok=True)

            logger.info(f"💾 SAVING LINEAR ENSEMBLE MODELS...")
            logger.info(f"   Output directory: {output_dir}")

            # Save each model and scaler
            for model_name, model in self.models.items():
                model_file = output_dir / f"linear_{model_name}_{self.timestamp}.pkl"
                scaler_file = output_dir / f"scaler_{model_name}_{self.timestamp}.pkl"

                joblib.dump(model, model_file)
                joblib.dump(self.scalers[model_name], scaler_file)

                logger.info(f"   ✅ Saved {model_name} model and scaler")

            # Save ensemble metadata
            metadata = {
                'timestamp': self.timestamp,
                'model_specs': self.model_specs,
                'feature_columns': self.feature_columns,
                'model_metadata': self.model_metadata,
                'training_summary': {
                    'total_models': len(self.models),
                    'feature_count': len(self.feature_columns),
                    'train_samples': len(self.train_data),
                    'val_samples': len(self.val_data),
                    'test_samples': len(self.test_data)
                }
            }

            metadata_file = output_dir / f"ensemble_metadata_{self.timestamp}.json"
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)

            logger.info(f"   ✅ Saved ensemble metadata")
            logger.info(f"💾 All models saved successfully to {output_dir}")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to save models: {e}")
            return False

    def validate_ensemble(self) -> Dict[str, Any]:
        """Comprehensive validation of the trained ensemble."""

        logger.info("🔍 PERFORMING COMPREHENSIVE ENSEMBLE VALIDATION...")

        validation_results = {}

        # 1. Signal accuracy validation
        logger.info("📊 Validating signal accuracy...")
        signal_accuracy = self._validate_signal_accuracy()
        validation_results['signal_accuracy'] = signal_accuracy

        # 2. Stability validation
        logger.info("📈 Validating model stability...")
        stability_results = self._validate_stability()
        validation_results['stability'] = stability_results

        # 3. Risk-reward validation
        logger.info("💰 Validating risk-reward ratios...")
        risk_reward = self._validate_risk_reward()
        validation_results['risk_reward'] = risk_reward

        # 4. Inference speed test
        logger.info("⚡ Testing inference speed...")
        speed_test = self._test_inference_speed()
        validation_results['inference_speed'] = speed_test

        # 5. Safety check specialization
        logger.info("🛡️ Validating safety check capabilities...")
        safety_check = self._validate_safety_check()
        validation_results['safety_check'] = safety_check

        # Overall assessment
        validation_results['overall_assessment'] = self._assess_overall_performance(validation_results)

        logger.info("✅ Comprehensive validation completed")

        return validation_results

    def _validate_signal_accuracy(self) -> Dict[str, Any]:
        """Validate signal accuracy against forward price movements."""

        if 'signal_direction' not in self.models:
            return {'error': 'Signal direction model not trained'}

        model = self.models['signal_direction']
        scaler = self.scalers['signal_direction']
        X_test = self.test_data[self.feature_columns]
        y_test = self.test_data['signal_direction']

        # Remove invalid data
        valid_mask = ~(pd.isna(y_test) | np.isinf(y_test))
        X_test_clean = X_test[valid_mask]
        y_test_clean = y_test[valid_mask]

        # Scale and predict
        X_test_scaled = scaler.transform(X_test_clean)
        predictions = model.predict(X_test_scaled)

        # Calculate metrics
        accuracy = accuracy_score(y_test_clean, predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(y_test_clean, predictions, average=None, zero_division=0)

        # Handle class distribution for potentially negative class labels
        unique_classes, class_counts = np.unique(y_test_clean, return_counts=True)
        class_distribution = dict(zip(unique_classes.tolist(), class_counts.tolist()))

        return {
            'test_accuracy': accuracy,
            'precision_by_class': precision.tolist(),
            'recall_by_class': recall.tolist(),
            'f1_by_class': f1.tolist(),
            'class_distribution': class_distribution,
            'meets_threshold': accuracy >= 0.55
        }

    def _validate_stability(self) -> Dict[str, Any]:
        """Validate model stability across time periods."""

        if 'signal_direction' not in self.models:
            return {'error': 'Signal direction model not trained'}

        model = self.models['signal_direction']
        scaler = self.scalers['signal_direction']
        X_test = self.test_data[self.feature_columns]
        y_test = self.test_data['signal_direction']

        # Remove invalid data
        valid_mask = ~(pd.isna(y_test) | np.isinf(y_test))
        X_clean = X_test[valid_mask]
        y_clean = y_test[valid_mask]

        # Time series cross-validation for stability
        tscv = TimeSeriesSplit(n_splits=5)
        fold_scores = []

        for fold, (train_idx, val_idx) in enumerate(tscv.split(X_clean)):
            X_fold_train, X_fold_val = X_clean.iloc[train_idx], X_clean.iloc[val_idx]
            y_fold_train, y_fold_val = y_clean.iloc[train_idx], y_clean.iloc[val_idx]

            # Create temporary model for this fold
            temp_model = LogisticRegression(max_iter=1000, random_state=42)
            temp_scaler = StandardScaler()

            X_fold_train_scaled = temp_scaler.fit_transform(X_fold_train)
            X_fold_val_scaled = temp_scaler.transform(X_fold_val)

            temp_model.fit(X_fold_train_scaled, y_fold_train)

            # Predict and score
            fold_pred = temp_model.predict(X_fold_val_scaled)
            fold_accuracy = accuracy_score(y_fold_val, fold_pred)
            fold_scores.append(fold_accuracy)

        stability_score = np.std(fold_scores)
        mean_score = np.mean(fold_scores)

        return {
            'fold_scores': fold_scores,
            'mean_accuracy': mean_score,
            'stability_std': stability_score,
            'is_stable': stability_score < 0.05
        }

    def _validate_risk_reward(self) -> Dict[str, Any]:
        """Validate risk-reward ratios from TP/SL predictions."""

        required_models = ['tp1_long', 'tp1_short', 'sl_long', 'sl_short']
        if not all(model in self.models for model in required_models):
            return {'error': 'Required TP/SL models not trained'}

        X_test = self.test_data[self.feature_columns]

        # Get predictions for all TP/SL models
        predictions = {}
        for model_name in required_models:
            model = self.models[model_name]
            scaler = self.scalers[model_name]
            X_test_scaled = scaler.transform(X_test)
            pred = model.predict(X_test_scaled)
            predictions[model_name] = pred

        # Calculate risk-reward ratios
        long_rr = predictions['tp1_long'] / np.abs(predictions['sl_long'])
        short_rr = predictions['tp1_short'] / np.abs(predictions['sl_short'])

        # Remove invalid ratios
        long_rr_clean = long_rr[(long_rr > 0) & (long_rr < 10)]
        short_rr_clean = short_rr[(short_rr > 0) & (short_rr < 10)]

        avg_rr = np.mean(np.concatenate([long_rr_clean, short_rr_clean]))

        return {
            'average_risk_reward': avg_rr,
            'long_avg_rr': np.mean(long_rr_clean),
            'short_avg_rr': np.mean(short_rr_clean),
            'meets_threshold': avg_rr >= 1.5
        }

    def _test_inference_speed(self) -> Dict[str, Any]:
        """Test inference speed for live trading readiness."""

        if not self.models:
            return {'error': 'No models trained'}

        # Test with sample data
        sample_data = self.test_data[self.feature_columns].iloc[:100]

        # Time single prediction
        start_time = time.time()
        for model_name, model in self.models.items():
            scaler = self.scalers[model_name]
            sample_scaled = scaler.transform(sample_data.iloc[:1])
            _ = model.predict(sample_scaled)
        single_time = time.time() - start_time

        # Time batch prediction
        start_time = time.time()
        for model_name, model in self.models.items():
            scaler = self.scalers[model_name]
            sample_scaled = scaler.transform(sample_data)
            _ = model.predict(sample_scaled)
        batch_time = time.time() - start_time

        return {
            'single_prediction_time': single_time,
            'batch_prediction_time': batch_time,
            'predictions_per_second': 100 / batch_time,
            'meets_threshold': single_time < 30.0
        }

    def _validate_safety_check(self) -> Dict[str, Any]:
        """Validate Linear model's safety check capabilities."""

        # Linear models provide interpretability and stability
        safety_features = {
            'interpretability': True,  # Linear models are inherently interpretable
            'stability': len(self.models) > 0,  # Models trained successfully
            'regularization': True,  # All models use regularization
            'feature_scaling': len(self.scalers) > 0,  # Proper feature scaling
        }

        # Check coefficient magnitudes for stability
        coefficient_stability = []
        for model_name, model in self.models.items():
            if hasattr(model, 'coef_'):
                coef_std = np.std(model.coef_)
                coefficient_stability.append(coef_std < 10.0)  # Reasonable coefficient range

        return {
            'safety_features': safety_features,
            'coefficient_stability': all(coefficient_stability) if coefficient_stability else False,
            'model_interpretability': True,
            'regularization_applied': True,
            'meets_safety_requirements': all(safety_features.values())
        }

    def _assess_overall_performance(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall ensemble performance and readiness."""

        criteria_met = {}

        # Check signal accuracy
        signal_acc = validation_results.get('signal_accuracy', {})
        criteria_met['signal_accuracy_55pct'] = signal_acc.get('meets_threshold', False)

        # Check stability
        stability = validation_results.get('stability', {})
        criteria_met['model_stability'] = stability.get('is_stable', False)

        # Check risk-reward
        risk_reward = validation_results.get('risk_reward', {})
        criteria_met['risk_reward_1_5'] = risk_reward.get('meets_threshold', False)

        # Check inference speed
        speed = validation_results.get('inference_speed', {})
        criteria_met['inference_speed_30s'] = speed.get('meets_threshold', False)

        # Check safety requirements
        safety = validation_results.get('safety_check', {})
        criteria_met['safety_requirements'] = safety.get('meets_safety_requirements', False)

        # Calculate overall score
        total_criteria = len(criteria_met)
        met_criteria = sum(criteria_met.values())
        overall_score = (met_criteria / total_criteria) * 100

        # Determine grade
        if overall_score >= 90:
            grade = 'A'
        elif overall_score >= 80:
            grade = 'B'
        elif overall_score >= 70:
            grade = 'C'
        elif overall_score >= 60:
            grade = 'D'
        else:
            grade = 'F'

        return {
            'criteria_met': criteria_met,
            'total_criteria': total_criteria,
            'met_criteria': met_criteria,
            'overall_score': overall_score,
            'overall_grade': grade,
            'ready_for_live_trading': overall_score >= 70
        }


def main() -> bool:
    """Main training function."""

    try:
        # Initialize trainer
        trainer = LinearEnsembleTrainer()

        # Load data
        if not trainer.load_data():
            logger.error("❌ Failed to load training data")
            return False

        # Train ensemble
        if not trainer.train_ensemble():
            logger.error("❌ Failed to train ensemble")
            return False

        # Save models
        if not trainer.save_models():
            logger.error("❌ Failed to save models")
            return False

        # Validate ensemble
        validation_results = trainer.validate_ensemble()

        # Print comprehensive results
        logger.info("=" * 80)
        logger.info("🎉 LINEAR ENSEMBLE TRAINING COMPLETED")
        logger.info("=" * 80)

        # Signal accuracy results
        if 'signal_accuracy' in validation_results:
            signal_acc = validation_results['signal_accuracy']
            logger.info(f"📊 SIGNAL ACCURACY VALIDATION:")
            logger.info(f"   Test accuracy: {signal_acc.get('test_accuracy', 0):.4f}")
            logger.info(f"   Meets 55% threshold: {signal_acc.get('meets_threshold', False)}")

        # Stability results
        if 'stability' in validation_results:
            stability = validation_results['stability']
            logger.info(f"📈 STABILITY VALIDATION:")
            logger.info(f"   Mean accuracy: {stability.get('mean_accuracy', 0):.4f}")
            logger.info(f"   Stability (std): {stability.get('stability_std', 0):.4f}")
            logger.info(f"   Is stable: {stability.get('is_stable', False)}")

        # Risk-reward results
        if 'risk_reward' in validation_results:
            rr = validation_results['risk_reward']
            logger.info(f"💰 RISK-REWARD VALIDATION:")
            logger.info(f"   Average R:R ratio: {rr.get('average_risk_reward', 0):.2f}")
            logger.info(f"   Meets 1.5:1 threshold: {rr.get('meets_threshold', False)}")

        # Speed results
        if 'inference_speed' in validation_results:
            speed = validation_results['inference_speed']
            logger.info(f"⚡ INFERENCE SPEED TEST:")
            logger.info(f"   Single prediction: {speed.get('single_prediction_time', 0):.3f}s")
            logger.info(f"   Predictions/sec: {speed.get('predictions_per_second', 0):.1f}")
            logger.info(f"   Meets <30s threshold: {speed.get('meets_threshold', False)}")

        # Safety check results
        if 'safety_check' in validation_results:
            safety = validation_results['safety_check']
            logger.info(f"🛡️ SAFETY CHECK VALIDATION:")
            logger.info(f"   Interpretability: {safety.get('model_interpretability', False)}")
            logger.info(f"   Coefficient stability: {safety.get('coefficient_stability', False)}")
            logger.info(f"   Meets safety requirements: {safety.get('meets_safety_requirements', False)}")

        # Overall assessment
        if 'overall_assessment' in validation_results:
            assessment = validation_results['overall_assessment']
            logger.info(f"🎯 OVERALL ASSESSMENT:")
            logger.info(f"   Criteria met: {assessment.get('met_criteria', 0)}/{assessment.get('total_criteria', 0)}")
            logger.info(f"   Overall score: {assessment.get('overall_score', 0):.1f}/100")
            logger.info(f"   Overall grade: {assessment.get('overall_grade', 'N/A')}")
            logger.info(f"   Ready for live trading: {assessment.get('ready_for_live_trading', False)}")

        logger.info(f"🛡️ SAFETY CHECK SPECIALIZATION:")
        logger.info(f"   Level 1 hierarchical decision-making ready")
        logger.info(f"   Linear model interpretability enabled")
        logger.info(f"   Stability monitoring and risk assessment capabilities")

        logger.info(f"🚀 NEXT STEPS:")
        logger.info(f"   1. Review validation results and recommendations")
        logger.info(f"   2. Test hierarchical wrapper with live data")
        logger.info(f"   3. Integrate with other ensemble levels")
        logger.info(f"   4. Complete 4-level hierarchical system")

        return True

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
