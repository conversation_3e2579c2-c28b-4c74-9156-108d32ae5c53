# Live Trading System Configuration - Production

# Trading Parameters
symbol: "XAUUSD!"
timeframe_minutes: 5
prediction_interval_seconds: 30  # Model prediction frequency
execution_interval_seconds: 60   # Trade execution check frequency

# Risk Management
initial_capital: 400.0
max_risk_per_trade: 0.03        # 4% risk per trade (doubled for multi-tier system)
max_concurrent_trades: 1
max_daily_trades: 20
max_daily_loss: 0.05             # 5% max daily loss

# Model-Driven Parameters (UPDATED: Moved to ensemble section below)

# Position Sizing (Model-Driven)
base_position_size: 0.1          # Base lot size
confidence_multiplier: 1.5       # Multiply by confidence
volatility_adjustment: true      # Use model volatility adjustment

# Entry/Exit Optimization (Model-Driven)
use_model_entry_timing: true     # Use model for optimal entry timing
use_model_exit_levels: true      # Use model for TP/SL levels
entry_patience_seconds: 300      # Max wait time for optimal entry

# Multi-Tier TP/SL System - TEMPORARILY DISABLED TP1/TP2
tp1_percentage: 0.0              # TEMPORARILY DISABLED - was 0.4 (40%)
tp2_percentage: 0.0              # TEMPORARILY DISABLED - was 0.35 (35%)
tp3_percentage: 1.0              # 100% for trend following (was 0.25)

# Transaction Costs (MT5 Realistic)
spread_pips: 0.18                # Average spread from analysis
slippage_pips: 0.05              # Expected slippage
commission_per_lot: 0.0          # Commission per lot

# MT5 Connection
mt5_timeout_seconds: 30
mt5_retry_attempts: 3
mt5_retry_delay: 1.0

# Data and Models - UPDATED for Specialized Ensemble Architecture
ensemble_models_dir: "data/models"  # Directory containing all ensemble models
model_path: "data/models/retrained_lightgbm_signal_generator_20250922_001127.pkl"  # Primary model for validation
feature_pipeline_path: "data/models/xauusd_feature_pipeline.joblib"
data_buffer_size: 1000           # Historical data buffer
use_specialized_ensemble: true   # Use the new specialized ensemble architecture

# UPDATED: Ensemble Model Configuration
ensemble_model_files:
  lightgbm: "retrained_lightgbm_signal_generator_20250922_001127.pkl"
  catboost: "fixed_catboost_market_regime_analyst_20250921_044615.pkl"
  xgboost: "fixed_xgboost_risk_manager_20250921_044032.pkl"
  linear: "specialized_linear_stability_monitor_20250921_042620.pkl"

# UPDATED: Ensemble Weights and Roles
ensemble_weights:
  lightgbm: 0.40  # Signal Generator
  catboost: 0.25  # Market Regime Analyst
  xgboost: 0.25   # Risk Manager
  linear: 0.10    # Stability Monitor

# UPDATED: Ensemble-Specific Thresholds (Lowered for ensemble operation)
min_signal_confidence: 0.50        # Lowered from 0.65 for ensemble
high_confidence_threshold: 0.75    # Lowered from 0.8 for ensemble
ensemble_consensus_threshold: 0.60 # Lowered from 0.7 for ensemble

# Performance Monitoring
latency_warning_ms: 50.0         # Warn if latency > 50ms
latency_critical_ms: 100.0       # Critical if latency > 100ms
performance_log_interval: 300    # Log performance every 5 minutes

# Session-Based Trading
trading_sessions:
  asian:
    start_hour: 0
    end_hour: 8
    position_multiplier: 0.8     # Reduce position size
    min_confidence: 0.7          # Higher confidence required
  european:
    start_hour: 8
    end_hour: 16
    position_multiplier: 1.0     # Standard position size
    min_confidence: 0.65         # Standard confidence
  us:
    start_hour: 16
    end_hour: 24
    position_multiplier: 1.2     # Increase position size
    min_confidence: 0.6          # Lower confidence acceptable

# Emergency Controls
emergency_stop_enabled: true
max_consecutive_losses: 5
emergency_stop_loss_pct: 0.1     # 10% portfolio loss triggers stop

# Logging and Monitoring
log_level: "INFO"
log_trades: true
log_predictions: true
log_performance: true
save_trade_history: true
