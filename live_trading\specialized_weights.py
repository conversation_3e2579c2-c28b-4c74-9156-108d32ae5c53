"""
Specialized Model Weight System for Live Trading

Implements dynamic weight adjustment based on model performance and market conditions:
- LightGBM Signal Generator: 40% weight (primary signal generation)
- CatBoost Market Regime Analyst: 25% weight (regime classification)
- XGBoost Risk Manager: 25% weight (risk assessment)
- Linear Stability Monitor: 10% weight (system health)
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from collections import deque
try:
    from utils.logging_utils import LoggerMixin
except ImportError:
    # Fallback for missing utils
    import logging
    class LoggerMixin:
        def __init__(self):
            self.logger = logging.getLogger(self.__class__.__name__)


@dataclass
class ModelPerformance:
    """Track individual model performance metrics."""
    model_type: str
    base_weight: float
    current_weight: float
    accuracy_score: float
    confidence_score: float
    recent_predictions: int
    correct_predictions: int
    avg_prediction_time: float
    last_updated: datetime


@dataclass
class WeightAdjustment:
    """Represents a weight adjustment decision."""
    model_type: str
    old_weight: float
    new_weight: float
    reason: str
    adjustment_factor: float
    timestamp: datetime


class SpecializedWeightSystem(LoggerMixin):
    """
    Dynamic weight adjustment system for specialized ensemble models.
    
    Monitors model performance and adjusts weights based on:
    - Recent prediction accuracy
    - Model confidence levels
    - Market regime compatibility
    - Prediction latency
    - Consensus contribution
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Base weight configuration (specialized roles)
        self.base_weights = {
            'lightgbm': 0.40,    # Signal Generator (primary)
            'catboost': 0.25,    # Market Regime Analyst
            'xgboost': 0.25,     # Risk Manager
            'linear': 0.10       # Stability Monitor
        }
        
        # Current dynamic weights
        self.current_weights = self.base_weights.copy()
        
        # Performance tracking
        self.model_performance = {}
        for model_type, base_weight in self.base_weights.items():
            self.model_performance[model_type] = ModelPerformance(
                model_type=model_type,
                base_weight=base_weight,
                current_weight=base_weight,
                accuracy_score=0.5,
                confidence_score=0.5,
                recent_predictions=0,
                correct_predictions=0,
                avg_prediction_time=0.0,
                last_updated=datetime.now()
            )
        
        # Weight adjustment history
        self.weight_history = deque(maxlen=1000)
        
        # Adjustment parameters
        self.min_weight_multiplier = 0.5  # Minimum 50% of base weight
        self.max_weight_multiplier = 1.8  # Maximum 180% of base weight
        self.adjustment_sensitivity = 0.1  # How quickly to adjust weights
        self.performance_window = timedelta(hours=24)  # Performance evaluation window
        
        self.logger.info("Specialized Weight System initialized")
    
    def get_current_weights(self) -> Dict[str, float]:
        """Get current model weights."""
        return self.current_weights.copy()
    
    def update_model_performance(self, model_type: str, prediction_result: Dict[str, Any],
                               actual_outcome: Optional[Dict[str, Any]] = None):
        """
        Update model performance metrics.
        
        Args:
            model_type: Type of model ('lightgbm', 'catboost', 'xgboost', 'linear')
            prediction_result: Model prediction result
            actual_outcome: Actual market outcome (if available)
        """
        if model_type not in self.model_performance:
            self.logger.warning(f"Unknown model type: {model_type}")
            return
        
        perf = self.model_performance[model_type]
        
        # Update prediction count
        perf.recent_predictions += 1
        
        # Update confidence score
        confidence = prediction_result.get('confidence', 0.5)
        perf.confidence_score = (perf.confidence_score * 0.9) + (confidence * 0.1)
        
        # Update prediction time
        pred_time = prediction_result.get('prediction_time_ms', 0.0)
        if pred_time > 0:
            if perf.avg_prediction_time == 0:
                perf.avg_prediction_time = pred_time
            else:
                perf.avg_prediction_time = (perf.avg_prediction_time * 0.9) + (pred_time * 0.1)
        
        # Update accuracy if actual outcome available
        if actual_outcome:
            predicted_direction = prediction_result.get('direction', 'hold')
            actual_direction = actual_outcome.get('direction', 'hold')
            
            if predicted_direction == actual_direction:
                perf.correct_predictions += 1
            
            # Calculate accuracy over recent window
            if perf.recent_predictions > 0:
                perf.accuracy_score = perf.correct_predictions / perf.recent_predictions
        
        perf.last_updated = datetime.now()
        
        # Trigger weight adjustment if needed
        self._evaluate_weight_adjustments()
    
    def calculate_ensemble_weights(self, market_context: Dict[str, Any],
                                 model_predictions: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate dynamic ensemble weights based on current conditions.
        
        Args:
            market_context: Current market conditions
            model_predictions: Current model predictions
            
        Returns:
            Dictionary of adjusted model weights
        """
        try:
            adjusted_weights = self.current_weights.copy()
            
            # Market regime-based adjustments
            market_regime = market_context.get('market_regime', 'normal')
            volatility_regime = market_context.get('volatility_regime', 'normal')
            
            # Adjust CatBoost weight based on regime uncertainty
            if market_regime in ['transition', 'uncertain']:
                # Increase CatBoost weight for regime analysis
                catboost_boost = 0.1
                adjusted_weights['catboost'] = min(
                    self.base_weights['catboost'] * self.max_weight_multiplier,
                    adjusted_weights['catboost'] + catboost_boost
                )
                # Reduce other weights proportionally
                self._normalize_weights(adjusted_weights, exclude='catboost')
            
            # Adjust XGBoost weight based on volatility
            if volatility_regime in ['high', 'extreme']:
                # Increase XGBoost weight for risk management
                xgboost_boost = 0.08
                adjusted_weights['xgboost'] = min(
                    self.base_weights['xgboost'] * self.max_weight_multiplier,
                    adjusted_weights['xgboost'] + xgboost_boost
                )
                # Reduce other weights proportionally
                self._normalize_weights(adjusted_weights, exclude='xgboost')
            
            # Adjust LightGBM weight based on signal clarity
            lightgbm_pred = model_predictions.get('lightgbm', {})
            signal_confidence = lightgbm_pred.get('confidence', 0.5)
            
            if signal_confidence > 0.8:
                # Boost LightGBM for high-confidence signals
                lightgbm_boost = 0.05
                adjusted_weights['lightgbm'] = min(
                    self.base_weights['lightgbm'] * self.max_weight_multiplier,
                    adjusted_weights['lightgbm'] + lightgbm_boost
                )
                # Reduce other weights proportionally
                self._normalize_weights(adjusted_weights, exclude='lightgbm')
            
            # Performance-based adjustments
            adjusted_weights = self._apply_performance_adjustments(adjusted_weights)
            
            # Ensure weights sum to 1.0
            self._normalize_weights(adjusted_weights)
            
            return adjusted_weights
            
        except Exception as e:
            self.logger.error(f"Weight calculation failed: {str(e)}")
            return self.current_weights.copy()
    
    def _evaluate_weight_adjustments(self):
        """Evaluate if weight adjustments are needed based on performance."""
        try:
            current_time = datetime.now()
            adjustments_made = []
            
            for model_type, perf in self.model_performance.items():
                # Skip if not enough recent data
                if perf.recent_predictions < 10:
                    continue
                
                # Calculate performance score
                performance_score = self._calculate_performance_score(perf)
                
                # Determine if adjustment needed
                target_weight = self.base_weights[model_type]
                
                if performance_score > 0.7:
                    # Good performance - increase weight slightly
                    adjustment_factor = 1.0 + (performance_score - 0.7) * self.adjustment_sensitivity
                elif performance_score < 0.4:
                    # Poor performance - decrease weight
                    adjustment_factor = 1.0 - (0.4 - performance_score) * self.adjustment_sensitivity
                else:
                    # Average performance - move toward base weight
                    adjustment_factor = 1.0
                
                # Apply adjustment with limits
                new_weight = target_weight * adjustment_factor
                new_weight = max(
                    target_weight * self.min_weight_multiplier,
                    min(target_weight * self.max_weight_multiplier, new_weight)
                )
                
                # Update if significant change
                if abs(new_weight - self.current_weights[model_type]) > 0.01:
                    old_weight = self.current_weights[model_type]
                    self.current_weights[model_type] = new_weight
                    perf.current_weight = new_weight
                    
                    adjustment = WeightAdjustment(
                        model_type=model_type,
                        old_weight=old_weight,
                        new_weight=new_weight,
                        reason=f"Performance score: {performance_score:.3f}",
                        adjustment_factor=adjustment_factor,
                        timestamp=current_time
                    )
                    
                    adjustments_made.append(adjustment)
                    self.weight_history.append(adjustment)
            
            # Normalize weights after adjustments
            if adjustments_made:
                self._normalize_weights(self.current_weights)
                
                self.logger.info(f"Weight adjustments made: {len(adjustments_made)}")
                for adj in adjustments_made:
                    self.logger.debug(f"{adj.model_type}: {adj.old_weight:.3f} → {adj.new_weight:.3f} ({adj.reason})")
            
        except Exception as e:
            self.logger.error(f"Weight adjustment evaluation failed: {str(e)}")
    
    def _calculate_performance_score(self, perf: ModelPerformance) -> float:
        """Calculate overall performance score for a model."""
        # Weighted combination of metrics
        accuracy_weight = 0.4
        confidence_weight = 0.3
        speed_weight = 0.2
        recency_weight = 0.1
        
        # Accuracy component
        accuracy_component = perf.accuracy_score
        
        # Confidence component
        confidence_component = perf.confidence_score
        
        # Speed component (inverse of prediction time, normalized)
        if perf.avg_prediction_time > 0:
            speed_component = max(0.0, 1.0 - (perf.avg_prediction_time / 1000.0))  # Normalize to seconds
        else:
            speed_component = 1.0
        
        # Recency component (how recently updated)
        time_since_update = (datetime.now() - perf.last_updated).total_seconds()
        recency_component = max(0.0, 1.0 - (time_since_update / 3600.0))  # Decay over 1 hour
        
        performance_score = (
            accuracy_component * accuracy_weight +
            confidence_component * confidence_weight +
            speed_component * speed_weight +
            recency_component * recency_weight
        )
        
        return max(0.0, min(1.0, performance_score))
    
    def _apply_performance_adjustments(self, weights: Dict[str, float]) -> Dict[str, float]:
        """Apply performance-based weight adjustments."""
        adjusted_weights = weights.copy()
        
        # Get performance scores
        performance_scores = {}
        for model_type in weights.keys():
            if model_type in self.model_performance:
                performance_scores[model_type] = self._calculate_performance_score(
                    self.model_performance[model_type]
                )
            else:
                performance_scores[model_type] = 0.5
        
        # Apply gradual adjustments based on relative performance
        avg_performance = np.mean(list(performance_scores.values()))
        
        for model_type, score in performance_scores.items():
            if score > avg_performance + 0.1:
                # Above average performance
                boost = min(0.05, (score - avg_performance) * 0.5)
                adjusted_weights[model_type] += boost
            elif score < avg_performance - 0.1:
                # Below average performance
                reduction = min(0.05, (avg_performance - score) * 0.5)
                adjusted_weights[model_type] = max(
                    self.base_weights[model_type] * self.min_weight_multiplier,
                    adjusted_weights[model_type] - reduction
                )
        
        return adjusted_weights
    
    def _normalize_weights(self, weights: Dict[str, float], exclude: Optional[str] = None):
        """Normalize weights to sum to 1.0."""
        if exclude:
            # Normalize all weights except the excluded one
            excluded_weight = weights[exclude]
            remaining_weight = 1.0 - excluded_weight
            
            other_weights = {k: v for k, v in weights.items() if k != exclude}
            total_other = sum(other_weights.values())
            
            if total_other > 0:
                for model_type in other_weights:
                    weights[model_type] = (weights[model_type] / total_other) * remaining_weight
        else:
            # Normalize all weights
            total_weight = sum(weights.values())
            if total_weight > 0:
                for model_type in weights:
                    weights[model_type] /= total_weight
    
    def get_weight_history(self, hours: int = 24) -> List[WeightAdjustment]:
        """Get weight adjustment history for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [adj for adj in self.weight_history if adj.timestamp >= cutoff_time]
    
    def reset_performance_stats(self):
        """Reset performance statistics for all models."""
        for perf in self.model_performance.values():
            perf.accuracy_score = 0.5
            perf.confidence_score = 0.5
            perf.recent_predictions = 0
            perf.correct_predictions = 0
            perf.avg_prediction_time = 0.0
            perf.last_updated = datetime.now()
        
        self.current_weights = self.base_weights.copy()
        self.weight_history.clear()
        
        self.logger.info("Performance statistics reset")
