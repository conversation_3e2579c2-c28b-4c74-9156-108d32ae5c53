# Advanced Feature Quality Configuration
# Comprehensive NaN handling and data quality optimization settings

# Global Data Quality Settings
global_settings:
  max_nan_ratio_per_feature: 0.15  # 15% max NaN values per feature
  quality_score_threshold: 0.85    # Minimum acceptable quality score
  enable_real_time_monitoring: true
  log_quality_metrics: true
  save_quality_reports: true

# NaN Handling Strategies by Feature Type
nan_handling_strategies:
  
  # Price-based features (returns, momentum, gaps)
  price_features:
    patterns:
      - "return_"
      - "roc_"
      - "momentum_"
      - "gap_"
      - "intraday_"
      - "price_acceleration"
    strategy: "zero_fill"
    default_value: 0.0
    interpolation_method: "linear"
    max_interpolation_gap: 5
    fallback_method: "zero_fill"
    validation:
      valid_range: [-0.2, 0.2]  # ±20% max reasonable return
      outlier_threshold: 3.0
  
  # RSI and bounded indicators (0-100 range)
  bounded_indicators:
    patterns:
      - "rsi_"
      - "stoch_"
      - "williams_"
      - "cci_"
    strategy: "neutral_fill"
    default_value: 50.0  # Neutral RSI value
    interpolation_method: "linear"
    max_interpolation_gap: 10
    fallback_method: "neutral_fill"
    validation:
      valid_range: [0, 100]
      outlier_threshold: 2.0
  
  # Correlation features (-1 to 1 range)
  correlation_features:
    patterns:
      - "corr_"
      - "correlation_"
      - "_corr"
    strategy: "zero_fill"
    default_value: 0.0  # No correlation
    interpolation_method: "linear"
    max_interpolation_gap: 20
    fallback_method: "zero_fill"
    validation:
      valid_range: [-1.0, 1.0]
      outlier_threshold: 2.5
  
  # Volatility features (non-negative)
  volatility_features:
    patterns:
      - "vol"
      - "atr_"
      - "bb_width"
      - "volatility_"
    strategy: "median_fill"
    default_value: null  # Use median
    interpolation_method: "linear"
    max_interpolation_gap: 15
    fallback_method: "median_fill"
    validation:
      valid_range: [0, null]  # Non-negative
      outlier_threshold: 3.0
  
  # Volume features (non-negative)
  volume_features:
    patterns:
      - "volume"
      - "vwap"
      - "obv"
      - "tick_volume"
    strategy: "forward_fill"
    default_value: null  # Use forward fill
    interpolation_method: "forward"
    max_interpolation_gap: 10
    fallback_method: "median_fill"
    validation:
      valid_range: [0, null]  # Non-negative
      outlier_threshold: 4.0
  
  # Bollinger Bands features
  bollinger_bands:
    patterns:
      - "bb_upper"
      - "bb_lower"
      - "bb_middle"
      - "bb_position"
    strategy: "interpolation"
    default_value: null
    interpolation_method: "linear"
    max_interpolation_gap: 20
    fallback_method: "forward_fill"
    validation:
      valid_range: [null, null]  # No specific range
      outlier_threshold: 3.0
  
  # Moving averages and trend features
  moving_averages:
    patterns:
      - "_mean_"
      - "_sma_"
      - "_ema_"
      - "trend_"
    strategy: "interpolation"
    default_value: null
    interpolation_method: "linear"
    max_interpolation_gap: 10
    fallback_method: "forward_fill"
    validation:
      valid_range: [null, null]
      outlier_threshold: 3.0
  
  # Statistical features (std, skew, kurt)
  statistical_features:
    patterns:
      - "_std_"
      - "_skew_"
      - "_kurt_"
      - "_var_"
    strategy: "median_fill"
    default_value: null
    interpolation_method: "linear"
    max_interpolation_gap: 20
    fallback_method: "median_fill"
    validation:
      valid_range: [null, null]
      outlier_threshold: 3.5
  
  # Regime and pattern features
  regime_features:
    patterns:
      - "regime_"
      - "pattern_"
      - "session_"
      - "trend_strength"
      - "trend_consistency"
    strategy: "mode_fill"
    default_value: 0  # Neutral regime
    interpolation_method: "nearest"
    max_interpolation_gap: 30
    fallback_method: "zero_fill"
    validation:
      valid_range: [null, null]
      outlier_threshold: null  # No outlier detection for categorical
  
  # Time-based features
  temporal_features:
    patterns:
      - "hour_"
      - "day_"
      - "month_"
      - "seasonal_"
      - "time_"
    strategy: "forward_fill"
    default_value: 0
    interpolation_method: "nearest"
    max_interpolation_gap: 5
    fallback_method: "zero_fill"
    validation:
      valid_range: [null, null]
      outlier_threshold: null

# Outlier Detection and Handling
outlier_detection:
  methods:
    - "z_score"
    - "iqr"
    - "isolation_forest"
  
  z_score:
    threshold: 3.0
    use_modified_z_score: true
    modified_threshold: 3.5
  
  iqr:
    multiplier: 1.5
    use_robust_iqr: true
  
  isolation_forest:
    contamination: 0.1
    random_state: 42
  
  handling_strategies:
    clip: "clip_to_threshold"
    winsorize: "winsorize_5_95"
    remove: "set_to_nan"
    transform: "log_transform"

# Feature Validation Rules
validation_rules:
  
  # Range validation
  range_checks:
    enable: true
    auto_clip: true
    log_violations: true
  
  # Variance checks
  variance_checks:
    min_variance_threshold: 1e-8
    zero_variance_action: "flag"  # flag, remove, or ignore
  
  # Correlation checks
  correlation_checks:
    max_correlation_threshold: 0.99
    high_correlation_action: "flag"  # flag, remove, or ignore
  
  # Distribution checks
  distribution_checks:
    check_normality: false
    check_stationarity: false
    normality_threshold: 0.05

# Performance Optimization
performance:
  enable_caching: true
  cache_size: 1000
  parallel_processing: false
  batch_size: 100
  memory_efficient: true

# Monitoring and Reporting
monitoring:
  enable_real_time_alerts: true
  quality_score_alert_threshold: 0.7
  nan_ratio_alert_threshold: 0.2
  
  reporting:
    generate_daily_reports: true
    save_quality_history: true
    max_history_days: 30
    report_format: "json"
    
  alerts:
    email_notifications: false
    log_level_alerts: "WARNING"
    slack_webhook: null

# Feature-Specific Overrides
# Override default settings for specific features
feature_overrides:
  
  # Problematic features that need special handling
  "volume.2":  # This feature was showing issues
    strategy: "forward_fill"
    max_interpolation_gap: 3
    fallback_method: "zero_fill"
    default_value: 0.0
  
  # Cross-asset correlation features that often have NaN
  "corr_dxy_100":
    strategy: "zero_fill"
    default_value: 0.0
    max_interpolation_gap: 50
  
  "corr_vix_50":
    strategy: "zero_fill"
    default_value: 0.0
    max_interpolation_gap: 50

# Emergency Fallback Settings
emergency_fallback:
  enable: true
  max_processing_time_ms: 5000  # 5 seconds max
  fallback_strategy: "zero_fill_all"
  log_emergency_usage: true
