"""
Real-Time Data Collection for Live Trading

Extends existing MT5DataCollector for streaming real-time data collection
with buffering and feature engineering integration.
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from collections import deque
import threading
import time

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.mt5_collector.mt5_client import MT5Client
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import MT5DataError


class RealTimeDataCollector(LoggerMixin):
    """
    Real-time data collector for live trading.
    
    Extends existing MT5DataCollector with streaming capabilities,
    data buffering, and real-time feature preparation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize real-time data collector.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.symbol = config.get('symbol', 'XAUUSD!')
        self.timeframe = config.get('timeframe_minutes', 5)
        self.buffer_size = config.get('data_buffer_size', 1000)
        
        # MT5 client
        self.client = MT5Client()
        self.is_connected = False
        
        # Data buffers
        self.price_buffer = deque(maxlen=self.buffer_size)
        self.tick_buffer = deque(maxlen=10000)  # Store recent ticks
        
        # Streaming control
        self.is_streaming = False
        self.stream_thread = None
        self.last_bar_time = None
        
        # Performance tracking
        self.stream_stats = {
            'bars_collected': 0,
            'ticks_collected': 0,
            'last_update': None,
            'avg_latency_ms': 0.0,
            'errors': 0,
            'connection_issues': 0
        }
        
        # MT5 timeframe mapping
        self.timeframe_mapping = {
            1: mt5.TIMEFRAME_M1,
            5: mt5.TIMEFRAME_M5,
            15: mt5.TIMEFRAME_M15,
            30: mt5.TIMEFRAME_M30,
            60: mt5.TIMEFRAME_H1,
            240: mt5.TIMEFRAME_H4
        }
    
    def connect(self) -> bool:
        """Establish connection to MT5."""
        try:
            if self.client.connect():
                self.is_connected = True
                
                # Verify symbol
                if not self.client.check_symbol(self.symbol):
                    raise MT5DataError(f"Symbol {self.symbol} not available")
                
                self.logger.info(f"Connected to MT5 for real-time data collection: {self.symbol}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to connect to MT5: {str(e)}")
            self.stream_stats['connection_issues'] += 1
            return False
    
    def disconnect(self):
        """Disconnect from MT5."""
        self.stop_streaming()
        if self.is_connected:
            self.client.disconnect()
            self.is_connected = False
            self.logger.info("Disconnected from MT5")
    
    def initialize_buffer(self) -> bool:
        """Initialize data buffer with historical data."""
        try:
            if not self.is_connected:
                if not self.connect():
                    return False
            
            # Get historical data to fill buffer
            mt5_timeframe = self.timeframe_mapping.get(self.timeframe, mt5.TIMEFRAME_M5)
            
            # Get last N bars
            rates = mt5.copy_rates_from_pos(
                self.symbol,
                mt5_timeframe,
                0,  # Start from current bar
                self.buffer_size
            )
            
            if rates is None or len(rates) == 0:
                raise MT5DataError("Failed to get historical data for buffer initialization")
            
            # Convert to DataFrame and add to buffer
            df = pd.DataFrame(rates)
            df['datetime'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('datetime', inplace=True)
            
            # Add required columns
            df['spread'] = 0.18  # Default spread from config
            df['real_volume'] = df['tick_volume']  # Use tick volume as proxy
            
            # Fill buffer
            for _, row in df.iterrows():
                self.price_buffer.append(row.to_dict())
            
            self.last_bar_time = df.index[-1]
            self.logger.info(f"Initialized buffer with {len(self.price_buffer)} bars")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize buffer: {str(e)}")
            self.stream_stats['errors'] += 1
            return False
    
    def start_streaming(self) -> bool:
        """Start real-time data streaming."""
        if self.is_streaming:
            self.logger.warning("Streaming already active")
            return True

        if not self.is_connected:
            if not self.connect():
                return False

        if not self.initialize_buffer():
            return False

        # Start streaming thread
        self.is_streaming = True
        self.stream_thread = threading.Thread(target=self._stream_worker, daemon=True)
        self.stream_thread.start()

        self.logger.info("🚀 Started real-time data streaming")
        self.logger.info(f"   Thread alive: {self.stream_thread.is_alive()}")
        self.logger.info(f"   Buffer size: {len(self.price_buffer)}")
        self.logger.info(f"   Last bar time: {self.last_bar_time}")

        return True
    
    def stop_streaming(self):
        """Stop real-time data streaming."""
        if self.is_streaming:
            self.is_streaming = False
            if self.stream_thread and self.stream_thread.is_alive():
                self.stream_thread.join(timeout=5.0)
            self.logger.info("Stopped real-time data streaming")
    
    def _stream_worker(self):
        """Worker thread for real-time data streaming."""
        mt5_timeframe = self.timeframe_mapping.get(self.timeframe, mt5.TIMEFRAME_M5)
        check_interval = min(30, self.timeframe * 60 / 4)  # Check 4 times per bar

        self.logger.info(f"🔍 STREAM WORKER STARTED:")
        self.logger.info(f"   Symbol: {self.symbol}")
        self.logger.info(f"   Timeframe: {self.timeframe} min (MT5: {mt5_timeframe})")
        self.logger.info(f"   Check interval: {check_interval} seconds")

        while self.is_streaming:
            try:
                start_time = time.time()

                # 🔍 CRITICAL DEBUGGING: Check MT5 connection
                if not mt5.terminal_info():
                    self.logger.error("🚨 MT5 terminal not connected!")
                    time.sleep(5)
                    continue

                # Get latest bar
                rates = mt5.copy_rates_from_pos(self.symbol, mt5_timeframe, 0, 1)

                # 🔍 CRITICAL DEBUGGING: Log what we get from MT5
                if rates is None:
                    self.logger.error(f"🚨 MT5 returned None for rates! Symbol: {self.symbol}, TF: {mt5_timeframe}")
                    time.sleep(5)
                    continue
                elif len(rates) == 0:
                    self.logger.error(f"🚨 MT5 returned empty rates array!")
                    time.sleep(5)
                    continue

                latest_bar = rates[0]
                bar_time = pd.to_datetime(latest_bar['time'], unit='s')

                # 🔍 CRITICAL DEBUGGING: Always log current bar info
                self.logger.info(f"🔍 STREAM CHECK:")
                self.logger.info(f"   Current bar time: {bar_time}")
                self.logger.info(f"   Last bar time: {self.last_bar_time}")
                self.logger.info(f"   Current OHLC: O={latest_bar['open']:.2f}, H={latest_bar['high']:.2f}, L={latest_bar['low']:.2f}, C={latest_bar['close']:.2f}")
                self.logger.info(f"   Volume: {latest_bar['tick_volume']}")

                # Check if this is a new bar
                if self.last_bar_time is None or bar_time > self.last_bar_time:
                    # New bar detected
                    self.logger.info(f"✅ NEW BAR DETECTED! {bar_time} - Close: {latest_bar['close']:.2f}")

                    bar_data = {
                        'time': latest_bar['time'],
                        'open': latest_bar['open'],
                        'high': latest_bar['high'],
                        'low': latest_bar['low'],
                        'close': latest_bar['close'],
                        'tick_volume': latest_bar['tick_volume'],
                        'spread': 0.18,  # Default spread
                        'real_volume': latest_bar['tick_volume']
                    }

                    self.price_buffer.append(bar_data)
                    self.last_bar_time = bar_time
                    self.stream_stats['bars_collected'] += 1

                    self.logger.info(f"✅ Added new bar to buffer. Buffer size: {len(self.price_buffer)}")
                else:
                    # Same bar - check if it's updating
                    if len(self.price_buffer) > 0:
                        last_buffered = self.price_buffer[-1]
                        if (last_buffered['close'] != latest_bar['close'] or
                            last_buffered['high'] != latest_bar['high'] or
                            last_buffered['low'] != latest_bar['low']):

                            self.logger.info(f"🔄 UPDATING current bar: Close {last_buffered['close']:.2f} → {latest_bar['close']:.2f}")

                            # Update the current bar in buffer
                            updated_bar_data = {
                                'time': latest_bar['time'],
                                'open': latest_bar['open'],
                                'high': latest_bar['high'],
                                'low': latest_bar['low'],
                                'close': latest_bar['close'],
                                'tick_volume': latest_bar['tick_volume'],
                                'spread': 0.18,
                                'real_volume': latest_bar['tick_volume']
                            }
                            self.price_buffer[-1] = updated_bar_data
                        else:
                            self.logger.debug(f"📊 Same bar, no price changes: {bar_time}")

                # Collect recent ticks for spread/slippage analysis
                self._collect_recent_ticks()

                # Update performance stats
                latency = (time.time() - start_time) * 1000
                self.stream_stats['avg_latency_ms'] = (
                    self.stream_stats['avg_latency_ms'] * 0.9 + latency * 0.1
                )
                self.stream_stats['last_update'] = datetime.now()

                # Sleep until next check
                time.sleep(check_interval)

            except Exception as e:
                self.logger.error(f"🚨 Error in streaming worker: {str(e)}")
                import traceback
                self.logger.error(f"🚨 Traceback: {traceback.format_exc()}")
                self.stream_stats['errors'] += 1
                time.sleep(5)  # Wait before retrying
    
    def _collect_recent_ticks(self):
        """Collect recent tick data for spread analysis."""
        try:
            # Get last few ticks
            ticks = mt5.copy_ticks_from(
                self.symbol,
                datetime.now() - timedelta(seconds=60),  # Last minute
                100  # Max 100 ticks
            )
            
            if ticks is not None and len(ticks) > 0:
                for tick in ticks[-10:]:  # Keep last 10 ticks
                    tick_data = {
                        'time': tick.time,
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'spread': tick.ask - tick.bid
                    }
                    self.tick_buffer.append(tick_data)
                    
                self.stream_stats['ticks_collected'] += len(ticks)
                
        except Exception as e:
            self.logger.debug(f"Tick collection error: {str(e)}")
    
    def get_latest_data(self, bars: int = 100) -> pd.DataFrame:
        """
        Get latest price data as DataFrame.
        
        Args:
            bars: Number of latest bars to return
            
        Returns:
            DataFrame with latest price data
        """
        if len(self.price_buffer) == 0:
            return pd.DataFrame()
        
        # Get latest bars from buffer
        latest_bars = list(self.price_buffer)[-bars:]
        
        # Convert to DataFrame
        df = pd.DataFrame(latest_bars)
        if not df.empty:
            df['datetime'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('datetime', inplace=True)
            df.drop('time', axis=1, inplace=True)
        
        return df
    
    def get_current_price(self) -> Optional[Dict[str, float]]:
        """Get current bid/ask prices."""
        try:
            if not self.is_connected:
                return None
            
            # Get current tick
            tick = mt5.symbol_info_tick(self.symbol)
            if tick is None:
                return None
            
            return {
                'bid': tick.bid,
                'ask': tick.ask,
                'spread': tick.ask - tick.bid,
                'time': tick.time
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get current price: {str(e)}")
            return None
    
    def get_spread_stats(self) -> Dict[str, float]:
        """Get recent spread statistics."""
        if len(self.tick_buffer) == 0:
            return {'avg_spread': 0.18, 'min_spread': 0.15, 'max_spread': 0.25}
        
        spreads = [tick['spread'] for tick in self.tick_buffer if 'spread' in tick]
        
        if not spreads:
            return {'avg_spread': 0.18, 'min_spread': 0.15, 'max_spread': 0.25}
        
        return {
            'avg_spread': np.mean(spreads),
            'min_spread': np.min(spreads),
            'max_spread': np.max(spreads),
            'std_spread': np.std(spreads)
        }
    
    def get_stream_stats(self) -> Dict[str, Any]:
        """Get streaming performance statistics."""
        return {
            **self.stream_stats,
            'buffer_size': len(self.price_buffer),
            'tick_buffer_size': len(self.tick_buffer),
            'is_streaming': self.is_streaming,
            'is_connected': self.is_connected
        }
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
