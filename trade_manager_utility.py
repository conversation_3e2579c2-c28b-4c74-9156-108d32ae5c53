#!/usr/bin/env python3
"""
Trade Manager Utility

Quick utility to manage trades when needed:
- View current positions
- Close all trades
- Close specific trades
- Check trade group status
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

import MetaTrader5 as mt5
from live_trading.trade_execution import TradeExecutionEngine
from datetime import datetime

def connect_mt5():
    """Connect to MT5."""
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        return False
    
    print("✅ Connected to MT5")
    return True

def show_current_positions():
    """Show all current positions."""
    print("\n📊 Current Positions:")
    print("=" * 60)
    
    positions = mt5.positions_get(symbol="XAUUSD!")
    if not positions:
        print("No open positions")
        return
    
    total_volume = 0
    for i, pos in enumerate(positions, 1):
        direction = "BUY" if pos.type == mt5.POSITION_TYPE_BUY else "SELL"
        profit = pos.profit
        comment = pos.comment
        
        # Try to extract trade group info
        group_id = "N/A"
        if 'AI_Trade_' in comment:
            try:
                group_id = comment.split('AI_Trade_')[1]  # Full group ID
            except:
                pass
        
        print(f"{i}. Ticket: {pos.ticket}")
        print(f"   Direction: {direction}")
        print(f"   Volume: {pos.volume} lots")
        print(f"   Entry: {pos.price_open}")
        print(f"   Current: {pos.price_current}")
        print(f"   Profit: ${profit:.2f}")
        print(f"   Comment: {comment}")
        print(f"   Group ID: {group_id}")
        print(f"   Time: {datetime.fromtimestamp(pos.time)}")
        print("-" * 40)
        
        total_volume += pos.volume
    
    print(f"Total Volume: {total_volume} lots")
    print(f"Total Positions: {len(positions)}")

def close_all_positions():
    """Close all positions."""
    print("\n🔄 Closing All Positions...")
    
    config = {
        'symbol': 'XAUUSD!',
        'magic_number': 12345
    }
    
    execution_engine = TradeExecutionEngine(config)
    execution_engine.close_all_trades("Manual_Close_All")
    
    print("✅ Close all command executed")

def analyze_trade_groups():
    """Analyze trade groups."""
    print("\n🔍 Trade Group Analysis:")
    print("=" * 60)
    
    positions = mt5.positions_get(symbol="XAUUSD!")
    if not positions:
        print("No open positions")
        return
    
    groups = {}
    ungrouped = []
    
    for pos in positions:
        comment = pos.comment
        group_id = None
        
        if 'AI_Trade_' in comment:
            try:
                group_id = comment.split('AI_Trade_')[1]  # Full group ID
            except:
                pass
        
        if group_id:
            if group_id not in groups:
                groups[group_id] = []
            groups[group_id].append(pos)
        else:
            ungrouped.append(pos)
    
    print(f"Trade Groups Found: {len(groups)}")
    print(f"Ungrouped Trades: {len(ungrouped)}")
    print()
    
    for group_id, trades in groups.items():
        total_volume = sum(t.volume for t in trades)
        total_profit = sum(t.profit for t in trades)
        directions = [("BUY" if t.type == mt5.POSITION_TYPE_BUY else "SELL") for t in trades]
        
        print(f"Group {group_id}:")
        print(f"  Trades: {len(trades)}")
        print(f"  Volume: {total_volume} lots")
        print(f"  Profit: ${total_profit:.2f}")
        print(f"  Directions: {', '.join(set(directions))}")
        print(f"  Tickets: {[t.ticket for t in trades]}")
        print()
    
    if ungrouped:
        print("Ungrouped Trades:")
        for trade in ungrouped:
            direction = "BUY" if trade.type == mt5.POSITION_TYPE_BUY else "SELL"
            print(f"  {trade.ticket}: {direction} {trade.volume} lots, ${trade.profit:.2f}")

def main():
    """Main menu."""
    if not connect_mt5():
        return
    
    while True:
        print("\n" + "=" * 60)
        print("🛠️  TRADE MANAGER UTILITY")
        print("=" * 60)
        print("1. Show Current Positions")
        print("2. Analyze Trade Groups")
        print("3. Close All Positions")
        print("4. Exit")
        print()
        
        try:
            choice = input("Select option (1-4): ").strip()
            
            if choice == '1':
                show_current_positions()
            elif choice == '2':
                analyze_trade_groups()
            elif choice == '3':
                confirm = input("⚠️  Are you sure you want to close ALL positions? (yes/no): ").strip().lower()
                if confirm == 'yes':
                    close_all_positions()
                else:
                    print("❌ Operation cancelled")
            elif choice == '4':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    mt5.shutdown()

if __name__ == "__main__":
    main()
