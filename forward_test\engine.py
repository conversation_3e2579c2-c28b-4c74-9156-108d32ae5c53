"""
Forward Testing Engine Implementation

Real-time simulation engine for forward testing trading models with
prediction accuracy tracking and performance analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import sys
from pathlib import Path
import time
import threading
from queue import Queue

# Add project root to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin

from .base import BaseForwardTester, ForwardTestResult, RealTimeTrade, ModelPredictionComparison
from .config import ForwardTestConfig
from .performance import ForwardTestPerformanceAnalyzer


class RealTimeForwardTester(BaseForwardTester):
    """
    Real-time forward testing implementation.
    
    Simulates real-time trading conditions with prediction tracking,
    latency simulation, and comprehensive performance analysis.
    """
    
    def __init__(self, config: ForwardTestConfig, ensemble_model=None):
        """
        Initialize real-time forward tester.
        
        Args:
            config: Forward testing configuration
            ensemble_model: Trained ensemble model
        """
        super().__init__(config)
        self.ensemble_model = ensemble_model
        self.performance_analyzer = ForwardTestPerformanceAnalyzer(config)
        
        # Real-time simulation state
        self.is_running = False
        self.current_time = None
        self.prediction_queue = Queue()
        self.trade_queue = Queue()
        
        # Performance tracking
        self.predictions = []
        self.prediction_accuracy = []
        self.real_time_trades = []
        self.latency_measurements = []
        
        # Threading for real-time simulation
        self.prediction_thread = None
        self.execution_thread = None
        
        self.logger.info("Real-time forward tester initialized")

    def validate_data(self, data: pd.DataFrame, features: pd.DataFrame) -> List[str]:
        """
        Validate input data for forward testing.

        Args:
            data: OHLCV data
            features: Feature data

        Returns:
            List of validation errors (empty if valid)
        """
        errors = []

        # Check if data is empty
        if data.empty:
            errors.append("OHLCV data is empty")

        if features.empty:
            errors.append("Features data is empty")

        # Check required columns in OHLCV data
        required_ohlcv_columns = ['open', 'high', 'low', 'close']
        missing_ohlcv_columns = [col for col in required_ohlcv_columns if col not in data.columns]
        if missing_ohlcv_columns:
            errors.append(f"Missing OHLCV columns: {missing_ohlcv_columns}")

        # Check data alignment
        if not data.empty and not features.empty:
            common_index = data.index.intersection(features.index)
            if len(common_index) == 0:
                errors.append("No common timestamps between OHLCV and features data")
            elif len(common_index) < 10:
                errors.append(f"Insufficient aligned data: only {len(common_index)} samples")

        # Check for NaN values
        if data.isnull().any().any():
            errors.append("OHLCV data contains NaN values")

        if features.isnull().any().any():
            errors.append("Features data contains NaN values")

        return errors
    
    def run_forward_test(self, data: pd.DataFrame, features: pd.DataFrame,
                        labels: pd.DataFrame, **kwargs) -> ForwardTestResult:
        """
        Run forward testing with real-time simulation.
        
        Args:
            data: Historical OHLCV data
            features: Engineered features
            labels: Model labels (for comparison)
            **kwargs: Additional parameters
            
        Returns:
            ForwardTestResult with comprehensive results
        """
        start_time = time.time()
        errors = []
        warnings = []
        
        # Validate input data
        validation_errors = self.validate_data(data, features)
        if validation_errors:
            errors.extend(validation_errors)
            return self._create_empty_result(errors, warnings, time.time() - start_time)
        
        try:
            self.logger.info("Starting forward testing with real-time simulation...")
            
            # Initialize simulation
            self._initialize_simulation(data, features, labels)
            
            # Run real-time simulation
            simulation_results = self._run_real_time_simulation(data, features, labels)
            
            # Analyze results
            performance_metrics = self._analyze_forward_test_results()
            
            # Calculate prediction accuracy
            accuracy_metrics = self._calculate_prediction_accuracy()
            
            # Analyze trading performance
            trading_metrics = self._analyze_trading_performance()
            
            # Create result summary
            execution_time = time.time() - start_time
            
            self.logger.info(f"Forward testing completed in {execution_time:.2f}s")
            
            return ForwardTestResult(
                trades=self.real_time_trades,
                prediction_comparisons=self.predictions,
                performance_metrics=performance_metrics,
                model_accuracy_metrics=accuracy_metrics,
                equity_curve=pd.DataFrame(),  # TODO: Implement equity curve
                drawdown_curve=pd.DataFrame(),  # TODO: Implement drawdown curve
                prediction_accuracy_curve=pd.DataFrame(),  # TODO: Implement accuracy curve
                latency_analysis={'measurements': self.latency_measurements},
                real_time_metrics=trading_metrics,
                errors=errors,
                warnings=warnings,
                execution_time=execution_time,
                metadata={
                    'test_type': 'real_time_forward',
                    'total_predictions': len(self.predictions),
                    'total_trades': len(self.real_time_trades),
                    'config': self.config.__dict__
                }
            )
            
        except Exception as e:
            error_msg = f"Forward testing failed: {str(e)}"
            self.logger.error(error_msg)
            errors.append(error_msg)
            return self._create_empty_result(errors, warnings, time.time() - start_time)
    
    def _initialize_simulation(self, data: pd.DataFrame, features: pd.DataFrame,
                             labels: pd.DataFrame):
        """Initialize the real-time simulation environment."""
        self.logger.info("Initializing real-time simulation environment...")
        
        # Reset state
        self.predictions.clear()
        self.real_time_trades.clear()
        self.latency_measurements.clear()
        
        # Set simulation parameters
        self.simulation_start_time = data.index[0]
        self.simulation_end_time = data.index[-1]
        self.current_time = self.simulation_start_time
        
        # Initialize performance tracking
        self.equity = self.config.initial_capital
        self.active_positions = {}
        
        self.logger.info(f"Simulation period: {self.simulation_start_time} to {self.simulation_end_time}")
    
    def _run_real_time_simulation(self, data: pd.DataFrame, features: pd.DataFrame,
                                labels: pd.DataFrame) -> Dict[str, Any]:
        """
        Run the real-time simulation.
        
        Args:
            data: OHLCV data
            features: Feature data
            labels: Label data
            
        Returns:
            Dictionary with simulation results
        """
        self.logger.info("Starting real-time simulation...")
        
        simulation_interval = self.config.simulation_interval_seconds
        prediction_interval = self.config.prediction_interval_seconds
        
        last_prediction_time = None
        processed_samples = 0
        
        # Process data chronologically (simulating real-time)
        for timestamp, row in data.iterrows():
            self.current_time = timestamp
            
            # Check if it's time to make a prediction
            if (last_prediction_time is None or 
                (timestamp - last_prediction_time).total_seconds() >= prediction_interval):
                
                # Get current features and labels
                if timestamp in features.index:
                    current_features = features.loc[timestamp]
                    current_labels = labels.loc[timestamp] if timestamp in labels.index else None
                    
                    # Make prediction with latency simulation
                    prediction = self._make_real_time_prediction(
                        current_features, current_labels, row, timestamp
                    )
                    
                    if prediction:
                        self.predictions.append(prediction)
                        
                        # Check for trading signals
                        should_trade = prediction.model_confidence > self.config.min_confidence_threshold
                        if should_trade:
                            trade = self._execute_real_time_trade(prediction, row, timestamp)
                            if trade:
                                self.real_time_trades.append(trade)
                    
                    last_prediction_time = timestamp
            
            # Update existing positions
            self._update_active_positions(row, timestamp)
            
            # Simulate processing delay
            if self.config.simulate_latency:
                time.sleep(simulation_interval / 1000.0)  # Convert to seconds
            
            processed_samples += 1
            
            # Log progress periodically
            if processed_samples % 100 == 0:
                self.logger.debug(f"Processed {processed_samples} samples, "
                                f"made {len(self.predictions)} predictions, "
                                f"executed {len(self.real_time_trades)} trades")
        
        self.logger.info(f"Real-time simulation completed: {processed_samples} samples processed")
        
        return {
            'processed_samples': processed_samples,
            'total_predictions': len(self.predictions),
            'total_trades': len(self.real_time_trades)
        }
    
    def _make_real_time_prediction(self, features: pd.Series, labels: Optional[pd.Series],
                                 current_data: pd.Series, timestamp: datetime) -> Optional[ModelPredictionComparison]:
        """
        Make a real-time prediction with latency tracking.
        
        Args:
            features: Current features
            labels: Current labels (for comparison)
            current_data: Current OHLCV data
            timestamp: Current timestamp
            
        Returns:
            ModelPredictionComparison object or None
        """
        prediction_start_time = time.time()
        
        try:
            # Prepare features for model prediction
            feature_array = features.values.reshape(1, -1)
            
            # Get model prediction
            if self.ensemble_model:
                model_prediction = self.ensemble_model.predict(feature_array)
                if hasattr(model_prediction, 'values'):
                    pred_values = model_prediction.values[0]
                else:
                    pred_values = model_prediction[0]
            else:
                # Use labels as proxy if no model available
                pred_values = labels.values if labels is not None else np.array([0.5, 15.0, 40.0, 25.0])
            
            # Calculate prediction latency
            prediction_latency = (time.time() - prediction_start_time) * 1000  # milliseconds
            self.latency_measurements.append(prediction_latency)
            
            # Extract prediction components
            signal_probability = pred_values[0] if len(pred_values) > 0 else 0.5
            confidence = abs(signal_probability - 0.5) * 2

            # Determine if should trade
            should_trade = confidence > self.config.min_confidence_threshold

            # Extract prediction values for comparison
            predicted_direction = "long" if pred_values[0] > 0.5 else "short"

            # Determine actual direction from labels if available
            actual_direction = "unknown"
            if labels is not None and len(labels) > 0:
                # Use signal_probability column: >0.5 = long, <0.5 = short
                if 'signal_probability' in labels.index:
                    signal_prob = labels['signal_probability']
                    actual_direction = "long" if signal_prob > 0.5 else "short"
                    self.logger.debug(f"Set actual_direction to {actual_direction} based on signal_prob {signal_prob}")
                else:
                    # Fallback: use first column as signal probability
                    signal_prob = labels.iloc[0]
                    actual_direction = "long" if signal_prob > 0.5 else "short"
                    self.logger.debug(f"Set actual_direction to {actual_direction} based on fallback signal_prob {signal_prob}")
            else:
                self.logger.debug(f"Labels is None or empty, keeping actual_direction as unknown")

            # Helper function to safely convert to float, skipping string values
            def safe_float_convert(value, default=0.0):
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default

            # Create prediction comparison with correct parameters
            prediction = ModelPredictionComparison(
                timestamp=timestamp,
                predicted_direction=predicted_direction,
                actual_direction=actual_direction,
                predicted_tp_distance=safe_float_convert(pred_values[1], 10.0) if len(pred_values) > 1 else 10.0,
                actual_tp_distance=0.0,  # Will be updated when trade closes
                predicted_sl_distance=safe_float_convert(pred_values[3], 15.0) if len(pred_values) > 3 else 15.0,
                actual_sl_distance=0.0,  # Will be updated when trade closes
                predicted_hold_time=safe_float_convert(pred_values[4], 5.0) if len(pred_values) > 4 else 5.0,
                actual_hold_time=0.0,  # Will be updated when trade closes
                prediction_accuracy_score=0.0,  # Will be calculated later
                model_confidence=confidence,
                market_regime_predicted=str(int(safe_float_convert(pred_values[6], 0))) if len(pred_values) > 6 else "0",
                market_regime_actual="unknown"  # Will be determined later
            )
            
            return prediction
            
        except Exception as e:
            self.logger.warning(f"Error making real-time prediction at {timestamp}: {str(e)}")
            return None
    
    def _execute_real_time_trade(self, prediction: ModelPredictionComparison,
                               current_data: pd.Series, timestamp: datetime) -> Optional[RealTimeTrade]:
        """
        Execute a real-time trade based on prediction.
        
        Args:
            prediction: Model prediction
            current_data: Current market data
            timestamp: Current timestamp
            
        Returns:
            RealTimeTrade object or None
        """
        try:
            # Determine trade direction from prediction
            direction = prediction.predicted_direction

            # Calculate position size based on confidence
            base_size = self.config.base_position_size
            confidence_multiplier = prediction.model_confidence
            position_size = base_size * confidence_multiplier
            
            # Apply position limits
            position_size = min(position_size, self.config.max_position_size)
            position_size = max(position_size, self.config.min_position_size)
            
            # Calculate entry price with slippage
            entry_price = current_data['close']
            if self.config.simulate_slippage:
                slippage = self.config.slippage_pips * 0.01
                if direction == 'long':
                    entry_price += slippage
                else:
                    entry_price -= slippage
            
            # Create real-time trade
            trade = RealTimeTrade(
                entry_time=timestamp,
                entry_price=entry_price,
                direction=direction,
                size=position_size,
                model_confidence=prediction.model_confidence,
                decision_latency_ms=50.0  # Simulated latency
            )
            
            # Add to active positions
            self.active_positions[timestamp] = trade
            
            self.logger.debug(f"Executed real-time trade: {direction} {position_size} lots at {entry_price}")
            
            return trade
            
        except Exception as e:
            self.logger.error(f"Error executing real-time trade: {str(e)}")
            return None
    
    def _update_active_positions(self, current_data: pd.Series, timestamp: datetime):
        """Update active positions and check for exits."""
        positions_to_close = []
        
        for entry_time, trade in self.active_positions.items():
            # Simple exit logic - close after certain time or profit/loss threshold
            time_in_trade = (timestamp - trade.entry_time).total_seconds() / 60  # minutes
            current_price = current_data['close']
            
            # Calculate current P&L
            if trade.direction == 'long':
                unrealized_pnl = (current_price - trade.entry_price) * trade.size * 100
            else:
                unrealized_pnl = (trade.entry_price - current_price) * trade.size * 100
            
            # Check exit conditions
            should_exit = False
            exit_reason = ""
            
            # Time-based exit
            if time_in_trade > self.config.max_trade_duration_minutes:
                should_exit = True
                exit_reason = "Time limit reached"
            
            # Profit target
            elif unrealized_pnl > self.config.profit_target_amount:
                should_exit = True
                exit_reason = "Profit target hit"
            
            # Stop loss
            elif unrealized_pnl < -self.config.stop_loss_amount:
                should_exit = True
                exit_reason = "Stop loss hit"
            
            if should_exit:
                # Close position
                trade.exit_time = timestamp
                trade.exit_price = current_price
                trade.exit_reason = exit_reason
                trade.pnl = unrealized_pnl
                
                positions_to_close.append(entry_time)
                
                self.logger.debug(f"Closed position: {exit_reason}, P&L: ${unrealized_pnl:.2f}")
        
        # Remove closed positions
        for entry_time in positions_to_close:
            del self.active_positions[entry_time]
    
    def _analyze_forward_test_results(self) -> Dict[str, float]:
        """Analyze forward test results and calculate performance metrics."""
        if not self.predictions:
            return {}
        
        return self.performance_analyzer.calculate_forward_test_metrics(
            self.predictions, self.real_time_trades, self.latency_measurements
        )
    
    def _calculate_prediction_accuracy(self) -> Dict[str, float]:
        """Calculate prediction accuracy metrics."""
        if not self.predictions:
            return {}
        
        # Calculate accuracy for predictions that have actual labels
        predictions_with_labels = [p for p in self.predictions if p.actual_direction != "unknown"]
        
        if not predictions_with_labels:
            return {'accuracy': 0.0, 'samples_with_labels': 0}
        
        # Simple accuracy calculation (can be enhanced)
        correct_predictions = 0
        total_predictions = len(predictions_with_labels)
        
        for pred in predictions_with_labels:
            # Check if direction prediction was correct
            if pred.direction_correct:
                correct_predictions += 1
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        
        return {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'samples_with_labels': len(predictions_with_labels)
        }
    
    def _analyze_trading_performance(self) -> Dict[str, float]:
        """Analyze trading performance metrics."""
        if not self.real_time_trades:
            return {}
        
        completed_trades = [t for t in self.real_time_trades if t.exit_time is not None]
        
        if not completed_trades:
            return {'completed_trades': 0}
        
        # Calculate basic trading metrics
        total_pnl = sum(trade.pnl for trade in completed_trades)
        winning_trades = sum(1 for trade in completed_trades if trade.pnl > 0)
        losing_trades = len(completed_trades) - winning_trades
        
        win_rate = winning_trades / len(completed_trades)
        avg_trade_duration = np.mean([trade.duration_minutes for trade in completed_trades])
        avg_latency = np.mean(self.latency_measurements) if self.latency_measurements else 0.0
        
        return {
            'total_trades': len(completed_trades),
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_trade_duration_minutes': avg_trade_duration,
            'avg_prediction_latency_ms': avg_latency
        }
    
    def execute_simulated_trade(self, signal: Dict[str, Any], current_data: pd.Series,
                              features: pd.Series) -> Optional[RealTimeTrade]:
        """
        Execute a simulated trade (implementation of abstract method).

        Args:
            signal: Trading signal
            current_data: Current market data
            features: Current features

        Returns:
            RealTimeTrade object or None
        """
        # Create proper ModelPredictionComparison for simulated trade
        # Determine actual direction from features if available (assuming it's in the signal)
        actual_direction = "unknown"
        if 'actual_direction' in signal:
            actual_direction = signal['actual_direction']
        elif hasattr(features, 'iloc') and len(features) > 0:
            # Try to infer from features - this is a fallback
            actual_direction = "long" if features.iloc[0] > 0 else "short"

        prediction = ModelPredictionComparison(
            timestamp=current_data.name,
            predicted_direction="long" if signal.get('confidence', 0.5) > 0.5 else "short",
            actual_direction=actual_direction,
            predicted_tp_distance=signal.get('tp_distance', 10.0),
            actual_tp_distance=0.0,
            predicted_sl_distance=signal.get('sl_distance', 15.0),
            actual_sl_distance=0.0,
            predicted_hold_time=signal.get('hold_time', 5.0),
            actual_hold_time=0.0,
            prediction_accuracy_score=0.0,
            model_confidence=signal.get('confidence', 0.5),
            market_regime_predicted="0",
            market_regime_actual="unknown"
        )

        return self._execute_real_time_trade(
            prediction,
            current_data,
            current_data.name
        )

    def simulate_real_time_decision(self, current_data: pd.Series, features: pd.Series,
                                  labels: Optional[pd.Series]) -> Dict[str, Any]:
        """
        Simulate real-time decision making (implementation of abstract method).

        Args:
            current_data: Current market data
            features: Current features
            labels: Current labels (optional)

        Returns:
            Decision dictionary
        """
        prediction = self._make_real_time_prediction(features, labels, current_data, current_data.name)

        if prediction:
            should_trade = prediction.model_confidence > self.config.min_confidence_threshold
            return {
                'should_trade': should_trade,
                'confidence': prediction.model_confidence,
                'prediction': [prediction.predicted_tp_distance, prediction.predicted_sl_distance],
                'latency_ms': 50.0  # Simulated latency
            }
        else:
            return {
                'should_trade': False,
                'confidence': 0.0,
                'prediction': [0.5],
                'latency_ms': 100.0
            }

    def update_real_time_position(self, current_data: pd.Series, features: pd.Series) -> bool:
        """
        Update real-time positions (implementation of abstract method).

        Args:
            current_data: Current market data
            features: Current features

        Returns:
            True if any positions were updated
        """
        initial_position_count = len(self.active_positions)
        self._update_active_positions(current_data, current_data.name)
        return len(self.active_positions) != initial_position_count

    def _create_empty_result(self, errors: List[str], warnings: List[str],
                           execution_time: float) -> ForwardTestResult:
        """Create empty result for error cases."""
        return ForwardTestResult(
            trades=[],
            prediction_comparisons=[],
            performance_metrics={},
            model_accuracy_metrics={},
            equity_curve=pd.DataFrame(),
            drawdown_curve=pd.DataFrame(),
            prediction_accuracy_curve=pd.DataFrame(),
            latency_analysis={},
            real_time_metrics={},
            errors=errors,
            warnings=warnings,
            execution_time=execution_time,
            metadata={'test_type': 'real_time_forward', 'status': 'failed'}
        )
