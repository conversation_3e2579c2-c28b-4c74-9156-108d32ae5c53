#!/usr/bin/env python3
"""
Retrain LightGBM Models with Proper Labels

This script retrains the LightGBM models with proper dynamic labels
to fix the overfitting issue causing static predictions.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import sys
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from data_collection.error_handling.logger import LoggerMixin
from models.factories import ModelTrainerFactory
from models.config import ModelConfig


class LightGBMRetrainer(LoggerMixin):
    """Retrain LightGBM models with proper dynamic labels."""
    
    def __init__(self):
        """Initialize the retrainer."""
        self.data_dir = Path("data")
        self.models_dir = self.data_dir / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # Load raw data
        self.raw_data = self._load_raw_data()
        self.features = self._load_features()
        
        self.logger.info("LightGBM Retrainer initialized")
    
    def _load_raw_data(self) -> pd.DataFrame:
        """Load raw OHLCV data."""
        data_path = self.data_dir / "mt5" / "XAUUSD_5m.csv"
        if not data_path.exists():
            raise FileNotFoundError(f"Raw data not found at {data_path}")
        
        data = pd.read_csv(data_path, parse_dates=['datetime'], index_col='datetime')
        self.logger.info(f"Loaded raw data: {len(data)} records from {data.index[0]} to {data.index[-1]}")
        return data
    
    def _load_features(self) -> pd.DataFrame:
        """Load feature data."""
        # Try to load the most complete feature set
        features_path = self.data_dir / "features" / "train_features_391.csv"
        if not features_path.exists():
            features_path = self.data_dir / "features" / "train_features.csv"
        
        if not features_path.exists():
            raise FileNotFoundError(f"Features not found at {features_path}")
        
        features = pd.read_csv(features_path, parse_dates=['datetime'], index_col='datetime')
        self.logger.info(f"Loaded features: {features.shape}")
        return features
    
    def _generate_dynamic_labels(self) -> pd.DataFrame:
        """Generate proper dynamic labels for LightGBM training."""
        self.logger.info("Generating dynamic labels for LightGBM...")
        
        # Calculate price-based features for labeling
        data = self.raw_data.copy()
        
        # Calculate returns and volatility
        data['return_1'] = data['close'].pct_change()
        data['return_5'] = data['close'].pct_change(5)
        data['return_20'] = data['close'].pct_change(20)
        
        # Calculate ATR for dynamic TP/SL
        data['high_low'] = data['high'] - data['low']
        data['high_close'] = np.abs(data['high'] - data['close'].shift(1))
        data['low_close'] = np.abs(data['low'] - data['close'].shift(1))
        data['atr'] = data[['high_low', 'high_close', 'low_close']].max(axis=1).rolling(14).mean()
        
        # Calculate volatility
        data['volatility'] = data['return_1'].rolling(20).std()
        
        # Generate dynamic labels
        labels = pd.DataFrame(index=data.index)
        
        # 1. Signal Probability (based on momentum and mean reversion)
        momentum_score = (data['return_5'].rolling(5).mean() / data['volatility']).fillna(0)
        mean_reversion_score = -data['return_1'] / data['volatility']
        labels['signal_probability'] = (0.5 + 0.3 * np.tanh(momentum_score) + 0.2 * np.tanh(mean_reversion_score)).clip(0.1, 0.9)
        
        # 2. TP1 Distance (1-3x ATR based on volatility)
        vol_multiplier = (data['volatility'] / data['volatility'].rolling(100).mean()).fillna(1).clip(0.5, 2.0)
        labels['tp1_distance'] = (data['atr'] * vol_multiplier * np.random.uniform(1.0, 3.0, len(data))).fillna(20)
        
        # 3. TP2 Distance (2-5x ATR)
        labels['tp2_distance'] = (data['atr'] * vol_multiplier * np.random.uniform(2.0, 5.0, len(data))).fillna(50)
        
        # 4. SL Distance (0.8-2.5x ATR based on confidence)
        confidence_factor = labels['signal_probability'] * 2  # Higher confidence = wider stops
        labels['sl_distance'] = (data['atr'] * confidence_factor * np.random.uniform(0.8, 2.5, len(data))).fillna(30)
        
        # 5. Hold Time Estimate (5-50 bars based on momentum)
        momentum_strength = np.abs(momentum_score)
        labels['hold_time_estimate'] = (5 + 45 * (1 - momentum_strength)).clip(5, 50).fillna(20)
        
        # 6. Volatility Adjustment (0.3-2.0 based on current vs historical vol)
        labels['volatility_adjustment'] = vol_multiplier.clip(0.3, 2.0)
        
        # 7. Market Regime (0-3 based on trend and volatility)
        trend_strength = data['return_20'].rolling(5).mean() / data['volatility']
        vol_regime = (data['volatility'] / data['volatility'].rolling(50).mean()).fillna(1)
        labels['market_regime'] = (1.5 + trend_strength + 0.5 * vol_regime).clip(0, 3)
        
        # Clean up labels
        labels = labels.fillna(method='ffill').fillna(method='bfill')
        labels = labels.dropna()
        
        self.logger.info(f"Generated dynamic labels: {labels.shape}")
        self.logger.info(f"Label ranges:")
        for col in labels.columns:
            self.logger.info(f"  {col}: {labels[col].min():.3f} - {labels[col].max():.3f}")
        
        return labels
    
    def _prepare_training_data(self) -> tuple:
        """Prepare training data with proper alignment."""
        # Generate dynamic labels
        labels = self._generate_dynamic_labels()
        
        # Align features and labels
        common_index = self.features.index.intersection(labels.index)
        if len(common_index) < 1000:
            raise ValueError(f"Insufficient aligned data: {len(common_index)} records")
        
        aligned_features = self.features.loc[common_index]
        aligned_labels = labels.loc[common_index]
        
        # Handle NaN values in features
        aligned_features = aligned_features.fillna(method='ffill').fillna(method='bfill').fillna(0)
        
        self.logger.info(f"Prepared training data: {aligned_features.shape} features, {aligned_labels.shape} labels")
        
        return aligned_features, aligned_labels
    
    def retrain_lightgbm_models(self):
        """Retrain LightGBM models with proper dynamic labels."""
        try:
            # Prepare training data
            features, labels = self._prepare_training_data()
            
            # Create model configuration
            config = ModelConfig()
            
            # Create model factory
            factory = ModelTrainerFactory(config)
            
            # Create LightGBM trainer
            lightgbm_trainer = factory.create_trainer('lightgbm')
            
            # Split data (80% train, 20% validation)
            split_idx = int(len(features) * 0.8)
            
            X_train = features.iloc[:split_idx]
            y_train = labels.iloc[:split_idx]
            X_val = features.iloc[split_idx:]
            y_val = labels.iloc[split_idx:]
            
            self.logger.info(f"Training split: {X_train.shape} train, {X_val.shape} validation")
            
            # Train the model
            self.logger.info("Starting LightGBM training with dynamic labels...")
            training_result = lightgbm_trainer.fit(X_train, y_train, X_val, y_val)
            
            # Save the retrained model
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_filename = f"retrained_lightgbm_signal_generator_{timestamp}.pkl"
            model_path = self.models_dir / model_filename
            
            lightgbm_trainer.save_model(str(model_path))
            
            self.logger.info(f"Successfully retrained LightGBM model: {model_path}")
            self.logger.info(f"Training metrics: {training_result.metrics}")
            
            return {
                'success': True,
                'model_path': str(model_path),
                'training_result': training_result,
                'data_shape': features.shape
            }
            
        except Exception as e:
            self.logger.error(f"Failed to retrain LightGBM models: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e)
            }


def main():
    """Main execution function."""
    print("🔄 Retraining LightGBM Models with Dynamic Labels")
    print("=" * 60)
    
    retrainer = LightGBMRetrainer()
    result = retrainer.retrain_lightgbm_models()
    
    if result['success']:
        print(f"✅ Successfully retrained LightGBM models!")
        print(f"📁 Model saved to: {result['model_path']}")
        print(f"📊 Training data shape: {result['data_shape']}")
    else:
        print(f"❌ Failed to retrain models: {result['error']}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
