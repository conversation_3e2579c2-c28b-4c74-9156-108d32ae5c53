"""
Fixed Feature Alignment System for Live Trading

This version properly handles the feature mismatch between live trading
and training data, ensuring exactly 354 features are produced.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Set, Optional
from pathlib import Path
import logging
import json

class FixedFeatureAlignmentSystem:
    """
    Fixed system to align live trading features with training dataset features.

    Ensures that live trading generates exactly the same 354 features that
    the specialized ensemble models were trained on.
    """

    def __init__(self, training_features_path: str = "data/features/train_features_391.csv"):
        """
        Initialize feature alignment system.

        Args:
            training_features_path: Path to training features CSV file
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.training_features_path = training_features_path
        self.expected_features = None
        self.feature_mapping = {}
        self.default_features = {}
        self.load_expected_features()
        self.load_mappings()

    def load_expected_features(self):
        """Load expected features from training dataset."""
        try:
            if Path(self.training_features_path).exists():
                df = pd.read_csv(self.training_features_path)
                # Exclude datetime column
                self.expected_features = [col for col in df.columns if col != 'datetime']
                self.logger.info(f"Loaded {len(self.expected_features)} expected features from training dataset")
            else:
                self.logger.error(f"Training features file not found: {self.training_features_path}")
                self.expected_features = []
        except Exception as e:
            self.logger.error(f"Error loading expected features: {e}")
            self.expected_features = []

    def load_mappings(self):
        """Load feature mappings and defaults."""
        try:
            # Create basic feature mapping for common mismatches
            self.feature_mapping = {
                # Volume regime mappings
                'vol_technical_regime_10': 'vol_regime_10',
                'vol_technical_regime_20': 'vol_regime_20',
                'vol_technical_regime_50': 'vol_regime_50',

                # Remove features that don't exist in training (set to None)
                'session_asian': None,
                'session_european': None,
                'session_us': None,
                'overlap_asian_european': None,
                'overlap_european_us': None,
                'hour_of_day': None,
                'day_of_week': None,
                'day_of_month': None,
                'month_of_year': None,
                'quarter_of_year': None,
                'is_weekend': None,
                'is_month_end': None,
                'is_quarter_end': None,
                'is_year_end': None,
                'is_holiday': None,
                'vwap_10': None,
                'vwap_20': None,
                'vwap_50': None,
                'obv': None,
                'volume_z_score': None,
                'volume_spike': None,
                'time_momentum': None,
                'time_mean_reversion': None,
            }

            # Default values for missing features
            self.default_features = {
                'vol_regime_10': 0.0,
                'vol_regime_20.1': 0.0,
                'vol_regime_50.1': 0.0,
                'market_regime_cluster': 0.0,
                'regime_stability': 0.5,
                'regime_transition_probability': 0.5,
                'regime_divergence': 0.0,
            }

            self.logger.info(f"Loaded {len(self.feature_mapping)} feature mappings")

        except Exception as e:
            self.logger.error(f"Error loading mappings: {e}")

    def align_features(self, live_features: pd.DataFrame, market_data: pd.DataFrame = None) -> pd.DataFrame:
        """
        Align live trading features with training dataset features.

        Args:
            live_features: DataFrame with live trading features
            market_data: Raw market data for filling missing OHLCV features

        Returns:
            DataFrame with aligned features (354 features matching training)
        """
        if self.expected_features is None:
            self.logger.error("Expected features not loaded")
            return live_features

        self.logger.info(f"Aligning features: {live_features.shape[1]} live -> {len(self.expected_features)} expected")

        # Start with empty aligned features
        aligned_features = pd.DataFrame(index=live_features.index)

        # Apply feature mapping and selection
        for expected_feature in self.expected_features:
            if expected_feature in live_features.columns:
                # Direct match
                aligned_features[expected_feature] = live_features[expected_feature]
            else:
                # Check if there's a mapping from live feature to expected feature
                mapped_from = None
                for live_name, expected_name in self.feature_mapping.items():
                    if expected_name == expected_feature and live_name in live_features.columns:
                        mapped_from = live_name
                        break

                if mapped_from:
                    # Use mapped feature
                    aligned_features[expected_feature] = live_features[mapped_from]
                    self.logger.debug(f"Mapped {mapped_from} -> {expected_feature}")
                else:
                    # Feature missing - use appropriate default
                    default_value = self.get_default_value(expected_feature, market_data)
                    aligned_features[expected_feature] = default_value
                    if default_value == 0.0:
                        self.logger.debug(f"Missing feature filled with zeros: {expected_feature}")

        # Log alignment results
        missing_count = (aligned_features == 0.0).all().sum()
        self.logger.info(f"Feature alignment complete: {aligned_features.shape[1]} features, {missing_count} filled with defaults")

        return aligned_features

    def get_default_value(self, feature_name: str, market_data: pd.DataFrame = None) -> float:
        """Get appropriate default value for missing feature."""

        # If we have market data, try to extract OHLCV features
        if market_data is not None and not market_data.empty:
            if feature_name == 'open':
                return market_data['open'].iloc[-1] if 'open' in market_data.columns else 0.0
            elif feature_name == 'high':
                return market_data['high'].iloc[-1] if 'high' in market_data.columns else 0.0
            elif feature_name == 'low':
                return market_data['low'].iloc[-1] if 'low' in market_data.columns else 0.0
            elif feature_name == 'close':
                return market_data['close'].iloc[-1] if 'close' in market_data.columns else 0.0
            elif feature_name == 'volume':
                return market_data['volume'].iloc[-1] if 'volume' in market_data.columns else 1000.0
            elif feature_name in ['high.1', 'high.2']:
                return market_data['high'].iloc[-2] if len(market_data) > 1 and 'high' in market_data.columns else 0.0
            elif feature_name in ['volume.1', 'volume.2']:
                return market_data['volume'].iloc[-2] if len(market_data) > 1 and 'volume' in market_data.columns else 1000.0

        # Use predefined defaults
        if feature_name in self.default_features:
            return self.default_features[feature_name]

        # Fallback defaults based on feature type
        if 'regime' in feature_name:
            return 0.5  # Neutral regime
        elif 'vol_' in feature_name:
            return 0.0  # Neutral volatility
        else:
            return 0.0  # General default

    def validate_alignment(self, aligned_features: pd.DataFrame) -> Dict[str, any]:
        """
        Validate that aligned features match training expectations.

        Args:
            aligned_features: Aligned features DataFrame

        Returns:
            Validation results dictionary
        """
        results = {
            'feature_count_match': len(aligned_features.columns) == len(self.expected_features),
            'feature_names_match': set(aligned_features.columns) == set(self.expected_features),
            'missing_features': set(self.expected_features) - set(aligned_features.columns),
            'extra_features': set(aligned_features.columns) - set(self.expected_features),
            'zero_filled_features': [],
            'alignment_score': 0.0
        }

        # Check for zero-filled features
        for col in aligned_features.columns:
            if (aligned_features[col] == 0.0).all():
                results['zero_filled_features'].append(col)

        # Calculate alignment score
        if results['feature_count_match'] and results['feature_names_match']:
            zero_filled_ratio = len(results['zero_filled_features']) / len(self.expected_features)
            results['alignment_score'] = 1.0 - zero_filled_ratio

        return results

    def get_feature_statistics(self) -> Dict[str, any]:
        """Get statistics about feature alignment system."""
        return {
            'expected_feature_count': len(self.expected_features) if self.expected_features else 0,
            'mapping_count': len(self.feature_mapping),
            'default_feature_count': len(self.default_features),
            'system_ready': self.expected_features is not None and len(self.expected_features) > 0
        }