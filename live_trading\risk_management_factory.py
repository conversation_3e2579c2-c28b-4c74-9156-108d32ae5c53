#!/usr/bin/env python3
"""
Risk Management Factory

Factory pattern implementation for creating risk management components
following clean architecture principles and ensuring consistent configuration.

Components:
- HierarchicalDecisionFramework
- FiveLayerRiskManager
- SharedRiskContext
- SpecializedWeightSystem
- ModelDrivenExecutionEngine
"""

from typing import Dict, Any, Optional, Union
from enum import Enum
import logging
from datetime import datetime

try:
    from utils.logging_utils import LoggerMixin
except ImportError:
    # Fallback for missing utils
    class LoggerMixin:
        def __init__(self):
            self.logger = logging.getLogger(self.__class__.__name__)


class RiskComponentType(Enum):
    """Enumeration of available risk management component types."""
    HIERARCHICAL_DECISION = "hierarchical_decision"
    FIVE_LAYER_RISK = "five_layer_risk"
    SHARED_RISK_CONTEXT = "shared_risk_context"
    SPECIALIZED_WEIGHTS = "specialized_weights"
    MODEL_DRIVEN_EXECUTION = "model_driven_execution"
    LIVE_RISK_MANAGER = "live_risk_manager"  # ENHANCED: Legacy compatibility


class RiskManagementFactory(LoggerMixin):
    """
    Factory for creating risk management components.
    
    Follows the established factory pattern used throughout the system
    for consistent component creation and configuration.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize risk management factory.
        
        Args:
            config: Risk management configuration
        """
        super().__init__()
        self.config = config
        self._components: Dict[RiskComponentType, Any] = {}
        
        # Registry of available component types
        self._component_registry: Dict[RiskComponentType, type] = {}
        self._register_components()
        
        # Shared risk context (singleton)
        self._shared_risk_context = None
        
        self.logger.info("Risk management factory initialized")
    
    def _register_components(self):
        """Register available risk management component types."""
        
        # Register HierarchicalDecisionFramework
        try:
            from .hierarchical_decision import HierarchicalDecisionFramework
            self._component_registry[RiskComponentType.HIERARCHICAL_DECISION] = HierarchicalDecisionFramework
        except ImportError as e:
            self.logger.warning(f"HierarchicalDecisionFramework not available: {e}")
        
        # Register FiveLayerRiskManager
        try:
            from .five_layer_risk import FiveLayerRiskManager
            self._component_registry[RiskComponentType.FIVE_LAYER_RISK] = FiveLayerRiskManager
        except ImportError as e:
            self.logger.warning(f"FiveLayerRiskManager not available: {e}")
        
        # Register SharedRiskContext
        try:
            from .shared_risk_context import get_shared_risk_context
            self._component_registry[RiskComponentType.SHARED_RISK_CONTEXT] = get_shared_risk_context
        except ImportError as e:
            self.logger.warning(f"SharedRiskContext not available: {e}")
        
        # Register SpecializedWeightSystem
        try:
            from .specialized_weights import SpecializedWeightSystem
            self._component_registry[RiskComponentType.SPECIALIZED_WEIGHTS] = SpecializedWeightSystem
        except ImportError as e:
            self.logger.warning(f"SpecializedWeightSystem not available: {e}")
        
        # Register ModelDrivenExecutionEngine
        try:
            from .model_driven_execution import ModelDrivenExecutionEngine
            self._component_registry[RiskComponentType.MODEL_DRIVEN_EXECUTION] = ModelDrivenExecutionEngine
        except ImportError as e:
            self.logger.warning(f"ModelDrivenExecutionEngine not available: {e}")

        # ENHANCED: Register LiveRiskManager for backward compatibility and balance management
        try:
            from .risk_manager import LiveRiskManager
            self._component_registry[RiskComponentType.LIVE_RISK_MANAGER] = LiveRiskManager
        except ImportError as e:
            self.logger.warning(f"LiveRiskManager not available: {e}")

        self.logger.info(f"Registered {len(self._component_registry)} risk management components")
    
    def create_component(self, component_type: Union[RiskComponentType, str], **kwargs) -> Any:
        """
        Create a risk management component for the specified type.
        
        Args:
            component_type: Type of component to create
            **kwargs: Additional parameters for component creation
            
        Returns:
            Risk management component instance
            
        Raises:
            ValueError: If component creation fails
        """
        if isinstance(component_type, str):
            try:
                component_type = RiskComponentType(component_type.lower())
            except ValueError:
                raise ValueError(f"Unknown risk component type: {component_type}")
        
        # Special handling for shared risk context (singleton)
        if component_type == RiskComponentType.SHARED_RISK_CONTEXT:
            return self._get_shared_risk_context()
        
        # Return cached component if available
        if component_type in self._components:
            return self._components[component_type]
        
        try:
            component = self._create_component_instance(component_type, **kwargs)
            self._components[component_type] = component
            self.logger.info(f"✓ Created {component_type.value} component")
            return component
            
        except Exception as e:
            error_msg = f"Failed to create {component_type.value} component: {str(e)}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
    
    def _create_component_instance(self, component_type: RiskComponentType, **kwargs) -> Any:
        """Create the actual component instance."""
        if component_type not in self._component_registry:
            available_types = list(self._component_registry.keys())
            raise ValueError(f"Component type {component_type.value} not available. Available: {[t.value for t in available_types]}")
        
        component_class = self._component_registry[component_type]
        
        # Merge config with kwargs
        component_config = {
            **self.config,
            **kwargs
        }
        
        # Create component with configuration
        return component_class(component_config)
    
    def _get_shared_risk_context(self):
        """Get or create shared risk context (singleton)."""
        if self._shared_risk_context is None:
            if RiskComponentType.SHARED_RISK_CONTEXT in self._component_registry:
                get_context_func = self._component_registry[RiskComponentType.SHARED_RISK_CONTEXT]
                self._shared_risk_context = get_context_func()
                self.logger.info("✓ Created shared risk context (singleton)")
            else:
                self.logger.warning("SharedRiskContext not available")
                return None
        
        return self._shared_risk_context
    
    def create_complete_risk_system(self) -> Dict[str, Any]:
        """
        Create a complete risk management system with all components.
        
        Returns:
            Dictionary containing all risk management components
        """
        self.logger.info("🏗️ Creating complete risk management system")
        
        risk_system = {}
        
        try:
            # Create shared risk context first (other components depend on it)
            shared_context = self.create_component(RiskComponentType.SHARED_RISK_CONTEXT)
            if shared_context:
                risk_system['shared_risk_context'] = shared_context
                self.logger.info("✓ Shared risk context created")
            
            # Create hierarchical decision framework
            hierarchical_decision = self.create_component(RiskComponentType.HIERARCHICAL_DECISION)
            risk_system['hierarchical_decision'] = hierarchical_decision
            self.logger.info("✓ Hierarchical decision framework created")
            
            # Create five-layer risk manager
            five_layer_risk = self.create_component(RiskComponentType.FIVE_LAYER_RISK)
            risk_system['five_layer_risk'] = five_layer_risk
            self.logger.info("✓ Five-layer risk manager created")
            
            # Create specialized weight system
            weight_system = self.create_component(RiskComponentType.SPECIALIZED_WEIGHTS)
            risk_system['weight_system'] = weight_system
            self.logger.info("✓ Specialized weight system created")
            
            # Create model-driven execution engine
            execution_engine = self.create_component(RiskComponentType.MODEL_DRIVEN_EXECUTION)
            risk_system['execution_engine'] = execution_engine
            self.logger.info("✓ Model-driven execution engine created")

            # ENHANCED: Create legacy risk manager for backward compatibility
            legacy_risk_manager = self.create_component(RiskComponentType.LIVE_RISK_MANAGER)
            risk_system['legacy_risk_manager'] = legacy_risk_manager
            self.logger.info("✓ Legacy risk manager created for backward compatibility")

            self.logger.info(f"🎯 Complete risk management system created with {len(risk_system)} components")

            return risk_system
            
        except Exception as e:
            error_msg = f"Failed to create complete risk system: {str(e)}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
    
    def get_available_components(self) -> list:
        """Get list of available risk component types."""
        return [component_type.value for component_type in self._component_registry.keys()]

    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate risk management configuration.

        Returns:
            Dictionary with validation results
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }

        # Check required configuration parameters
        required_params = [
            'min_signal_confidence',
            'high_confidence_threshold',
            'ensemble_consensus_threshold'
        ]

        for param in required_params:
            if param not in self.config:
                validation_result['errors'].append(f"Missing required parameter: {param}")
                validation_result['valid'] = False

        # Check parameter ranges
        if 'min_signal_confidence' in self.config:
            if not 0.0 <= self.config['min_signal_confidence'] <= 1.0:
                validation_result['errors'].append("min_signal_confidence must be between 0.0 and 1.0")
                validation_result['valid'] = False

        if 'high_confidence_threshold' in self.config:
            if not 0.0 <= self.config['high_confidence_threshold'] <= 1.0:
                validation_result['errors'].append("high_confidence_threshold must be between 0.0 and 1.0")
                validation_result['valid'] = False

        if 'ensemble_consensus_threshold' in self.config:
            if not 0.0 <= self.config['ensemble_consensus_threshold'] <= 1.0:
                validation_result['errors'].append("ensemble_consensus_threshold must be between 0.0 and 1.0")
                validation_result['valid'] = False

        # Check component availability
        if len(self._component_registry) == 0:
            validation_result['warnings'].append("No risk management components registered")

        return validation_result

    def create_unified_risk_manager(self) -> 'UnifiedRiskManager':
        """
        Create a unified risk manager that bridges old and new systems.

        Returns:
            UnifiedRiskManager instance that provides all functionality
        """
        return UnifiedRiskManager(self)


class UnifiedRiskManager(LoggerMixin):
    """
    Unified Risk Manager that bridges old and new risk management systems.

    This class provides a single interface that combines:
    - Legacy LiveRiskManager functionality (balance management, trade validation)
    - New factory-based risk components (hierarchical decisions, five-layer risk)
    - Shared risk context for component communication

    This ensures backward compatibility while enabling new advanced features.
    """

    def __init__(self, factory: RiskManagementFactory):
        """
        Initialize unified risk manager.

        Args:
            factory: Risk management factory instance
        """
        super().__init__()
        self.factory = factory

        # Create all risk components
        self.risk_system = factory.create_complete_risk_system()

        # Extract key components for easy access
        self.legacy_risk_manager = self.risk_system.get('legacy_risk_manager')
        self.hierarchical_decision = self.risk_system.get('hierarchical_decision')
        self.five_layer_risk = self.risk_system.get('five_layer_risk')
        self.shared_risk_context = self.risk_system.get('shared_risk_context')

        self.logger.info("🔗 UnifiedRiskManager initialized with complete risk system")

    # LEGACY API COMPATIBILITY - Delegate to LiveRiskManager
    def validate_trade_risk(self, signal: Dict[str, Any], current_balance: float, active_trades: list) -> Dict[str, Any]:
        """Legacy API: Validate trade risk using comprehensive system."""
        if self.legacy_risk_manager:
            # Get legacy validation
            legacy_result = self.legacy_risk_manager.validate_trade_risk(signal, current_balance, active_trades)

            # ENHANCED: Add new system validation
            if self.hierarchical_decision and legacy_result.get('approved', False):
                # Additional validation through hierarchical decision framework
                try:
                    # This would require adapting the signal format - for now, preserve legacy behavior
                    self.logger.debug("Legacy validation passed, hierarchical decision validation could be added here")
                except Exception as e:
                    self.logger.warning(f"Hierarchical decision validation failed: {e}")

            return legacy_result
        else:
            self.logger.error("Legacy risk manager not available")
            return {'approved': False, 'reason': 'Risk manager not available'}

    def can_open_position(self, signal: Dict[str, Any]) -> bool:
        """Legacy API: Check if position can be opened."""
        if self.legacy_risk_manager:
            return self.legacy_risk_manager.can_open_position(signal)
        else:
            self.logger.error("Legacy risk manager not available")
            return False

    def update_trade_result(self, trade):
        """Legacy API: Update trade result."""
        if self.legacy_risk_manager:
            self.legacy_risk_manager.update_trade_result(trade)
        else:
            self.logger.error("Legacy risk manager not available for trade result update")

    # NEW API - Enhanced functionality
    def comprehensive_risk_assessment(self, trade_signal: Dict[str, Any],
                                    current_positions: list,
                                    portfolio_metrics: Dict[str, Any],
                                    market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive risk assessment using all available systems.

        Returns:
            Combined risk assessment from all systems
        """
        assessment_results = {}

        # Legacy risk assessment
        if self.legacy_risk_manager:
            try:
                current_balance = self.legacy_risk_manager.refresh_balance()
                legacy_assessment = self.legacy_risk_manager.validate_trade_risk(
                    trade_signal, current_balance, current_positions
                )
                assessment_results['legacy'] = legacy_assessment
            except Exception as e:
                self.logger.error(f"Legacy risk assessment failed: {e}")
                assessment_results['legacy'] = {'approved': False, 'reason': f'Legacy assessment error: {e}'}

        # Five-layer risk assessment
        if self.five_layer_risk:
            try:
                five_layer_result = self.five_layer_risk.assess_comprehensive_risk(
                    trade_signal, current_positions, portfolio_metrics, market_data
                )
                assessment_results['five_layer'] = {
                    'approved': five_layer_result.final_action.value not in ['EMERGENCY_STOP', 'CLOSE_POSITIONS'],
                    'risk_level': five_layer_result.overall_risk_level.value,
                    'risk_score': five_layer_result.overall_risk_score,
                    'position_size_adjustment': five_layer_result.position_size_adjustment,
                    'reasoning': five_layer_result.reasoning
                }
            except Exception as e:
                self.logger.error(f"Five-layer risk assessment failed: {e}")
                assessment_results['five_layer'] = {'approved': False, 'reason': f'Five-layer assessment error: {e}'}

        # Combine results - most restrictive wins
        final_approved = all(result.get('approved', False) for result in assessment_results.values())

        return {
            'approved': final_approved,
            'assessments': assessment_results,
            'final_reasoning': 'Combined assessment from all risk systems'
        }

    def get_risk_metrics(self) -> Dict[str, Any]:
        """Get comprehensive risk metrics from all systems."""
        metrics = {}

        if self.legacy_risk_manager:
            metrics['legacy_metrics'] = self.legacy_risk_manager.risk_metrics

        if self.shared_risk_context:
            metrics['shared_context'] = self.shared_risk_context.get_risk_summary()

        return metrics
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate risk management configuration.
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'config_summary': {}
        }
        
        # Check required configuration keys
        required_keys = [
            'min_signal_confidence',
            'high_confidence_threshold',
            'ensemble_consensus_threshold'
        ]
        
        for key in required_keys:
            if key not in self.config:
                validation_results['errors'].append(f"Missing required config key: {key}")
                validation_results['valid'] = False
            else:
                validation_results['config_summary'][key] = self.config[key]
        
        # Check configuration value ranges
        if 'min_signal_confidence' in self.config:
            if not 0.0 <= self.config['min_signal_confidence'] <= 1.0:
                validation_results['errors'].append("min_signal_confidence must be between 0.0 and 1.0")
                validation_results['valid'] = False
        
        if 'high_confidence_threshold' in self.config:
            if not 0.0 <= self.config['high_confidence_threshold'] <= 1.0:
                validation_results['errors'].append("high_confidence_threshold must be between 0.0 and 1.0")
                validation_results['valid'] = False
        
        # Check component availability
        available_components = len(self._component_registry)
        total_components = len(RiskComponentType)
        
        if available_components < total_components:
            missing_components = total_components - available_components
            validation_results['warnings'].append(f"{missing_components} risk components not available")
        
        validation_results['available_components'] = available_components
        validation_results['total_components'] = total_components
        
        return validation_results
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system status and health."""
        return {
            'factory_initialized': True,
            'available_components': self.get_available_components(),
            'created_components': list(self._components.keys()),
            'shared_context_active': self._shared_risk_context is not None,
            'timestamp': datetime.now().isoformat()
        }
