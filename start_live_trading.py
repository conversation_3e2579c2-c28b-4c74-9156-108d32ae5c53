#!/usr/bin/env python3
"""
XAUUSD AI Live Trading System
============================

Main entry point for the live trading system with comprehensive
model-driven trading capabilities for XAUUSD!

Features:
- Real-time trading with MetaTrader 5 integration
- Paper trading for risk-free testing
- High-speed simulation for validation
- Model-driven entry/exit optimization
- Multi-tier TP/SL system
- Comprehensive risk management

Usage Examples:
    # Start paper trading (recommended for testing)
    python start_live_trading.py --mode paper

    # Run simulation with historical data
    python start_live_trading.py --mode simulation --steps 1000

    # Start live trading (use with caution!)
    python start_live_trading.py --mode live

    # Interactive monitoring mode
    python start_live_trading.py --mode paper --interactive

Author: AI Trading System
Version: 1.0.0
"""

import argparse
import sys
import time
import signal
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from live_trading.factories import LiveTradingOrchestrator, LiveTradingType
from live_trading.config import LiveTradingConfig
from data_collection.error_handling.logger import get_logger
from models.specialized_ensemble import SpecializedEnsembleOrchestrator
import joblib


class LiveTradingLauncher:
    """Main launcher for the live trading system."""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.orchestrator = None
        self.specialized_ensemble = None
        self.is_running = False

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.stop_trading()
    
    def start_trading(self, mode: str, config_file: str = None, interactive: bool = False) -> bool:
        """
        Start the live trading system.
        
        Args:
            mode: Trading mode ('live', 'paper', 'simulation')
            config_file: Path to configuration file
            interactive: Whether to run in interactive mode
            
        Returns:
            True if started successfully, False otherwise
        """
        try:
            self._print_banner()
            
            # Load configuration
            config = self._load_configuration(config_file, mode)
            if not config:
                return False

            # Load specialized ensemble
            if not self._load_specialized_ensemble():
                self.logger.error("Failed to load specialized ensemble models")
                return False
            
            # Map mode to trading type
            trading_type_map = {
                'live': LiveTradingType.REAL_TIME,
                'paper': LiveTradingType.PAPER_TRADING,
                'simulation': LiveTradingType.SIMULATION
            }
            
            trading_type = trading_type_map.get(mode)
            if not trading_type:
                self.logger.error(f"Invalid trading mode: {mode}")
                return False
            
            # Create orchestrator
            self.orchestrator = LiveTradingOrchestrator(config)
            
            # Start trading system
            start_result = self.orchestrator.start_live_trading(trading_type)
            
            if not start_result.success:
                self.logger.error(f"Failed to start trading system: {start_result.message}")
                return False
            
            self.is_running = True
            
            # Log startup information
            self._log_startup_info(mode, config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start trading system: {str(e)}")
            return False
    
    def stop_trading(self) -> bool:
        """Stop the live trading system."""
        try:
            if not self.is_running or not self.orchestrator:
                self.logger.info("Trading system is not running")
                return True
            
            self.logger.info("🛑 Stopping Live Trading System...")
            
            # Stop orchestrator
            stop_result = self.orchestrator.stop_live_trading()
            
            if stop_result.success:
                self.logger.info("✅ Live Trading System Stopped Successfully")
                
                # Log final performance summary
                if stop_result.data:
                    self._log_final_summary(stop_result.data)
                
                self.is_running = False
                return True
            else:
                self.logger.error(f"Error stopping trading system: {stop_result.message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")
            return False
    
    def run_interactive_mode(self, mode: str, config_file: str = None):
        """Run in interactive mode with monitoring."""
        if not self.start_trading(mode, config_file, interactive=True):
            return
        
        try:
            self.logger.info("🔄 Entering interactive monitoring mode")
            self.logger.info("Press Ctrl+C to stop the system")
            
            while self.is_running:
                try:
                    # Show status every 5 minutes
                    self._show_status()
                    
                    # Sleep and check for stop condition
                    for _ in range(300):  # 5 minutes = 300 seconds
                        if not self.is_running:
                            break
                        time.sleep(1)
                    
                except KeyboardInterrupt:
                    self.logger.info("Keyboard interrupt received")
                    break
                except Exception as e:
                    self.logger.error(f"Error in monitoring loop: {str(e)}")
                    time.sleep(10)
            
        finally:
            self.stop_trading()
    
    def run_simulation_mode(self, config_file: str = None, max_steps: int = None):
        """Run in simulation mode."""
        try:
            # Load configuration
            config = self._load_configuration(config_file, 'simulation')
            if not config:
                return
            
            # Create simulation trader directly
            from live_trading.simulation_trader import SimulationLiveTrader
            
            trader = SimulationLiveTrader(config.__dict__, "simulation")
            
            self.logger.info("⚡ Starting Simulation Mode")
            
            # Run simulation
            result = trader.run_simulation(max_steps)
            
            if result.success:
                self.logger.info("✅ Simulation completed successfully")
                self._log_final_summary(result.data.get('final_performance', {}))
            else:
                self.logger.error(f"Simulation failed: {result.message}")
            
        except Exception as e:
            self.logger.error(f"Simulation error: {str(e)}")

    def _load_specialized_ensemble(self) -> bool:
        """Load the specialized ensemble models."""
        try:
            self.logger.info("Loading specialized ensemble models...")

            # Define model paths
            models_dir = Path("data/models")
            model_files = {
                'lightgbm': models_dir / "specialized_lightgbm_signal_generator_20250921_042626.pkl",
                'catboost': models_dir / "fixed_catboost_market_regime_analyst_20250921_044615.pkl",
                'xgboost': models_dir / "fixed_xgboost_risk_manager_20250921_044032.pkl",
                'linear': models_dir / "specialized_linear_stability_monitor_20250921_042620.pkl"
            }

            # Load models
            models = {}
            for model_name, model_path in model_files.items():
                if model_path.exists():
                    try:
                        # Load the model file (contains metadata and multi-output models)
                        model_data = joblib.load(model_path)

                        # Extract the actual model dictionary from the metadata structure
                        if isinstance(model_data, dict):
                            if 'model' in model_data:
                                model_obj = model_data['model']
                                if isinstance(model_obj, dict):
                                    # LightGBM/XGBoost structure: {'model': {target: model, ...}}
                                    models[model_name] = model_obj
                                    self.logger.info(f"✓ Loaded {model_name} model with {len(model_obj)} outputs")
                                else:
                                    # Single model object (like MultiOutputRegressor)
                                    models[model_name] = model_obj
                                    # Try to get number of outputs for MultiOutputRegressor
                                    if hasattr(model_obj, 'estimators_'):
                                        self.logger.info(f"✓ Loaded {model_name} model with {len(model_obj.estimators_)} outputs")
                                    else:
                                        self.logger.info(f"✓ Loaded {model_name} single model")
                            elif 'models' in model_data:
                                # CatBoost structure: {'models': {target: model, ...}}
                                model_dict = model_data['models']
                                models[model_name] = model_dict
                                self.logger.info(f"✓ Loaded {model_name} model with {len(model_dict)} outputs")
                            else:
                                # Direct dictionary of models
                                models[model_name] = model_data
                                self.logger.info(f"✓ Loaded {model_name} model (direct dict)")
                        else:
                            # Fallback for direct model objects
                            models[model_name] = model_data
                            self.logger.info(f"✓ Loaded {model_name} model (direct object)")
                    except Exception as e:
                        self.logger.warning(f"Failed to load {model_name}: {str(e)}")
                else:
                    self.logger.warning(f"Model file not found: {model_path}")

            if len(models) < 4:
                self.logger.warning(f"Only {len(models)}/4 models loaded successfully")

            # Create ensemble configuration
            ensemble_config = {
                'lightgbm_config': {'role': 'signal_generator', 'weight': 0.40},
                'catboost_config': {'role': 'market_regime_analyst', 'weight': 0.25},
                'xgboost_config': {'role': 'risk_manager', 'weight': 0.25},
                'linear_config': {'role': 'stability_monitor', 'weight': 0.10}
            }

            # Initialize specialized ensemble
            self.specialized_ensemble = SpecializedEnsembleOrchestrator(models, ensemble_config)

            self.logger.info(f"✅ Specialized ensemble initialized with {len(models)} models")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load specialized ensemble: {str(e)}")
            return False

    def _load_configuration(self, config_file: str, mode: str) -> LiveTradingConfig:
        """Load configuration based on mode and file."""
        try:
            if config_file and Path(config_file).exists():
                config = LiveTradingConfig.from_yaml(config_file)
                self.logger.info(f"✓ Loaded configuration from {config_file}")
            else:
                # Use default configuration based on mode
                default_configs = {
                    'live': 'live_trading/config/production.yaml',
                    'paper': 'live_trading/config/paper_trading.yaml',
                    'simulation': 'live_trading/config/simulation.yaml'
                }
                
                default_config_path = default_configs.get(mode)
                if default_config_path and Path(default_config_path).exists():
                    config = LiveTradingConfig.from_yaml(default_config_path)
                    self.logger.info(f"✓ Loaded default {mode} configuration")
                else:
                    config = LiveTradingConfig()
                    self.logger.info("✓ Using default configuration")
            
            # Validate configuration
            issues = config.validate()
            if issues:
                self.logger.error("Configuration validation failed:")
                for issue in issues:
                    self.logger.error(f"  - {issue}")
                return None
            
            return config
            
        except Exception as e:
            self.logger.error(f"Failed to load configuration: {str(e)}")
            return None
    
    def _print_banner(self):
        """Print system banner."""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                 XAUUSD AI LIVE TRADING SYSTEM                ║
║                                                              ║
║  🤖 Model-Driven Trading with Ensemble Intelligence         ║
║  📊 Real-Time Data Processing & Risk Management              ║
║  🛡️ Multi-Layer Safety Controls & Emergency Stops           ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        self.logger.info("🚀 Starting XAUUSD AI Live Trading System")
        self.logger.info("=" * 60)
    
    def _log_startup_info(self, mode: str, config: LiveTradingConfig):
        """Log startup information."""
        self.logger.info("✅ Live Trading System Started Successfully")
        self.logger.info(f"Trading Mode: {mode.upper()}")
        self.logger.info(f"Symbol: {config.symbol}")
        self.logger.info(f"Initial Capital: ${config.initial_capital:,.2f}")
        self.logger.info(f"Max Risk Per Trade: {config.max_risk_per_trade:.1%}")
        self.logger.info(f"Max Concurrent Trades: {config.max_concurrent_trades}")
        self.logger.info(f"Min Signal Confidence: {config.min_signal_confidence:.1%}")
        self.logger.info(f"Model Path: {config.model_path}")
        self.logger.info("=" * 60)
    
    def _show_status(self):
        """Show current system status."""
        if not self.orchestrator:
            return
        
        try:
            status = self.orchestrator.get_status()
            
            self.logger.info("📊 SYSTEM STATUS")
            self.logger.info("-" * 40)
            self.logger.info(f"Running: {status['is_running']}")
            self.logger.info(f"Start Time: {status.get('start_time', 'N/A')}")
            
            # Trader performance
            trader_perf = status.get('trader_status', {})
            if trader_perf:
                self.logger.info(f"Total Trades: {trader_perf.get('total_trades', 0)}")
                self.logger.info(f"Active Trades: {trader_perf.get('active_trades_count', 0)}")
                self.logger.info(f"Win Rate: {trader_perf.get('win_rate', 0):.1%}")
                self.logger.info(f"Total P&L: ${trader_perf.get('total_pnl', 0):.2f}")
            
            # Orchestrator stats
            orch_stats = status.get('orchestrator_stats', {})
            if orch_stats:
                self.logger.info(f"Total Cycles: {orch_stats.get('total_cycles', 0)}")
                self.logger.info(f"Successful Predictions: {orch_stats.get('successful_predictions', 0)}")
                self.logger.info(f"Trades Executed: {orch_stats.get('trades_executed', 0)}")
            
            self.logger.info("-" * 40)
            
        except Exception as e:
            self.logger.error(f"Error showing status: {str(e)}")
    
    def _log_final_summary(self, performance_data: dict):
        """Log final performance summary."""
        self.logger.info("📈 FINAL PERFORMANCE SUMMARY")
        self.logger.info("=" * 50)
        
        trader_perf = performance_data.get('trader_performance', {})
        if trader_perf:
            self.logger.info(f"Total Trades: {trader_perf.get('total_trades', 0)}")
            self.logger.info(f"Winning Trades: {trader_perf.get('winning_trades', 0)}")
            self.logger.info(f"Losing Trades: {trader_perf.get('losing_trades', 0)}")
            self.logger.info(f"Win Rate: {trader_perf.get('win_rate', 0):.1%}")
            self.logger.info(f"Total P&L: ${trader_perf.get('total_pnl', 0):.2f}")
            self.logger.info(f"Profit Factor: {trader_perf.get('profit_factor', 0):.2f}")
        
        self.logger.info("=" * 50)


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="XAUUSD AI Live Trading System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --mode paper                    # Start paper trading
  %(prog)s --mode paper --interactive      # Paper trading with monitoring
  %(prog)s --mode simulation --steps 1000  # Run 1000-step simulation
  %(prog)s --mode live --config my.yaml    # Live trading with custom config
  
Trading Modes:
  paper      - Risk-free paper trading with real market data
  simulation - High-speed historical data replay for validation
  live       - Real money trading (use with extreme caution!)
  
Safety Notes:
  - Always start with paper trading to validate system behavior
  - Use small position sizes when transitioning to live trading
  - Monitor the system actively during operation
  - Understand that trading involves substantial risk of loss
        """
    )
    
    parser.add_argument(
        "--mode", "-m",
        choices=["live", "paper", "simulation"],
        default="paper",
        help="Trading mode (default: paper)"
    )
    parser.add_argument(
        "--config", "-c",
        help="Path to configuration file (uses mode defaults if not specified)"
    )
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="Run in interactive monitoring mode"
    )
    parser.add_argument(
        "--steps", "-s",
        type=int,
        help="Maximum steps for simulation mode"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress detailed output"
    )
    
    args = parser.parse_args()
    
    # Create launcher
    launcher = LiveTradingLauncher()
    
    try:
        if args.mode == "simulation":
            launcher.run_simulation_mode(args.config, args.steps)
        elif args.interactive:
            launcher.run_interactive_mode(args.mode, args.config)
        else:
            # Start and run indefinitely
            if launcher.start_trading(args.mode, args.config):
                try:
                    while launcher.is_running:
                        time.sleep(60)  # Check every minute
                except KeyboardInterrupt:
                    pass
                finally:
                    launcher.stop_trading()
        
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
