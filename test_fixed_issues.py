#!/usr/bin/env python3
"""
Test script to verify all the critical fixes:
1. Trade group system working properly
2. Opposite signal closure logic
3. LiveTrade constructor fix
4. Risk manager integration
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from live_trading.base import LiveTrade, TradeDirection, OrderType
from live_trading.risk_manager import LiveRiskManager
from live_trading.trade_execution import TradeExecutionEngine
from datetime import datetime
import uuid

def test_livetrade_constructor():
    """Test that LiveTrade constructor works without 'confidence' parameter."""
    print("🧪 Testing LiveTrade constructor...")
    
    try:
        trade = LiveTrade(
            trade_id=str(uuid.uuid4()),
            symbol="XAUUSD!",
            direction=TradeDirection.LONG,
            entry_time=datetime.now(),
            entry_price=3740.0,
            entry_type=OrderType.MARKET,
            position_size=0.1,
            model_confidence=0.75,  # Correct parameter name
            model_consensus={},
            signal_probability=0.75,
            trade_group_id="test_group_123"
        )
        print("✅ LiveTrade constructor works correctly")
        print(f"   Trade ID: {trade.trade_id}")
        print(f"   Trade Group ID: {trade.trade_group_id}")
        print(f"   Model Confidence: {trade.model_confidence}")
        return True
    except Exception as e:
        print(f"❌ LiveTrade constructor failed: {e}")
        return False

def test_trade_group_counting():
    """Test that trade group counting works properly."""
    print("\n🧪 Testing trade group counting...")
    
    try:
        # Create test config
        config = {
            'initial_capital': 1000.0,
            'max_risk_per_trade': 0.04,
            'max_concurrent_trades': 3,
            'symbol': 'XAUUSD!'
        }
        
        risk_manager = LiveRiskManager(config)
        
        # Create test trades with same group ID
        group_id = "test_group_123"
        trades = []
        
        for i in range(3):
            trade = LiveTrade(
                trade_id=f"trade_{i}",
                symbol="XAUUSD!",
                direction=TradeDirection.LONG,
                entry_time=datetime.now(),
                entry_price=3740.0,
                entry_type=OrderType.MARKET,
                position_size=0.1,
                model_confidence=0.75,
                model_consensus={},
                signal_probability=0.75,
                trade_group_id=group_id  # Same group ID for all
            )
            trades.append(trade)
        
        # Test trade group counting
        group_count = risk_manager._count_trade_groups(trades)
        
        if group_count == 1:
            print("✅ Trade group counting works correctly")
            print(f"   3 individual trades = {group_count} trade group")
            return True
        else:
            print(f"❌ Trade group counting failed: expected 1, got {group_count}")
            return False
            
    except Exception as e:
        print(f"❌ Trade group counting test failed: {e}")
        return False

def test_risk_manager_integration():
    """Test that risk manager integration works."""
    print("\n🧪 Testing risk manager integration...")

    try:
        # Create test config
        config = {
            'initial_capital': 1000.0,
            'max_risk_per_trade': 0.04,
            'max_concurrent_trades': 3,
            'symbol': 'XAUUSD!'
        }

        risk_manager = LiveRiskManager(config)
        execution_engine = TradeExecutionEngine(config, risk_manager)

        # Test that the correct method exists
        if (execution_engine.risk_manager is not None and
            hasattr(execution_engine.risk_manager, 'validate_trade_risk')):
            print("✅ Risk manager integration works correctly")
            print(f"   Execution engine has risk manager: {type(execution_engine.risk_manager).__name__}")
            print(f"   Risk manager has validate_trade_risk method: ✅")
            return True
        else:
            print("❌ Risk manager integration failed")
            if execution_engine.risk_manager is None:
                print("   - Execution engine has no risk manager")
            else:
                print("   - Risk manager missing validate_trade_risk method")
            return False

    except Exception as e:
        print(f"❌ Risk manager integration test failed: {e}")
        return False

def test_opposite_signal_method():
    """Test that opposite signal closure method exists."""
    print("\n🧪 Testing opposite signal closure method...")

    try:
        config = {
            'symbol': 'XAUUSD!',
            'magic_number': 12345
        }

        execution_engine = TradeExecutionEngine(config)

        # Test that all required methods exist
        methods_to_check = [
            '_close_opposite_direction_trades',
            'close_all_trades'
        ]

        missing_methods = []
        for method in methods_to_check:
            if not hasattr(execution_engine, method):
                missing_methods.append(method)

        # Test that magic_number is properly set
        if not hasattr(execution_engine, 'magic_number'):
            missing_methods.append('magic_number attribute')
        elif execution_engine.magic_number != 12345:
            missing_methods.append('magic_number value incorrect')

        if not missing_methods:
            print("✅ All trade execution methods exist")
            print("   Methods: _close_opposite_direction_trades, close_all_trades")
            print(f"   Magic number: {execution_engine.magic_number}")
            return True
        else:
            print(f"❌ Missing methods/attributes: {missing_methods}")
            return False

    except Exception as e:
        print(f"❌ Trade execution test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing Critical Fixes for Live Trading System")
    print("=" * 60)
    
    tests = [
        test_livetrade_constructor,
        test_trade_group_counting,
        test_risk_manager_integration,
        test_opposite_signal_method
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All critical fixes are working correctly!")
        print("\n✅ System is ready for:")
        print("   - Multi-tier trade execution")
        print("   - Trade group management")
        print("   - Opposite signal closure")
        print("   - Risk manager validation")
    else:
        print("❌ Some fixes need attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
