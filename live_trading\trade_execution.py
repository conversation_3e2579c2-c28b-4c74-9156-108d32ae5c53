"""
Trade Execution Engine

Handles MT5 trade execution with model-driven entry/exit optimization,
multi-tier TP/SL system, and comprehensive position management.
"""

import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import time
import uuid

from .base import LiveTrade, TradeDirection, TradeStatus, OrderType
from .account_balance_manager import AccountBalanceManager

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.mt5_collector.mt5_client import MT5Client
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import MT5DataError

# ENHANCED: Import new unified risk management system
try:
    from .risk_management_factory import RiskManagementFactory, UnifiedRiskManager
    UNIFIED_RISK_AVAILABLE = True
except ImportError:
    UNIFIED_RISK_AVAILABLE = False


class TradeExecutionEngine(LoggerMixin):
    """
    Advanced trade execution engine with model-driven optimization.
    
    Implements sophisticated entry/exit logic, multi-tier TP/SL system,
    and real-time position management with MT5 integration.
    """
    
    def __init__(self, config: Dict[str, Any], risk_manager=None):
        """
        Initialize trade execution engine.

        Args:
            config: Configuration dictionary
            risk_manager: Risk manager instance for trade validation (legacy or unified)
        """
        self.config = config
        self.symbol = config.get('symbol', 'XAUUSD!')

        # ENHANCED: Support both legacy and new unified risk management
        if risk_manager is None and UNIFIED_RISK_AVAILABLE:
            # Create unified risk manager if none provided
            try:
                risk_factory = RiskManagementFactory(config)
                self.risk_manager = risk_factory.create_unified_risk_manager()
                self.logger.info("✓ Created unified risk manager for trade execution")
            except Exception as e:
                self.logger.warning(f"Failed to create unified risk manager: {e}")
                self.risk_manager = None
        else:
            self.risk_manager = risk_manager

        # MT5 client
        self.client = MT5Client()
        self.is_connected = False

        # Account balance manager
        self.balance_manager = AccountBalanceManager(config)
        
        # Execution parameters
        self.spread_pips = config.get('spread_pips', 0.18)
        self.slippage_pips = config.get('slippage_pips', 0.05)
        self.max_slippage_pips = config.get('max_slippage_pips', 0.5)
        self.magic_number = config.get('magic_number', 12345)
        
        # Multi-tier TP/SL configuration
        self.tp1_percentage = config.get('tp1_percentage', 0.4)  # 40% quick profits
        self.tp2_percentage = config.get('tp2_percentage', 0.35) # 35% swing profits
        self.tp3_percentage = config.get('tp3_percentage', 0.25) # 25% trend following
        
        # Model-driven parameters
        self.use_model_entry_timing = config.get('use_model_entry_timing', True)
        self.use_model_exit_levels = config.get('use_model_exit_levels', True)
        self.entry_patience_seconds = config.get('entry_patience_seconds', 300)
        
        # Execution tracking
        self.execution_stats = {
            'orders_placed': 0,
            'orders_filled': 0,
            'orders_rejected': 0,
            'avg_execution_time_ms': 0.0,
            'avg_slippage_pips': 0.0,
            'total_spread_cost': 0.0,
            'total_commission': 0.0,
            'last_execution_time': None,
            'errors': 0
        }
        
        # Active orders tracking
        self.pending_orders = {}
        self.active_positions = {}
    
    def connect(self) -> bool:
        """Connect to MT5 for trade execution."""
        try:
            if self.client.connect():
                self.is_connected = True

                # Connect balance manager
                if not self.balance_manager.connect():
                    self.logger.warning("Balance manager connection failed, will use fallback")

                # Verify symbol and trading permissions
                symbol_info = self.client.get_symbol_info(self.symbol)
                if not symbol_info:
                    raise MT5DataError(f"Symbol {self.symbol} not available for trading")

                # Get account info through balance manager
                account_info = self.balance_manager.get_account_info()
                if not account_info:
                    raise MT5DataError("Failed to get account information")

                self.logger.info(f"Account balance: ${account_info.balance:,.2f} ({account_info.source})")
                self.logger.info(f"Account equity: ${account_info.equity:,.2f}")
                self.logger.info(f"Free margin: ${account_info.margin_free:,.2f}")

                self.logger.info(f"Connected to MT5 for trade execution: {self.symbol}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to connect for trade execution: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5."""
        if self.is_connected:
            self.client.disconnect()
            self.balance_manager.disconnect()
            self.is_connected = False
            self.logger.info("Disconnected from MT5 trade execution")

    def execute_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute trade with automatic account balance retrieval.

        This is the main interface method called by the trading system.
        It automatically retrieves current account balance and executes the trade.

        Args:
            signal: Trading signal from model engine

        Returns:
            Dictionary with execution result
        """
        try:
            # Get current price from signal or fetch from market
            current_price = signal.get('current_price')
            if not current_price:
                # Would need to fetch current price from data collector
                # For now, use a placeholder - this should be improved
                self.logger.warning("No current price in signal, using placeholder")
                current_price = 2000.0  # XAUUSD approximate price

            # Get current account balance
            account_info = self.balance_manager.get_account_info()
            account_balance = account_info.balance

            self.logger.info(f"Executing trade with balance: ${account_balance:,.2f} ({account_info.source})")

            # Execute the multi-tier trade system
            trades = self.execute_model_driven_trade(signal, current_price, account_balance)

            if trades and len(trades) > 0:
                # Calculate total position size across all trades
                total_position_size = sum(trade.position_size for trade in trades)
                trade_ids = [trade.trade_id for trade in trades]

                return {
                    'success': True,
                    'trade_ids': trade_ids,
                    'num_trades': len(trades),
                    'entry_price': trades[0].entry_price,  # All trades have same entry price
                    'total_position_size': total_position_size,
                    'individual_sizes': [trade.position_size for trade in trades],
                    'message': f"Multi-tier trade system executed: {len(trades)} trades, total {total_position_size} lots"
                }
            else:
                return {
                    'success': False,
                    'error': 'Multi-tier trade execution failed',
                    'message': 'Failed to execute any trades - check logs for details'
                }

        except Exception as e:
            error_msg = f"Error in execute_trade: {str(e)}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'message': 'Trade execution failed due to system error'
            }

    def execute_model_driven_trade(self, signal: Dict[str, Any],
                                 current_price: float,
                                 account_balance: float) -> Optional[List[LiveTrade]]:
        """
        Execute multi-tier trade system with 3 separate trades.

        This method opens 3 separate trades with the same SL but different TP levels:
        - Trade 1: 40% of total position size, TP1 level
        - Trade 2: 35% of total position size, TP2 level
        - Trade 3: 25% of total position size, TP3 level
        All trades share the same SL level for consistent risk management.

        Args:
            signal: Trading signal from model engine
            current_price: Current market price
            account_balance: Current account balance

        Returns:
            List of LiveTrade objects if successful, None otherwise
        """
        if not self.is_connected:
            if not self.connect():
                return None

        # Close opposite direction trades before opening new ones
        self._close_opposite_direction_trades(signal['direction'])

        # Validate with risk manager before executing trades
        if hasattr(self, 'risk_manager') and self.risk_manager:
            # Get current active trades for validation (after closing opposite trades)
            active_trades = []
            positions = mt5.positions_get(symbol=self.symbol)
            if positions:
                for pos in positions:
                    # Extract trade group ID from comment (format: AI_Trade_<group_id>)
                    comment = getattr(pos, 'comment', '')
                    trade_group_id = None

                    if 'AI_Trade_' in comment:
                        # Extract group ID from AI_Trade_<group_id> format
                        try:
                            trade_group_id = comment.split('AI_Trade_')[1]  # Full group ID after AI_Trade_
                        except:
                            trade_group_id = None

                    # Create minimal LiveTrade objects for risk validation
                    trade = LiveTrade(
                        trade_id=str(pos.ticket),
                        symbol=self.symbol,
                        direction=TradeDirection.LONG if pos.type == mt5.POSITION_TYPE_BUY else TradeDirection.SHORT,
                        entry_time=datetime.fromtimestamp(pos.time),
                        entry_price=pos.price_open,
                        entry_type=OrderType.MARKET,
                        position_size=pos.volume,
                        model_confidence=0.5,  # Default for existing positions
                        model_consensus={},
                        signal_probability=0.5,
                        trade_group_id=trade_group_id
                    )
                    active_trades.append(trade)

            self.logger.info(f"🔍 Current active trades for risk validation: {len(active_trades)} trades")
            if active_trades:
                group_ids = [t.trade_group_id for t in active_trades if t.trade_group_id]
                unique_groups = len(set(group_ids)) if group_ids else len(active_trades)
                self.logger.info(f"🔍 Trade groups: {unique_groups} groups from {len(active_trades)} trades")

            # Validate the new trade
            risk_validation = self.risk_manager.validate_trade_risk(
                signal=signal,
                current_balance=account_balance,
                active_trades=active_trades
            )

            if not risk_validation['approved']:
                self.logger.warning(f"🛑 Trade rejected by risk manager: {risk_validation['rejections']}")
                return None

            self.logger.info(f"✅ Risk manager approved trade (risk score: {risk_validation.get('risk_score', 'N/A')})")

        # Check if we have a valid trading signal
        if not signal.get('direction') or signal.get('confidence', 0) < 0.5:
            self.logger.warning(f"Invalid signal: direction={signal.get('direction')}, confidence={signal.get('confidence', 0)}")
            return None

        try:
            start_time = time.time()

            # Calculate total position size with model-driven adjustments
            total_position_size = self._calculate_position_size(
                signal, account_balance, current_price
            )

            if total_position_size <= 0:
                self.logger.warning("Position size calculation resulted in zero or negative size")
                return None

            # Determine optimal entry price and method
            entry_result = self._determine_optimal_entry(signal, current_price)

            # Calculate model-driven TP/SL levels
            tp_sl_levels = self._calculate_model_driven_levels(signal, entry_result['target_price'])

            # Calculate individual trade sizes based on configured percentages
            tp1_size = round(total_position_size * self.tp1_percentage, 2)
            tp2_size = round(total_position_size * self.tp2_percentage, 2)
            tp3_size = round(total_position_size * self.tp3_percentage, 2)

            # Filter out trades with 0% allocation and ensure minimum trade sizes (0.01 lots minimum for XAUUSD)
            min_size = 0.01
            active_trades = []

            # Check each tier and only include if size > 0 and >= min_size
            if tp1_size > 0 and tp1_size >= min_size:
                active_trades.append({'tier': 'TP1', 'size': tp1_size, 'tp_price': tp_sl_levels['tp1_price']})
            elif tp1_size > 0:
                self.logger.warning(f"TP1 size too small: {tp1_size} < {min_size}, skipping")

            if tp2_size > 0 and tp2_size >= min_size:
                active_trades.append({'tier': 'TP2', 'size': tp2_size, 'tp_price': tp_sl_levels['tp2_price']})
            elif tp2_size > 0:
                self.logger.warning(f"TP2 size too small: {tp2_size} < {min_size}, skipping")

            if tp3_size > 0 and tp3_size >= min_size:
                active_trades.append({'tier': 'TP3', 'size': tp3_size, 'tp_price': tp_sl_levels['tp3_price']})
            elif tp3_size > 0:
                self.logger.warning(f"TP3 size too small: {tp3_size} < {min_size}, skipping")

            # If no active trades, fall back to single trade
            if not active_trades:
                self.logger.warning(f"No valid trades after filtering: TP1={tp1_size}, TP2={tp2_size}, TP3={tp3_size}")
                return self._execute_single_trade_fallback(signal, current_price, account_balance, entry_result, tp_sl_levels)

            self.logger.info("🎯 EXECUTING MULTI-TIER TRADE SYSTEM:")
            self.logger.info(f"   Total Position Size: {total_position_size:.2f} lots")
            self.logger.info(f"   Active Trades: {len(active_trades)}")

            # Log only active trades
            for trade_config in active_trades:
                self.logger.info(f"   {trade_config['tier']} Trade: {trade_config['size']:.2f} lots @ {trade_config['tp_price']:.2f}")

            # Log disabled trades
            if tp1_size == 0:
                self.logger.info(f"   TP1 Trade: DISABLED (0% allocation)")
            if tp2_size == 0:
                self.logger.info(f"   TP2 Trade: DISABLED (0% allocation)")
            if tp3_size == 0:
                self.logger.info(f"   TP3 Trade: DISABLED (0% allocation)")

            self.logger.info(f"   Shared SL: {tp_sl_levels['sl_price']:.2f}")

            # Generate unique trade group ID for this multi-tier signal (keep short for MT5 comment limit)
            trade_group_id = f"g{str(uuid.uuid4())[:6]}{int(time.time()) % 10000}"

            # Execute only active trades
            executed_trades = []

            for i, config in enumerate(active_trades):
                trade = self._create_individual_trade(
                    signal, entry_result, tp_sl_levels, config, i+1, trade_group_id
                )

                if trade:
                    execution_result = self._execute_mt5_order(trade, entry_result)

                    if execution_result['success']:
                        trade.status = TradeStatus.OPEN
                        trade.entry_price = execution_result['fill_price']
                        trade.spread_cost = execution_result['spread_cost']
                        trade.slippage_cost = execution_result['slippage_cost']

                        # Set up individual TP/SL orders for this trade
                        self._setup_individual_trade_exits(trade)

                        executed_trades.append(trade)

                        self.logger.info(f"✅ {config['tier']} Trade executed: {trade.trade_id} - "
                                       f"{trade.direction.value} {trade.position_size} lots at {trade.entry_price}")
                    else:
                        self.logger.error(f"❌ {config['tier']} Trade execution failed: {execution_result['error']}")
                        # If any trade fails, we should consider closing already opened trades
                        # For now, we'll continue with the successful ones
                else:
                    self.logger.error(f"❌ Failed to create {config['tier']} trade")

            if executed_trades:
                # Update statistics
                execution_time_ms = (time.time() - start_time) * 1000
                self._update_execution_stats({'success': True}, execution_time_ms)

                self.logger.info(f"🎉 Multi-tier trade system executed: {len(executed_trades)}/{len(active_trades)} trades successful")
                return executed_trades
            else:
                self.logger.error("❌ No trades were successfully executed")
                return None

        except Exception as e:
            self.logger.error(f"Error executing multi-tier trade system: {str(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            self.execution_stats['errors'] += 1
            return None

    def _close_opposite_direction_trades(self, new_signal_direction: str):
        """
        Close all trades in the opposite direction when a new signal is received.

        Args:
            new_signal_direction: Direction of the new signal ('BUY' or 'SELL')
        """
        try:
            # Get all open positions
            positions = mt5.positions_get(symbol=self.symbol)
            if positions is None:
                return

            # Determine which positions to close (opposite to new signal)
            positions_to_close = []
            if new_signal_direction.upper() in ['BUY', 'LONG']:
                # New signal is BUY, so close all SELL positions
                for position in positions:
                    if position.type == mt5.POSITION_TYPE_SELL:
                        positions_to_close.append(position)
                close_direction_name = "SELL"
                close_order_type = mt5.ORDER_TYPE_BUY  # To close SELL positions
            else:
                # New signal is SELL, so close all BUY positions
                for position in positions:
                    if position.type == mt5.POSITION_TYPE_BUY:
                        positions_to_close.append(position)
                close_direction_name = "BUY"
                close_order_type = mt5.ORDER_TYPE_SELL  # To close BUY positions

            closed_count = 0
            for position in positions_to_close:

                # Close the position
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": self.symbol,
                    "volume": position.volume,
                    "type": close_order_type,
                    "position": position.ticket,
                    "deviation": 5,
                    "magic": self.magic_number,
                    "comment": f"Opposite_Signal_Close_{new_signal_direction}",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }

                result = mt5.order_send(close_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed_count += 1
                    self.logger.info(f"✅ Closed {close_direction_name} position {position.ticket} due to {new_signal_direction} signal")
                else:
                    self.logger.warning(f"❌ Failed to close {close_direction_name} position {position.ticket}: {result.comment}")

            if closed_count > 0:
                self.logger.info(f"🔄 Closed {closed_count} {close_direction_name} positions before opening {new_signal_direction} trades")
            else:
                self.logger.info(f"ℹ️ No {close_direction_name} positions to close for {new_signal_direction} signal")

        except Exception as e:
            self.logger.error(f"Error closing opposite direction trades: {str(e)}")

    def close_all_trades(self, reason: str = "Manual close"):
        """
        Close all open trades for this symbol.

        Args:
            reason: Reason for closing trades
        """
        try:
            positions = mt5.positions_get(symbol=self.symbol)
            if positions is None:
                self.logger.info("No positions to close")
                return

            closed_count = 0
            for position in positions:
                # Determine close order type
                if position.type == mt5.POSITION_TYPE_BUY:
                    close_type = mt5.ORDER_TYPE_SELL
                else:
                    close_type = mt5.ORDER_TYPE_BUY

                # Close the position
                close_request = {
                    "action": mt5.TRADE_ACTION_DEAL,
                    "symbol": self.symbol,
                    "volume": position.volume,
                    "type": close_type,
                    "position": position.ticket,
                    "deviation": 5,
                    "magic": self.magic_number,
                    "comment": f"Close_All_{reason}",
                    "type_time": mt5.ORDER_TIME_GTC,
                    "type_filling": mt5.ORDER_FILLING_IOC,
                }

                result = mt5.order_send(close_request)
                if result.retcode == mt5.TRADE_RETCODE_DONE:
                    closed_count += 1
                    self.logger.info(f"✅ Closed position {position.ticket} - {reason}")
                else:
                    self.logger.warning(f"❌ Failed to close position {position.ticket}: {result.comment}")

            if closed_count > 0:
                self.logger.info(f"🔄 Closed {closed_count} positions - {reason}")

        except Exception as e:
            self.logger.error(f"Error closing all trades: {str(e)}")

    def _execute_single_trade_fallback(self, signal: Dict[str, Any], current_price: float,
                                     account_balance: float, entry_result: Dict[str, Any],
                                     tp_sl_levels: Dict[str, float]) -> Optional[List[LiveTrade]]:
        """
        Fallback to single trade when individual trade sizes are too small.
        """
        self.logger.info("🔄 Using single trade fallback due to small position sizes")

        # Calculate total position size
        total_position_size = self._calculate_position_size(signal, account_balance, current_price)

        # Generate trade group ID for single trade fallback (keep short for MT5 comment limit)
        trade_group_id = f"s{str(uuid.uuid4())[:6]}{int(time.time()) % 10000}"

        # Create single trade with TP3 as the target (most conservative)
        trade = LiveTrade(
            trade_id=str(uuid.uuid4()),
            symbol=self.symbol,
            direction=TradeDirection.LONG if signal['direction'].lower() in ['long', 'buy'] else TradeDirection.SHORT,
            trade_group_id=trade_group_id,  # Assign trade group ID
            entry_time=datetime.now(),
            entry_price=round(entry_result['target_price'], 2),
            entry_type=entry_result['order_type'],
            position_size=total_position_size,
            model_confidence=signal.get('confidence', 0.0),
            model_consensus={},
            signal_probability=signal.get('confidence', 0.0)
        )

        # Set TP3 and SL levels
        trade.tp1_price = round(tp_sl_levels['tp3_price'], 2)  # Use TP3 as single target
        trade.tp2_price = None
        trade.tp3_price = None
        trade.sl_price = round(tp_sl_levels['sl_price'], 2)

        # Execute the trade
        execution_result = self._execute_mt5_order(trade, entry_result)

        if execution_result['success']:
            trade.status = TradeStatus.OPEN
            trade.entry_price = execution_result['fill_price']
            trade.spread_cost = execution_result['spread_cost']
            trade.slippage_cost = execution_result['slippage_cost']

            # Set up single trade exits
            self._setup_individual_trade_exits(trade)

            self.logger.info(f"✅ Single fallback trade executed: {trade.trade_id}")
            return [trade]
        else:
            self.logger.error(f"❌ Single fallback trade failed: {execution_result['error']}")
            return None

    def _create_individual_trade(self, signal: Dict[str, Any], entry_result: Dict[str, Any],
                               tp_sl_levels: Dict[str, float], config: Dict[str, Any],
                               trade_number: int, trade_group_id: str) -> Optional[LiveTrade]:
        """
        Create an individual trade for the multi-tier system.
        """
        try:
            trade = LiveTrade(
                trade_id=f"{str(uuid.uuid4())}-{config['tier']}",
                symbol=self.symbol,
                direction=TradeDirection.LONG if signal['direction'].lower() in ['long', 'buy'] else TradeDirection.SHORT,
                trade_group_id=trade_group_id,  # Assign trade group ID
                entry_time=datetime.now(),
                entry_price=round(entry_result['target_price'], 2),
                entry_type=entry_result['order_type'],
                position_size=config['size'],
                model_confidence=signal.get('confidence', 0.0),
                model_consensus={},
                signal_probability=signal.get('confidence', 0.0)
            )

            # Set individual TP and shared SL
            if config['tier'] == 'TP1':
                trade.tp1_price = round(config['tp_price'], 2)
                trade.tp2_price = None
                trade.tp3_price = None
            elif config['tier'] == 'TP2':
                trade.tp1_price = round(config['tp_price'], 2)
                trade.tp2_price = None
                trade.tp3_price = None
            elif config['tier'] == 'TP3':
                trade.tp1_price = round(config['tp_price'], 2)
                trade.tp2_price = None
                trade.tp3_price = None

            # All trades share the same SL
            trade.sl_price = round(tp_sl_levels['sl_price'], 2)

            return trade

        except Exception as e:
            self.logger.error(f"Error creating {config['tier']} trade: {str(e)}")
            return None

    def _setup_individual_trade_exits(self, trade: LiveTrade):
        """
        Set up TP/SL orders for an individual trade in the multi-tier system.
        """
        try:
            self.logger.info(f"Setting up exits for {trade.trade_id}")

            # Store trade in active positions for monitoring
            self.active_positions[trade.trade_id] = {
                'trade': trade,
                'original_size': trade.position_size,
                'remaining_size': trade.position_size,
                'entry_time': datetime.now(),
                'last_check': datetime.now(),
                'is_multi_tier': True  # Flag to identify multi-tier trades
            }

            self.logger.info(f"Individual trade exits configured: TP@{trade.tp1_price:.2f}, SL@{trade.sl_price:.2f}")

        except Exception as e:
            self.logger.error(f"Failed to setup individual trade exits: {str(e)}")

    def _calculate_position_size(self, signal: Dict[str, Any],
                               account_balance: float, current_price: float) -> float:
        """Calculate position size with model-driven adjustments and comprehensive logging."""

        # Log initial parameters
        self.logger.info("=== POSITION SIZE CALCULATION ===")
        self.logger.info(f"Account Balance: ${account_balance:,.2f}")

        # Base risk calculation
        risk_per_trade = self.config.get('max_risk_per_trade', 0.02)
        max_risk_amount = account_balance * risk_per_trade
        sl_distance = signal.get('sl_distance', 20.0)

        self.logger.info(f"Risk per trade: {risk_per_trade:.1%} = ${max_risk_amount:.2f}")
        self.logger.info(f"Stop loss distance: {sl_distance:.1f} pips")

        # Calculate base position size
        pip_value = 1.0  # For XAUUSD, approximately $1 per pip per 0.01 lot
        if sl_distance <= 0:
            self.logger.error("Invalid stop loss distance, using default 20 pips")
            sl_distance = 20.0

        base_position_size = max_risk_amount / (sl_distance * pip_value)
        self.logger.info(f"Base position size: ${max_risk_amount:.2f} ÷ ({sl_distance:.1f} × {pip_value}) = {base_position_size:.4f} lots")

        # CRITICAL: Extract model-driven multipliers with detailed logging
        self.logger.info("🔍 EXTRACTING MODEL MULTIPLIERS:")

        confidence_multiplier = None
        volatility_adjustment = None

        # Source 1: Direct signal values
        if 'position_size_multiplier' in signal:
            confidence_multiplier = signal['position_size_multiplier']
            self.logger.info(f"   ✅ Found position_size_multiplier in signal: {confidence_multiplier:.3f}")
        if 'volatility_adjustment' in signal:
            volatility_adjustment = signal['volatility_adjustment']
            self.logger.info(f"   ✅ Found volatility_adjustment in signal: {volatility_adjustment:.3f}")

        # Source 2: Check nested predictions structure
        if 'predictions' in signal:
            predictions = signal['predictions']
            if 'position_size_category' in predictions and confidence_multiplier is None:
                confidence_multiplier = predictions['position_size_category']
                self.logger.info(f"   ✅ Found position_size_category in predictions: {confidence_multiplier:.3f}")
            if 'volatility_adjustment' in predictions and volatility_adjustment is None:
                volatility_adjustment = predictions['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in predictions: {volatility_adjustment:.3f}")

        # Source 3: Check trading_signal structure
        if 'trading_signal' in signal:
            trading_signal = signal['trading_signal']
            if 'position_size_multiplier' in trading_signal and confidence_multiplier is None:
                confidence_multiplier = trading_signal['position_size_multiplier']
                self.logger.info(f"   ✅ Found position_size_multiplier in trading_signal: {confidence_multiplier:.3f}")
            if 'volatility_adjustment' in trading_signal and volatility_adjustment is None:
                volatility_adjustment = trading_signal['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in trading_signal: {volatility_adjustment:.3f}")

        # Apply defaults only if no model predictions found
        if confidence_multiplier is None:
            confidence_multiplier = 1.0
            self.logger.warning(f"   ❌ No model position_size_multiplier found, using default: {confidence_multiplier:.3f}")
        if volatility_adjustment is None:
            volatility_adjustment = 1.0
            self.logger.warning(f"   ❌ No model volatility_adjustment found, using default: {volatility_adjustment:.3f}")

        self.logger.info(f"📊 FINAL MULTIPLIERS:")
        self.logger.info(f"   Ensemble multiplier: {confidence_multiplier:.3f}")
        self.logger.info(f"   Volatility adjustment: {volatility_adjustment:.3f}")

        # Session-based adjustment
        current_hour = datetime.now().hour
        session_config = self.config.get('trading_sessions', {})
        session_multiplier = 1.0
        current_session = "unknown"

        for session_name, session_data in session_config.items():
            start_hour = session_data.get('start_hour', 0)
            end_hour = session_data.get('end_hour', 24)
            if start_hour <= current_hour < end_hour:
                session_multiplier = session_data.get('position_multiplier', 1.0)
                current_session = session_name
                break

        self.logger.info(f"Session: {current_session} (hour {current_hour}) multiplier: {session_multiplier:.3f}")

        # Final position size calculation
        final_position_size = (
            base_position_size *
            confidence_multiplier *
            volatility_adjustment *
            session_multiplier
        )

        self.logger.info(f"Pre-limits size: {base_position_size:.4f} × {confidence_multiplier:.3f} × {volatility_adjustment:.3f} × {session_multiplier:.3f} = {final_position_size:.4f} lots")

        # Apply limits
        min_lot = 0.01
        max_lot = self.config.get('max_position_size', 10.0)
        original_size = final_position_size
        final_position_size = max(min_lot, min(final_position_size, max_lot))

        if final_position_size != original_size:
            self.logger.info(f"Size adjusted by limits: {original_size:.4f} → {final_position_size:.4f} lots")

        # Round to valid lot size
        final_position_size = round(final_position_size, 2)

        # Final validation
        if final_position_size <= 0:
            self.logger.error("Final position size is zero or negative, using minimum")
            final_position_size = min_lot

        # Calculate final risk amount for verification
        final_risk_amount = final_position_size * sl_distance * pip_value
        risk_percentage = final_risk_amount / account_balance

        self.logger.info(f"FINAL POSITION SIZE: {final_position_size:.2f} lots")
        self.logger.info(f"Final risk amount: ${final_risk_amount:.2f} ({risk_percentage:.2%} of balance)")
        self.logger.info("=== END POSITION SIZE CALCULATION ===")

        return final_position_size
    
    def _determine_optimal_entry(self, signal: Dict[str, Any], 
                               current_price: float) -> Dict[str, Any]:
        """Determine optimal entry price and order type."""
        if not self.use_model_entry_timing:
            return {
                'target_price': current_price,
                'order_type': OrderType.MARKET,
                'patience_seconds': 0
            }
        
        # Model-driven entry optimization
        entry_urgency = signal.get('entry_urgency', 'normal')
        confidence = signal.get('confidence', 0.0)
        volatility_adjustment = signal.get('volatility_adjustment', 1.0)
        
        if entry_urgency == 'high' or confidence > 0.85:
            # High confidence - immediate market entry
            return {
                'target_price': current_price,
                'order_type': OrderType.MARKET,
                'patience_seconds': 0
            }
        
        # Calculate optimal entry offset
        direction = signal.get('direction', 'long')
        base_offset = 2.0 * volatility_adjustment  # Base offset in pips
        
        if direction == 'long':
            target_price = current_price - (base_offset * 0.01)  # Better entry for long
        else:
            target_price = current_price + (base_offset * 0.01)  # Better entry for short
        
        return {
            'target_price': target_price,
            'order_type': OrderType.LIMIT,
            'patience_seconds': self.entry_patience_seconds
        }
    
    def _calculate_model_driven_levels(self, signal: Dict[str, Any],
                                     entry_price: float) -> Dict[str, float]:
        """Calculate TP/SL levels using model predictions with MT5 validation and comprehensive logging."""

        self.logger.info("🎯 MODEL-DRIVEN TP/SL CALCULATION STARTING")
        self.logger.info("=" * 50)

        direction = signal.get('direction', 'long').lower()
        direction_multiplier = 1 if direction in ['long', 'buy'] else -1

        self.logger.info(f"Direction: {direction.upper()}, Entry Price: {entry_price:.2f}")

        # Get symbol info for stop level validation
        symbol_info = self.client.get_symbol_info(self.symbol)
        if not symbol_info:
            self.logger.warning(f"Could not get symbol info for {self.symbol}, using default stops")
            min_stop_distance = 10.0  # Default minimum distance in pips
        else:
            # MT5 stops_level is in points, convert to pips
            min_stop_distance = max(symbol_info.get('stops_level', 10) / 10.0, 5.0)
            self.logger.info(f"MT5 minimum stop distance for {self.symbol}: {min_stop_distance} pips")

        # CRITICAL: Extract model predictions with detailed logging
        self.logger.info("🔍 EXTRACTING MODEL PREDICTIONS:")

        # Check multiple possible sources for model predictions
        tp1_distance = None
        tp2_distance = None
        tp3_distance = None
        sl_distance = None
        vol_adjustment = None

        # Source 1: Direct signal values
        if 'tp1_distance' in signal:
            tp1_distance = signal['tp1_distance']
            self.logger.info(f"   ✅ Found tp1_distance in signal: {tp1_distance:.1f}")
        if 'tp2_distance' in signal:
            tp2_distance = signal['tp2_distance']
            self.logger.info(f"   ✅ Found tp2_distance in signal: {tp2_distance:.1f}")
        if 'tp3_distance' in signal:
            tp3_distance = signal['tp3_distance']
            self.logger.info(f"   ✅ Found tp3_distance in signal: {tp3_distance:.1f}")
        if 'sl_distance' in signal:
            sl_distance = signal['sl_distance']
            self.logger.info(f"   ✅ Found sl_distance in signal: {sl_distance:.1f}")
        if 'volatility_adjustment' in signal:
            vol_adjustment = signal['volatility_adjustment']
            self.logger.info(f"   ✅ Found volatility_adjustment in signal: {vol_adjustment:.2f}")

        # Source 2: Check nested predictions structure
        if 'predictions' in signal:
            predictions = signal['predictions']
            if 'tp1_distance' in predictions and tp1_distance is None:
                tp1_distance = predictions['tp1_distance']
                self.logger.info(f"   ✅ Found tp1_distance in predictions: {tp1_distance:.1f}")
            if 'tp2_distance' in predictions and tp2_distance is None:
                tp2_distance = predictions['tp2_distance']
                self.logger.info(f"   ✅ Found tp2_distance in predictions: {tp2_distance:.1f}")
            if 'sl_distance' in predictions and sl_distance is None:
                sl_distance = predictions['sl_distance']
                self.logger.info(f"   ✅ Found sl_distance in predictions: {sl_distance:.1f}")
            if 'volatility_adjustment' in predictions and vol_adjustment is None:
                vol_adjustment = predictions['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in predictions: {vol_adjustment:.2f}")

        # Source 3: Check trading_signal structure
        if 'trading_signal' in signal:
            trading_signal = signal['trading_signal']
            if 'tp1_distance' in trading_signal and tp1_distance is None:
                tp1_distance = trading_signal['tp1_distance']
                self.logger.info(f"   ✅ Found tp1_distance in trading_signal: {tp1_distance:.1f}")
            if 'tp2_distance' in trading_signal and tp2_distance is None:
                tp2_distance = trading_signal['tp2_distance']
                self.logger.info(f"   ✅ Found tp2_distance in trading_signal: {tp2_distance:.1f}")
            if 'sl_distance' in trading_signal and sl_distance is None:
                sl_distance = trading_signal['sl_distance']
                self.logger.info(f"   ✅ Found sl_distance in trading_signal: {sl_distance:.1f}")
            if 'volatility_adjustment' in trading_signal and vol_adjustment is None:
                vol_adjustment = trading_signal['volatility_adjustment']
                self.logger.info(f"   ✅ Found volatility_adjustment in trading_signal: {vol_adjustment:.2f}")

        # Apply defaults only if no model predictions found
        current_atr = signal.get('current_atr', 40.0)  # Default ~40 pips ATR for XAUUSD

        if tp1_distance is None:
            tp1_distance = current_atr * 1.5   # 1.5x ATR
            self.logger.warning(f"   ❌ No model tp1_distance found, using ATR-based: {tp1_distance:.1f}")
        if tp2_distance is None:
            tp2_distance = current_atr * 3.0   # 3.0x ATR
            self.logger.warning(f"   ❌ No model tp2_distance found, using ATR-based: {tp2_distance:.1f}")
        if tp3_distance is None:
            tp3_distance = current_atr * 5.0   # 5.0x ATR
            self.logger.warning(f"   ❌ No model tp3_distance found, using ATR-based: {tp3_distance:.1f}")
        if sl_distance is None:
            sl_distance = current_atr * 2.0    # 2.0x ATR
            self.logger.warning(f"   ❌ No model sl_distance found, using ATR-based: {sl_distance:.1f}")
        if vol_adjustment is None:
            vol_adjustment = 1.0
            self.logger.warning(f"   ❌ No model volatility_adjustment found, using default: {vol_adjustment:.2f}")

        # Apply volatility adjustment
        original_tp1 = tp1_distance
        original_tp2 = tp2_distance
        original_tp3 = tp3_distance
        original_sl = sl_distance

        tp1_distance *= vol_adjustment
        tp2_distance *= vol_adjustment
        tp3_distance *= vol_adjustment
        sl_distance *= vol_adjustment

        if vol_adjustment != 1.0:
            self.logger.info(f"📊 VOLATILITY ADJUSTMENT APPLIED ({vol_adjustment:.2f}x):")
            self.logger.info(f"   TP1: {original_tp1:.1f} → {tp1_distance:.1f}")
            self.logger.info(f"   TP2: {original_tp2:.1f} → {tp2_distance:.1f}")
            self.logger.info(f"   TP3: {original_tp3:.1f} → {tp3_distance:.1f}")
            self.logger.info(f"   SL:  {original_sl:.1f} → {sl_distance:.1f}")

        # Validate minimum distances (but use realistic minimums for XAUUSD)
        xauusd_min_distance = max(min_stop_distance, 20.0)  # At least 20 pips for XAUUSD
        tp1_distance = max(tp1_distance, xauusd_min_distance)
        tp2_distance = max(tp2_distance, xauusd_min_distance * 1.5)  # At least 30 pips
        tp3_distance = max(tp3_distance, xauusd_min_distance * 2.0)  # At least 40 pips
        sl_distance = max(sl_distance, xauusd_min_distance)

        # Calculate actual price levels
        tp1_price = entry_price + (direction_multiplier * tp1_distance * 0.01)
        tp2_price = entry_price + (direction_multiplier * tp2_distance * 0.01)
        tp3_price = entry_price + (direction_multiplier * tp3_distance * 0.01)
        sl_price = entry_price - (direction_multiplier * sl_distance * 0.01)

        # Final logging
        self.logger.info(f"🎯 FINAL CALCULATED LEVELS for {direction.upper()} at {entry_price:.2f}:")
        self.logger.info(f"   TP1: {tp1_price:.2f} ({tp1_distance:.1f} pips)")
        self.logger.info(f"   TP2: {tp2_price:.2f} ({tp2_distance:.1f} pips)")
        self.logger.info(f"   TP3: {tp3_price:.2f} ({tp3_distance:.1f} pips)")
        self.logger.info(f"   SL:  {sl_price:.2f} ({sl_distance:.1f} pips)")
        self.logger.info("=" * 50)

        return {
            'tp1_price': tp1_price,
            'tp2_price': tp2_price,
            'tp3_price': tp3_price,
            'sl_price': sl_price,
            'tp1_distance': tp1_distance,
            'tp2_distance': tp2_distance,
            'tp3_distance': tp3_distance,
            'sl_distance': sl_distance
        }

    def _execute_mt5_order(self, trade: LiveTrade, entry_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute market order with MT5 at the last acceptable price (no pending orders)."""
        try:
            # Get symbol information for price normalization
            symbol_info = mt5.symbol_info(self.symbol)
            if symbol_info is None:
                return {'success': False, 'error': f'Symbol {self.symbol} not found'}
            
            # Check if trading is allowed
            if not symbol_info.trade_mode:
                return {'success': False, 'error': 'Trading is disabled for this symbol'}
            
            # Get current tick data
            tick = mt5.symbol_info_tick(self.symbol)
            if tick is None:
                return {'success': False, 'error': 'Cannot get current tick data'}
            
            # Helper function to normalize prices
            def normalize_price(price):
                """Normalize price to symbol's tick size and digits"""
                digits = symbol_info.digits
                price = round(price, digits)
                tick_size = symbol_info.trade_tick_size
                if tick_size > 0:
                    price = round(price / tick_size) * tick_size
                    price = round(price, digits)
                return price
            
            # Always use current market price for execution
            if trade.direction == TradeDirection.LONG:
                entry_price = normalize_price(tick.ask)
            else:
                entry_price = normalize_price(tick.bid)
            
            # Normalize SL and TP prices (handle None values)
            sl_price = normalize_price(trade.sl_price) if trade.sl_price is not None else None

            # For multi-tier system, use the appropriate TP price (tp1_price is used for individual targets)
            if trade.tp1_price is not None:
                tp_price = normalize_price(trade.tp1_price)
            elif trade.tp3_price is not None:
                tp_price = normalize_price(trade.tp3_price)
            else:
                tp_price = None
            
            # Check minimum distance requirements
            min_stop_level = symbol_info.trade_stops_level
            point = symbol_info.point
            min_distance = min_stop_level * point
            
            # Validate and adjust stop levels (only if they exist)
            if sl_price is not None:
                sl_distance = abs(entry_price - sl_price)
                if sl_distance < min_distance:
                    self.logger.warning(f"SL distance {sl_distance} < minimum {min_distance}, adjusting...")
                    if trade.direction == TradeDirection.LONG:
                        sl_price = normalize_price(entry_price - min_distance)
                    else:
                        sl_price = normalize_price(entry_price + min_distance)

            if tp_price is not None:
                tp_distance = abs(entry_price - tp_price)
                if tp_distance < min_distance:
                    self.logger.warning(f"TP distance {tp_distance} < minimum {min_distance}, adjusting...")
                    if trade.direction == TradeDirection.LONG:
                        tp_price = normalize_price(entry_price + min_distance)
                    else:
                        tp_price = normalize_price(entry_price - min_distance)
            
            # Final validation of stop levels (only if they exist)
            if trade.direction == TradeDirection.LONG:
                if sl_price is not None and sl_price >= entry_price:
                    self.logger.error(f"Invalid SL for LONG: SL {sl_price} >= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid stop loss level for LONG position'}
                if tp_price is not None and tp_price <= entry_price:
                    self.logger.error(f"Invalid TP for LONG: TP {tp_price} <= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid take profit level for LONG position'}
            else:  # SHORT
                if sl_price is not None and sl_price <= entry_price:
                    self.logger.error(f"Invalid SL for SHORT: SL {sl_price} <= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid stop loss level for SHORT position'}
                if tp_price is not None and tp_price >= entry_price:
                    self.logger.error(f"Invalid TP for SHORT: TP {tp_price} >= Entry {entry_price}")
                    return {'success': False, 'error': 'Invalid take profit level for SHORT position'}

            # Log the normalized levels for debugging
            self.logger.info(f"Normalized order levels - Entry: {entry_price}, SL: {sl_price}, TP: {tp_price}")
            self.logger.info(f"Symbol info - Digits: {symbol_info.digits}, Tick size: {symbol_info.trade_tick_size}, Min stop level: {min_stop_level}")

            # Always send MARKET order
            if trade.direction == TradeDirection.LONG:
                order_type = mt5.ORDER_TYPE_BUY
                action = mt5.TRADE_ACTION_DEAL
                price = entry_price
            else:
                order_type = mt5.ORDER_TYPE_SELL
                action = mt5.TRADE_ACTION_DEAL
                price = entry_price

            # Prepare order request (handle None values for SL/TP)
            request = {
                "action": action,
                "symbol": self.symbol,
                "volume": float(trade.position_size),
                "type": order_type,
                "price": price,
                "deviation": int(self.max_slippage_pips * 10),  # allow some slippage
                "magic": 12345,
                "comment": f"AI_Trade_{trade.trade_group_id}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,  # Immediate-Or-Cancel for market orders
            }

            # Only add SL/TP if they are not None (MT5 doesn't accept None values)
            if sl_price is not None:
                request["sl"] = sl_price
            if tp_price is not None:
                request["tp"] = tp_price
            
            # Log the request for debugging
            self.logger.info(f"Sending order request: {request}")
            
            # Risk management is handled by the risk manager's trade group system
            # Remove hardcoded MT5 trade limit to allow proper multi-tier execution

            # Send order
            result = mt5.order_send(request)
            
            if result is None:
                last_error = mt5.last_error()
                return {
                    'success': False,
                    'error': f"Order send failed: {last_error}"
                }
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                # Log more details about the rejection
                self.logger.error(f"Order rejected - Code: {result.retcode}, Comment: {result.comment}")
                self.logger.error(f"Request was: {request}")
                return {
                    'success': False,
                    'error': f"Order rejected: {result.retcode} - {result.comment}"
                }
            
            # Calculate costs
            fill_price = result.price
            spread_cost = self.spread_pips * trade.position_size * 1.0
            slippage_cost = abs(fill_price - price) * trade.position_size * 100
            
            self.logger.info(f"Order executed successfully - Ticket: {result.order}, Fill price: {fill_price}")
            
            return {
                'success': True,
                'order_ticket': result.order,
                'fill_price': fill_price,
                'spread_cost': spread_cost,
                'slippage_cost': slippage_cost,
                'commission': 0.0
            }
            
        except Exception as e:
            self.logger.error(f"MT5 execution error: {str(e)}")
            return {
                'success': False,
                'error': f"MT5 execution error: {str(e)}"
            }

    def _setup_multi_tier_exits(self, trade: LiveTrade):
        """
        DEPRECATED: This method is no longer used.
        Multi-tier system now uses separate trades instead of partial closes.
        Kept for backward compatibility.
        """
        self.logger.warning("_setup_multi_tier_exits called but deprecated - using separate trades approach")
        pass

    def check_multi_tier_exits(self):
        """
        Check and monitor individual trades in the multi-tier system.
        Each trade has its own TP/SL levels and will be closed automatically by MT5.
        This method just monitors and logs the status.
        """
        if not self.active_positions:
            return

        current_price = self._get_current_price()
        if current_price is None:
            return

        positions_to_remove = []

        for trade_id, position_info in self.active_positions.items():
            try:
                trade = position_info['trade']

                # Check if position still exists in MT5
                if not self._position_exists(trade_id):
                    positions_to_remove.append(trade_id)

                    # Log which TP/SL was hit based on current price
                    if trade.direction == TradeDirection.LONG:
                        if current_price >= trade.tp1_price:
                            self.logger.info(f"✅ {trade_id} closed at TP level: {current_price:.2f}")
                        elif current_price <= trade.sl_price:
                            self.logger.info(f"🛑 {trade_id} closed at SL level: {current_price:.2f}")
                        else:
                            self.logger.info(f"🔄 {trade_id} closed manually or by system: {current_price:.2f}")
                    else:  # SHORT
                        if current_price <= trade.tp1_price:
                            self.logger.info(f"✅ {trade_id} closed at TP level: {current_price:.2f}")
                        elif current_price >= trade.sl_price:
                            self.logger.info(f"🛑 {trade_id} closed at SL level: {current_price:.2f}")
                        else:
                            self.logger.info(f"🔄 {trade_id} closed manually or by system: {current_price:.2f}")
                    continue

                # Log current status for monitoring
                if position_info.get('is_multi_tier', False):
                    tier = 'TP1' if 'TP1' in trade_id else 'TP2' if 'TP2' in trade_id else 'TP3'
                    distance_to_tp = abs(current_price - trade.tp1_price)
                    distance_to_sl = abs(current_price - trade.sl_price)

                    # Only log every 5 minutes to avoid spam
                    last_log = position_info.get('last_log', datetime.min)
                    if (datetime.now() - last_log).total_seconds() > 300:  # 5 minutes
                        self.logger.info(f"📊 {tier} Trade {trade_id}: Price={current_price:.2f}, "
                                       f"TP={trade.tp1_price:.2f} ({distance_to_tp:.1f}), "
                                       f"SL={trade.sl_price:.2f} ({distance_to_sl:.1f})")
                        position_info['last_log'] = datetime.now()

                position_info['last_check'] = datetime.now()

            except Exception as e:
                self.logger.error(f"Error checking multi-tier exits for {trade_id}: {str(e)}")

        # Clean up completed positions
        for trade_id in positions_to_remove:
            del self.active_positions[trade_id]

    def _get_current_price(self) -> Optional[float]:
        """Get current market price for the symbol."""
        try:
            tick = mt5.symbol_info_tick(self.symbol)
            if tick:
                return (tick.bid + tick.ask) / 2  # Mid price
            return None
        except Exception as e:
            self.logger.error(f"Failed to get current price: {str(e)}")
            return None

    def _position_exists(self, trade_id: str) -> bool:
        """Check if position still exists in MT5."""
        try:
            positions = mt5.positions_get(symbol=self.symbol)
            if positions:
                for pos in positions:
                    if str(pos.comment).endswith(trade_id[:8]):  # Match trade ID
                        return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to check position existence: {str(e)}")
            return False

    def _price_hit_target(self, current_price: float, target_price: float, direction: 'TradeDirection') -> bool:
        """Check if current price has hit the target level."""
        if direction.value.lower() in ['long', 'buy']:
            return current_price >= target_price
        else:
            return current_price <= target_price

    def _execute_partial_close(self, trade_id: str, close_size: float, level_name: str):
        """Execute partial position close."""
        try:
            # Find the MT5 position
            positions = mt5.positions_get(symbol=self.symbol)
            target_position = None

            if positions:
                for pos in positions:
                    if str(pos.comment).endswith(trade_id[:8]):
                        target_position = pos
                        break

            if not target_position:
                self.logger.error(f"Position not found for partial close: {trade_id}")
                return False

            # Prepare close request
            close_type = mt5.ORDER_TYPE_SELL if target_position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY

            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": self.symbol,
                "volume": close_size,
                "type": close_type,
                "position": target_position.ticket,
                "deviation": int(self.max_slippage_pips * 10),
                "magic": 12345,
                "comment": f"Partial_Close_{level_name}_{trade_id[:8]}",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # Execute partial close
            result = mt5.order_send(request)

            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                self.logger.info(f"✓ Partial close executed: {level_name} - {close_size:.2f} lots at {result.price:.2f}")
                return True
            else:
                self.logger.error(f"Partial close failed: {result.retcode if result else 'No result'}")
                return False

        except Exception as e:
            self.logger.error(f"Error executing partial close: {str(e)}")
            return False

    def _update_execution_stats(self, execution_result: Dict[str, Any], execution_time_ms: float):
        """Update execution statistics."""
        self.execution_stats['orders_placed'] += 1
        
        if execution_result['success']:
            self.execution_stats['orders_filled'] += 1
            
            # Update averages
            total_filled = self.execution_stats['orders_filled']
            
            self.execution_stats['avg_execution_time_ms'] = (
                (self.execution_stats['avg_execution_time_ms'] * (total_filled - 1) + execution_time_ms) / total_filled
            )
            
            slippage_pips = execution_result.get('slippage_cost', 0.0) / 100  # Convert to pips
            self.execution_stats['avg_slippage_pips'] = (
                (self.execution_stats['avg_slippage_pips'] * (total_filled - 1) + slippage_pips) / total_filled
            )
            
            self.execution_stats['total_spread_cost'] += execution_result.get('spread_cost', 0.0)
            
        else:
            self.execution_stats['orders_rejected'] += 1
        
        self.execution_stats['last_execution_time'] = datetime.now()
    
    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution performance statistics."""
        total_orders = self.execution_stats['orders_placed']
        fill_rate = self.execution_stats['orders_filled'] / max(total_orders, 1)
        
        return {
            **self.execution_stats,
            'fill_rate': fill_rate,
            'rejection_rate': self.execution_stats['orders_rejected'] / max(total_orders, 1),
            'is_connected': self.is_connected,
            'pending_orders_count': len(self.pending_orders),
            'active_positions_count': len(self.active_positions)
        }
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
