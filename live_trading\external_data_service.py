"""
External Data Service for Live Trading

Manages background collection and caching of external asset data
for feature engineering with proper rate limiting and error handling.
"""

import asyncio
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import pandas as pd
import logging

from data_collection.external_data.factories import ExternalDataCollectorFactory
from data_collection.external_data.yfinance_collector import YFinanceCollector

# Import logging from existing system
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin


class ExternalDataService(LoggerMixin):
    """
    Background service for collecting and caching external asset data.
    
    Features:
    - Automatic data collection at configurable intervals
    - Rate limiting to respect yfinance limits
    - Data caching and persistence
    - Error handling and retry logic
    - Integration with live trading system
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize external data service."""
        super().__init__()
        self.config = config
        
        # External data configuration
        self.external_config = config.get('external_data', {})
        self.assets = self.external_config.get('assets', ['DXY', 'SPY', 'VIX', 'TLT', 'QQQ', 'IEF'])
        self.timeframe = self.external_config.get('timeframe', '5m')
        
        # Update intervals (in minutes)
        self.update_interval = self.external_config.get('update_interval_minutes', 10)  # 10 minutes
        self.lookback_days = self.external_config.get('lookback_days', 7)  # 7 days of data
        
        # Rate limiting (yfinance allows ~2000 requests/hour)
        self.request_delay = self.external_config.get('request_delay_seconds', 1.0)  # 1 second between requests
        self.max_requests_per_hour = self.external_config.get('max_requests_per_hour', 1800)  # Conservative limit
        
        # Data storage
        self.data_path = Path(self.external_config.get('data_path', 'data/external'))
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # Service state
        self._running = False
        self._thread = None
        self._data_cache = {}
        self._last_update_times = {}
        self._request_count = 0
        self._hour_start = datetime.now().replace(minute=0, second=0, microsecond=0)
        
        # Initialize data collector
        self._init_data_collector()
        
        self.logger.info(f"External data service initialized for {len(self.assets)} assets")
        self.logger.info(f"Update interval: {self.update_interval} minutes")
        self.logger.info(f"Rate limit: {self.max_requests_per_hour} requests/hour")
    
    def _init_data_collector(self):
        """Initialize the external data collector."""
        try:
            # Create collector factory
            factory = ExternalDataCollectorFactory(self.config)
            
            # Create yfinance collector
            self.collector = factory.create_collector('yfinance')
            
            # Test connection
            if not self.collector.test_connection():
                self.logger.warning("External data collector connection test failed")
            else:
                self.logger.info("External data collector connection test successful")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize external data collector: {str(e)}")
            # Create fallback collector
            self.collector = YFinanceCollector(self.config)
    
    def start(self):
        """Start the external data service."""
        if self._running:
            self.logger.warning("External data service is already running")
            return
        
        self.logger.info("Starting external data service...")
        self._running = True
        
        # Start background thread
        self._thread = threading.Thread(target=self._run_service, daemon=True)
        self._thread.start()
        
        # Initial data collection
        self._collect_initial_data()
        
        self.logger.info("External data service started successfully")
    
    def stop(self):
        """Stop the external data service."""
        if not self._running:
            return
        
        self.logger.info("Stopping external data service...")
        self._running = False
        
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5.0)
        
        self.logger.info("External data service stopped")
    
    def _run_service(self):
        """Main service loop running in background thread."""
        self.logger.info("External data service loop started")
        
        while self._running:
            try:
                # Check if we need to update data
                if self._should_update_data():
                    self._update_external_data()
                
                # Reset request counter every hour
                self._reset_hourly_counter()
                
                # Sleep for 30 seconds before next check
                time.sleep(30)
                
            except Exception as e:
                self.logger.error(f"Error in external data service loop: {str(e)}")
                time.sleep(60)  # Wait longer on error
        
        self.logger.info("External data service loop stopped")
    
    def _should_update_data(self) -> bool:
        """Check if data should be updated."""
        now = datetime.now()
        
        # Check if enough time has passed since last update
        for asset in self.assets:
            last_update = self._last_update_times.get(asset)
            if not last_update:
                return True
            
            time_since_update = (now - last_update).total_seconds() / 60
            if time_since_update >= self.update_interval:
                return True
        
        return False
    
    def _collect_initial_data(self):
        """Collect initial data for all assets."""
        self.logger.info("Collecting initial external data...")
        
        try:
            self._update_external_data()
            self.logger.info("Initial external data collection completed")
        except Exception as e:
            self.logger.error(f"Initial data collection failed: {str(e)}")
    
    def _update_external_data(self):
        """Update external data for all assets."""
        if not self._can_make_requests():
            self.logger.warning("Rate limit reached, skipping data update")
            return
        
        now = datetime.now()
        start_date = now - timedelta(days=self.lookback_days)
        end_date = now
        
        updated_assets = []
        
        for asset in self.assets:
            try:
                # Check individual asset update timing
                last_update = self._last_update_times.get(asset)
                if last_update:
                    time_since_update = (now - last_update).total_seconds() / 60
                    if time_since_update < self.update_interval:
                        continue
                
                # Rate limiting check
                if not self._can_make_requests():
                    self.logger.warning(f"Rate limit reached, stopping updates at {asset}")
                    break
                
                # Collect data
                self.logger.info(f"Updating {asset} data...")
                result = self.collector.collect_data(asset, self.timeframe, start_date, end_date)
                
                if result.errors:
                    self.logger.warning(f"Errors collecting {asset} data: {result.errors}")
                    continue
                
                if result.data.empty:
                    self.logger.warning(f"No data collected for {asset}")
                    continue
                
                # Cache the data
                self._data_cache[asset] = result.data
                self._last_update_times[asset] = now
                
                # Save to disk
                self._save_asset_data(asset, result.data)
                
                updated_assets.append(asset)
                self._request_count += 1
                
                # Rate limiting delay
                time.sleep(self.request_delay)
                
            except Exception as e:
                self.logger.error(f"Failed to update {asset} data: {str(e)}")
        
        if updated_assets:
            self.logger.info(f"Updated external data for {len(updated_assets)} assets: {updated_assets}")
        else:
            self.logger.info("No external data updates needed")
    
    def _save_asset_data(self, asset: str, data: pd.DataFrame):
        """Save asset data to disk."""
        try:
            asset_dir = self.data_path / asset.lower() / self.timeframe
            asset_dir.mkdir(parents=True, exist_ok=True)
            
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{asset.lower()}_{self.timeframe}_{timestamp}.csv"
            filepath = asset_dir / filename
            
            # Save data
            data.to_csv(filepath)
            
            # Clean up old files (keep only last 5 files)
            self._cleanup_old_files(asset_dir, keep_count=5)
            
            self.logger.debug(f"Saved {asset} data to {filepath}")
            
        except Exception as e:
            self.logger.error(f"Failed to save {asset} data: {str(e)}")
    
    def _cleanup_old_files(self, directory: Path, keep_count: int = 5):
        """Clean up old data files, keeping only the most recent ones."""
        try:
            csv_files = list(directory.glob("*.csv"))
            if len(csv_files) <= keep_count:
                return

            # Sort by modification time (newest first)
            csv_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove old files with better error handling
            files_to_remove = csv_files[keep_count:]
            removed_count = 0

            for old_file in files_to_remove:
                if self._safe_remove_file(old_file):
                    removed_count += 1

            if removed_count > 0:
                self.logger.debug(f"Successfully removed {removed_count}/{len(files_to_remove)} old data files from {directory}")

        except Exception as e:
            self.logger.error(f"Failed to cleanup old files in {directory}: {str(e)}")

    def _safe_remove_file(self, file_path: Path, max_retries: int = 3) -> bool:
        """Safely remove a file with retry logic and proper error handling."""
        import os
        import stat

        for attempt in range(max_retries):
            try:
                # Check if file exists
                if not file_path.exists():
                    return True

                # Try to make file writable if it's read-only
                try:
                    file_path.chmod(stat.S_IWRITE | stat.S_IREAD)
                except:
                    pass  # Ignore chmod errors

                # Attempt to remove the file
                file_path.unlink()
                self.logger.debug(f"Removed old data file: {file_path}")
                return True

            except PermissionError as e:
                if attempt < max_retries - 1:
                    # Wait a bit and retry
                    time.sleep(0.5)
                    continue
                else:
                    self.logger.warning(f"Permission denied removing {file_path}: {str(e)}")
                    return False

            except FileNotFoundError:
                # File was already removed
                return True

            except Exception as e:
                if attempt < max_retries - 1:
                    time.sleep(0.5)
                    continue
                else:
                    self.logger.warning(f"Failed to remove {file_path}: {str(e)}")
                    return False

        return False
    
    def _can_make_requests(self) -> bool:
        """Check if we can make more requests within rate limits."""
        return self._request_count < self.max_requests_per_hour
    
    def _reset_hourly_counter(self):
        """Reset request counter every hour."""
        now = datetime.now()
        current_hour = now.replace(minute=0, second=0, microsecond=0)
        
        if current_hour > self._hour_start:
            self._request_count = 0
            self._hour_start = current_hour
            self.logger.debug("Reset hourly request counter")
    
    def get_cached_data(self, asset: str) -> Optional[pd.DataFrame]:
        """Get cached data for an asset."""
        return self._data_cache.get(asset)
    
    def get_all_cached_data(self) -> Dict[str, pd.DataFrame]:
        """Get all cached data."""
        return self._data_cache.copy()
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status information."""
        return {
            'running': self._running,
            'assets': self.assets,
            'update_interval_minutes': self.update_interval,
            'last_update_times': self._last_update_times.copy(),
            'cached_assets': list(self._data_cache.keys()),
            'requests_this_hour': self._request_count,
            'max_requests_per_hour': self.max_requests_per_hour,
            'rate_limit_remaining': self.max_requests_per_hour - self._request_count
        }
    
    def force_update(self, asset: Optional[str] = None):
        """Force an immediate update for specific asset or all assets."""
        if asset:
            if asset in self.assets:
                # Reset last update time to force update
                self._last_update_times[asset] = datetime.now() - timedelta(hours=1)
                self.logger.info(f"Forced update scheduled for {asset}")
            else:
                self.logger.warning(f"Asset {asset} not in configured assets list")
        else:
            # Force update for all assets
            past_time = datetime.now() - timedelta(hours=1)
            for asset in self.assets:
                self._last_update_times[asset] = past_time
            self.logger.info("Forced update scheduled for all assets")
