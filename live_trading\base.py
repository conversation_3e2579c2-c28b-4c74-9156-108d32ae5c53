"""
Base Classes for Live Trading System

Provides abstract base classes and data structures for live trading components
following the established factory pattern architecture.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from enum import Enum
import pandas as pd
import numpy as np

# Import logging from existing system
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import DataCollectionError


class TradeDirection(Enum):
    """Trade direction enumeration."""
    LONG = "long"
    SHORT = "short"


class TradeStatus(Enum):
    """Trade status enumeration."""
    PENDING = "pending"
    OPEN = "open"
    CLOSED = "closed"
    CANCELLED = "cancelled"
    ERROR = "error"


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


@dataclass
class LiveTrade:
    """Live trading position data structure."""
    
    # Trade Identification
    trade_id: str
    symbol: str
    direction: TradeDirection

    # Entry Information
    entry_time: datetime
    entry_price: float
    entry_type: OrderType
    position_size: float  # Lot size

    # Model-Driven Parameters
    model_confidence: float
    model_consensus: Dict[str, float]  # Individual model confidences
    signal_probability: float

    # Trade Grouping (for multi-tier trades)
    trade_group_id: Optional[str] = None  # Groups multi-tier trades from same signal
    
    # Exit Levels (Model-Driven)
    tp1_price: Optional[float] = None
    tp2_price: Optional[float] = None
    tp3_price: Optional[float] = None
    sl_price: Optional[float] = None
    
    # Position Management
    remaining_size: float = field(init=False)
    tp1_filled: float = 0.0
    tp2_filled: float = 0.0
    tp3_filled: float = 0.0
    
    # Trade Status
    status: TradeStatus = TradeStatus.PENDING
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    
    # Performance Metrics
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    max_favorable_excursion: float = 0.0
    max_adverse_excursion: float = 0.0
    
    # Execution Details
    spread_cost: float = 0.0
    slippage_cost: float = 0.0
    commission_cost: float = 0.0
    
    # Metadata
    entry_features: Optional[Dict[str, Any]] = None
    exit_reason: Optional[str] = None
    notes: Optional[str] = None
    
    def __post_init__(self):
        """Initialize calculated fields."""
        self.remaining_size = self.position_size
    
    def update_unrealized_pnl(self, current_price: float):
        """Update unrealized P&L based on current price."""
        if self.status != TradeStatus.OPEN:
            return
        
        price_diff = current_price - self.entry_price
        if self.direction == TradeDirection.SHORT:
            price_diff = -price_diff
        
        # Calculate P&L (assuming $1 per pip per 0.01 lot for XAUUSD)
        pip_value = self.position_size * 100  # $1 per pip per 0.01 lot
        self.unrealized_pnl = price_diff * pip_value
        
        # Update max favorable/adverse excursion
        if self.unrealized_pnl > self.max_favorable_excursion:
            self.max_favorable_excursion = self.unrealized_pnl
        if self.unrealized_pnl < self.max_adverse_excursion:
            self.max_adverse_excursion = self.unrealized_pnl
    
    def partial_close(self, size: float, price: float, tp_level: int) -> float:
        """Close partial position and return realized P&L."""
        if size > self.remaining_size:
            size = self.remaining_size
        
        # Calculate P&L for closed portion
        price_diff = price - self.entry_price
        if self.direction == TradeDirection.SHORT:
            price_diff = -price_diff
        
        pip_value = size * 100
        partial_pnl = price_diff * pip_value
        
        # Update position
        self.remaining_size -= size
        self.realized_pnl += partial_pnl
        
        # Track TP fills
        if tp_level == 1:
            self.tp1_filled += size
        elif tp_level == 2:
            self.tp2_filled += size
        elif tp_level == 3:
            self.tp3_filled += size
        
        # Close trade if fully filled
        if self.remaining_size <= 0.001:  # Account for floating point precision
            self.status = TradeStatus.CLOSED
            self.exit_time = datetime.now()
            self.exit_price = price
        
        return partial_pnl
    
    def close_trade(self, price: float, reason: str = "manual"):
        """Close entire trade."""
        if self.remaining_size > 0:
            self.partial_close(self.remaining_size, price, 0)
        
        self.exit_reason = reason
        self.status = TradeStatus.CLOSED
        self.exit_time = datetime.now()
        self.exit_price = price
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trade to dictionary for logging/storage."""
        return {
            'trade_id': self.trade_id,
            'symbol': self.symbol,
            'direction': self.direction.value,
            'entry_time': self.entry_time.isoformat(),
            'entry_price': self.entry_price,
            'position_size': self.position_size,
            'model_confidence': self.model_confidence,
            'signal_probability': self.signal_probability,
            'tp1_price': self.tp1_price,
            'tp2_price': self.tp2_price,
            'tp3_price': self.tp3_price,
            'sl_price': self.sl_price,
            'status': self.status.value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'max_favorable_excursion': self.max_favorable_excursion,
            'max_adverse_excursion': self.max_adverse_excursion,
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'exit_price': self.exit_price,
            'exit_reason': self.exit_reason
        }


@dataclass
class LiveTradingResult:
    """Result of live trading operation."""
    
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    trades: List[LiveTrade] = field(default_factory=list)
    performance_metrics: Optional[Dict[str, float]] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    execution_time_ms: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


class BaseLiveTrader(LoggerMixin, ABC):
    """
    Abstract base class for live trading components.
    
    Provides common functionality and interface for all live trading
    implementations following the established factory pattern.
    """
    
    def __init__(self, config: Dict[str, Any], trader_type: str):
        """
        Initialize base live trader.
        
        Args:
            config: Trading configuration
            trader_type: Type of trader (real_time, paper, simulation)
        """
        self.config = config
        self.trader_type = trader_type
        self.is_active = False
        self.start_time = None
        self.active_trades: Dict[str, LiveTrade] = {}
        self.trade_history: List[LiveTrade] = []
        
        # Performance tracking
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'win_rate': 0.0,
            'profit_factor': 0.0,
            'sharpe_ratio': 0.0,
            'start_time': None,
            'last_update': None
        }
    
    @abstractmethod
    def start_trading(self) -> LiveTradingResult:
        """Start the live trading system."""
        pass
    
    @abstractmethod
    def stop_trading(self) -> LiveTradingResult:
        """Stop the live trading system."""
        pass
    
    @abstractmethod
    def execute_trade(self, signal: Dict[str, Any]) -> LiveTradingResult:
        """Execute a trade based on model signal."""
        pass
    
    @abstractmethod
    def update_positions(self) -> LiveTradingResult:
        """Update existing positions and check exit conditions."""
        pass
    
    @abstractmethod
    def get_account_info(self) -> Dict[str, Any]:
        """Get current account information."""
        pass
    
    def add_trade(self, trade: LiveTrade):
        """Add trade to active trades."""
        self.active_trades[trade.trade_id] = trade
        self.performance_stats['total_trades'] += 1
        self.logger.info(f"Added trade {trade.trade_id}: {trade.direction.value} {trade.position_size} lots")
    
    def close_trade(self, trade_id: str, price: float, reason: str = "manual") -> bool:
        """Close an active trade."""
        if trade_id not in self.active_trades:
            self.logger.warning(f"Trade {trade_id} not found in active trades")
            return False
        
        trade = self.active_trades[trade_id]
        trade.close_trade(price, reason)
        
        # Move to history
        self.trade_history.append(trade)
        del self.active_trades[trade_id]
        
        # Update performance stats
        if trade.realized_pnl > 0:
            self.performance_stats['winning_trades'] += 1
        else:
            self.performance_stats['losing_trades'] += 1
        
        self.performance_stats['total_pnl'] += trade.realized_pnl
        self._update_performance_metrics()
        
        self.logger.info(f"Closed trade {trade_id}: P&L = ${trade.realized_pnl:.2f}")
        return True
    
    def _update_performance_metrics(self):
        """Update performance metrics."""
        total_trades = self.performance_stats['total_trades']
        if total_trades == 0:
            return
        
        winning_trades = self.performance_stats['winning_trades']
        self.performance_stats['win_rate'] = winning_trades / total_trades
        
        # Calculate profit factor
        gross_profit = sum(t.realized_pnl for t in self.trade_history if t.realized_pnl > 0)
        gross_loss = abs(sum(t.realized_pnl for t in self.trade_history if t.realized_pnl < 0))
        
        if gross_loss > 0:
            self.performance_stats['profit_factor'] = gross_profit / gross_loss
        else:
            self.performance_stats['profit_factor'] = float('inf') if gross_profit > 0 else 0.0
        
        self.performance_stats['last_update'] = datetime.now()
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get current performance summary."""
        return {
            **self.performance_stats,
            'active_trades_count': len(self.active_trades),
            'total_trades_history': len(self.trade_history),
            'unrealized_pnl': sum(t.unrealized_pnl for t in self.active_trades.values())
        }
