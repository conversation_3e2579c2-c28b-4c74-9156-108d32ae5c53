{% extends "base.html" %}

{% block title %}Performance Analytics - {{ super() }}{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-nasa-light mb-2">
                <i class="fas fa-chart-bar text-nasa-accent mr-3"></i>
                Performance Analytics
            </h1>
            <p class="text-nasa-gray">Comprehensive trading performance analysis and metrics</p>
        </div>
        <div class="flex items-center mt-4 sm:mt-0 space-x-3">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-nasa-accent/20 text-nasa-accent">
                <i class="fas fa-check-circle mr-2"></i>
                Models Active
            </span>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-500/20 text-blue-400">
                Ensemble Mode
            </span>
        </div>
    </div>
</div>

<!-- Tabbed Interface -->
<div class="mb-8">
    <div class="border-b border-nasa-gray/20">
        <nav class="flex space-x-8">
            <button class="tab-button active" data-tab="backtest">
                <i class="fas fa-history mr-2"></i>
                Backtest
            </button>
            <button class="tab-button" data-tab="forward-test">
                <i class="fas fa-chart-line mr-2"></i>
                Forward Test
            </button>
            <button class="tab-button" data-tab="comparison">
                <i class="fas fa-balance-scale mr-2"></i>
                Comparison
            </button>
        </nav>
    </div>
</div>

<!-- Tab Content -->
<div class="tab-content">
    <!-- Backtest Tab -->
    <div id="backtest-tab" class="tab-pane active">
        <!-- Performance KPIs -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Return -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-nasa-accent text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Total Return</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="backtest-return">0.0%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                            Excellent
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-calendar mr-2"></i>
                    2-Year Backtest
                </div>
            </div>

            <!-- Sharpe Ratio -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-chart-bar text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Sharpe Ratio</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="backtest-sharpe">2.87</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                            Superior
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-shield-alt mr-2"></i>
                    Risk-Adjusted
                </div>
            </div>

            <!-- Max Drawdown -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-nasa-red/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-arrow-down text-nasa-red text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Max Drawdown</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="backtest-drawdown">0.0%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-red/20 text-nasa-red">
                            18d recovery
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Risk Control
                </div>
            </div>

            <!-- Win Rate -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-percentage text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Win Rate</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="backtest-winrate">0.0%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                            1,247 trades
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-bullseye mr-2"></i>
                    Success Rate
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Rolling Sharpe Ratio -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-nasa-light">
                        <i class="fas fa-chart-line text-nasa-accent mr-2"></i>
                        Rolling Sharpe Ratio
                    </h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-accent/20 text-nasa-accent">30D</button>
                        <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">90D</button>
                        <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">180D</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="rolling-sharpe-chart"></canvas>
                </div>
            </div>

            <!-- Recovery Time Analysis -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-nasa-light">
                        <i class="fas fa-clock text-nasa-accent mr-2"></i>
                        Recovery Time Analysis
                    </h3>
                </div>
                <div class="h-80">
                    <canvas id="recovery-time-chart"></canvas>
                </div>
            </div>
        </div>

        <!-- Monthly Profitability Heatmap -->
        <div class="chart-container mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-nasa-light">
                    <i class="fas fa-calendar-alt text-nasa-accent mr-2"></i>
                    Monthly Profitability Heatmap
                </h3>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-nasa-red rounded"></div>
                        <span class="text-sm text-nasa-gray">Loss</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-nasa-gray rounded"></div>
                        <span class="text-sm text-nasa-gray">Neutral</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-3 h-3 bg-nasa-accent rounded"></div>
                        <span class="text-sm text-nasa-gray">Profit</span>
                    </div>
                </div>
            </div>
            <div class="h-64">
                <canvas id="profitability-heatmap"></canvas>
            </div>
        </div>
    </div>

    <!-- Forward Test Tab -->
    <div id="forward-test-tab" class="tab-pane hidden">
        <!-- Forward Test KPIs -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Live Return -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-chart-line text-nasa-accent text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Live Return</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="forward-return">0.0%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +2.3%
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-calendar mr-2"></i>
                    6-Month Forward Test
                </div>
            </div>

            <!-- Model Accuracy -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-brain text-blue-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Model Accuracy</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="forward-accuracy">87.3%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                            Excellent
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-bullseye mr-2"></i>
                    Prediction Quality
                </div>
            </div>

            <!-- Signal Confidence -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-signal text-purple-400 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Avg Confidence</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="forward-confidence">92.1%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                            Very High
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-chart-pie mr-2"></i>
                    Signal Quality
                </div>
            </div>

            <!-- Model Consensus -->
            <div class="kpi-card">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-handshake text-yellow-500 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-nasa-gray">Model Consensus</h3>
                            <p class="text-2xl font-bold text-nasa-light" id="forward-consensus">94.5%</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-500">
                            Strong
                        </span>
                    </div>
                </div>
                <div class="flex items-center text-sm text-nasa-gray">
                    <i class="fas fa-users mr-2"></i>
                    Model Agreement
                </div>
            </div>
        </div>

        <!-- Forward Test Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Out-of-Sample Performance -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-nasa-light">
                        <i class="fas fa-chart-area text-nasa-accent mr-2"></i>
                        Out-of-Sample Performance
                    </h3>
                </div>
                <div class="h-80">
                    <canvas id="out-of-sample-chart"></canvas>
                </div>
            </div>

            <!-- Model Degradation Analysis -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-nasa-light">
                        <i class="fas fa-chart-line text-nasa-red mr-2"></i>
                        Model Degradation Analysis
                    </h3>
                </div>
                <div class="h-80">
                    <canvas id="degradation-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Tab -->
    <div id="comparison-tab" class="tab-pane hidden">
        <!-- Benchmark Comparison -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Performance vs Benchmark -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-nasa-light">
                        <i class="fas fa-balance-scale text-nasa-accent mr-2"></i>
                        Performance vs Benchmark
                    </h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-accent/20 text-nasa-accent">S&P 500</button>
                        <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">Gold ETF</button>
                        <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">Buy & Hold</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="benchmark-comparison-chart"></canvas>
                </div>
            </div>

            <!-- Risk-Return Scatter -->
            <div class="chart-container">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-nasa-light">
                        <i class="fas fa-chart-scatter text-nasa-accent mr-2"></i>
                        Risk-Return Analysis
                    </h3>
                </div>
                <div class="h-80">
                    <canvas id="risk-return-scatter"></canvas>
                </div>
            </div>
        </div>

        <!-- Comparison Table -->
        <div class="trading-card">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-nasa-light">
                    <i class="fas fa-table text-nasa-accent mr-2"></i>
                    Performance Comparison Table
                </h3>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-nasa-gray/20">
                            <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Strategy</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Total Return</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Sharpe Ratio</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Max Drawdown</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Win Rate</th>
                            <th class="text-left py-3 px-4 text-sm font-medium text-nasa-gray">Volatility</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-nasa-gray/10">
                        <tr class="hover:bg-nasa-gray/5 transition-colors">
                            <td class="py-4 px-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-nasa-accent rounded-full mr-3"></div>
                                    <span class="font-semibold text-nasa-light">AI Trading System</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 font-semibold text-nasa-accent">+127.4%</td>
                            <td class="py-4 px-4 font-semibold text-nasa-light">2.87</td>
                            <td class="py-4 px-4 font-semibold text-nasa-light">-12.3%</td>
                            <td class="py-4 px-4 font-semibold text-nasa-light">72.8%</td>
                            <td class="py-4 px-4 font-semibold text-nasa-light">18.4%</td>
                        </tr>
                        <tr class="hover:bg-nasa-gray/5 transition-colors">
                            <td class="py-4 px-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                                    <span class="font-medium text-nasa-light">S&P 500</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 font-medium text-nasa-light">+28.7%</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">1.12</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">-23.1%</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">N/A</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">22.1%</td>
                        </tr>
                        <tr class="hover:bg-nasa-gray/5 transition-colors">
                            <td class="py-4 px-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                                    <span class="font-medium text-nasa-light">Gold ETF (GLD)</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 font-medium text-nasa-light">+15.2%</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">0.68</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">-18.7%</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">N/A</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">19.8%</td>
                        </tr>
                        <tr class="hover:bg-nasa-gray/5 transition-colors">
                            <td class="py-4 px-4">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-nasa-gray rounded-full mr-3"></div>
                                    <span class="font-medium text-nasa-light">Buy & Hold XAUUSD</span>
                                </div>
                            </td>
                            <td class="py-4 px-4 font-medium text-nasa-light">+42.1%</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">0.89</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">-31.4%</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">N/A</td>
                            <td class="py-4 px-4 font-medium text-nasa-light">25.3%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Tab functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab functionality
    initializeTabs();

    // Initialize page
    initializePage();
});

function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');

            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.add('hidden'));

            // Add active class to clicked button and corresponding pane
            button.classList.add('active');
            document.getElementById(targetTab + '-tab').classList.remove('hidden');
        });
    });
}

function initializePage() {
    // Initialize charts with dark theme
    initializeRollingSharpeChart();
    initializeRecoveryTimeChart();
    initializeProfitabilityHeatmap();
    initializeOutOfSampleChart();
    initializeDegradationChart();
    initializeBenchmarkChart();
    initializeRiskReturnScatter();
}

function initializeRollingSharpeChart() {
    const ctx = document.getElementById('rolling-sharpe-chart');
    if (ctx) {
        // Destroy existing chart if it exists
        if (window.rollingSharpeChart) {
            window.rollingSharpeChart.destroy();
        }

        window.rollingSharpeChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Rolling Sharpe Ratio',
                    data: [2.1, 2.4, 2.8, 2.6, 2.9, 2.87],
                    borderColor: '#22C55E',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}

function initializeRecoveryTimeChart() {
    const ctx = document.getElementById('recovery-time-chart');
    if (ctx) {
        // Destroy existing chart if it exists
        if (window.recoveryTimeChart) {
            window.recoveryTimeChart.destroy();
        }

        window.recoveryTimeChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Recovery Time (Days)',
                    data: [12, 8, 15, 6, 18, 10],
                    backgroundColor: '#EF4444',
                    borderColor: '#EF4444',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}

function initializeProfitabilityHeatmap() {
    const ctx = document.getElementById('profitability-heatmap');
    if (ctx) {
        // This would typically use a specialized heatmap library
        // For now, using a simple bar chart as placeholder
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                datasets: [{
                    label: 'Quarterly Returns (%)',
                    data: [12.4, 8.7, 15.2, 9.1],
                    backgroundColor: ['#22C55E', '#22C55E', '#22C55E', '#22C55E'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}

function initializeOutOfSampleChart() {
    const ctx = document.getElementById('out-of-sample-chart');
    if (ctx) {
        // Destroy existing chart if it exists
        if (window.outOfSampleChart) {
            window.outOfSampleChart.destroy();
        }

        window.outOfSampleChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
                datasets: [{
                    label: 'Out-of-Sample Performance',
                    data: [2.1, 3.4, 5.2, 7.8, 12.1, 24.7],
                    borderColor: '#22C55E',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}

function initializeDegradationChart() {
    const ctx = document.getElementById('degradation-chart');
    if (ctx) {
        // Destroy existing chart if it exists
        if (window.degradationChart) {
            window.degradationChart.destroy();
        }

        window.degradationChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
                datasets: [{
                    label: 'Model Accuracy Degradation',
                    data: [89.2, 88.7, 88.1, 87.8, 87.5, 87.3],
                    borderColor: '#EF4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}

function initializeBenchmarkChart() {
    const ctx = document.getElementById('benchmark-comparison-chart');
    if (ctx) {
        // Destroy existing chart if it exists
        if (window.benchmarkChart) {
            window.benchmarkChart.destroy();
        }

        window.benchmarkChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'AI Trading System',
                    data: [0, 8.2, 15.7, 23.1, 34.8, 42.3, 58.9, 67.2, 78.4, 89.7, 105.2, 127.4],
                    borderColor: '#22C55E',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 3
                }, {
                    label: 'S&P 500',
                    data: [0, 2.1, 4.8, 7.2, 9.8, 12.4, 15.1, 17.8, 20.2, 22.9, 25.7, 28.7],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2
                }, {
                    label: 'Gold ETF',
                    data: [0, 1.2, 2.8, 4.1, 6.7, 8.9, 10.2, 11.8, 12.9, 13.7, 14.5, 15.2],
                    borderColor: '#EAB308',
                    backgroundColor: 'rgba(234, 179, 8, 0.1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}

function initializeRiskReturnScatter() {
    const ctx = document.getElementById('risk-return-scatter');
    if (ctx) {
        new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'AI Trading System',
                    data: [{x: 18.4, y: 127.4}],
                    backgroundColor: '#22C55E',
                    borderColor: '#22C55E',
                    pointRadius: 8
                }, {
                    label: 'S&P 500',
                    data: [{x: 22.1, y: 28.7}],
                    backgroundColor: '#3B82F6',
                    borderColor: '#3B82F6',
                    pointRadius: 6
                }, {
                    label: 'Gold ETF',
                    data: [{x: 19.8, y: 15.2}],
                    backgroundColor: '#EAB308',
                    borderColor: '#EAB308',
                    pointRadius: 6
                }, {
                    label: 'Buy & Hold XAUUSD',
                    data: [{x: 25.3, y: 42.1}],
                    backgroundColor: '#64748B',
                    borderColor: '#64748B',
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Volatility (%)',
                            color: '#64748B'
                        },
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Total Return (%)',
                            color: '#64748B'
                        },
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }
}
</script>

<style>
/* Tab Styles */
.tab-button {
    @apply px-4 py-2 text-sm font-medium text-nasa-gray border-b-2 border-transparent hover:text-nasa-light hover:border-nasa-gray/30 transition-all duration-200;
}

.tab-button.active {
    @apply text-nasa-accent border-nasa-accent;
}

.tab-pane {
    @apply mt-8;
}
</style>
{% endblock %}
