"""
CatBoost Ensemble Trainer for XAUUSD Hierarchical Decision-Making System
Level 2: Market Context Analysis

Specializes in:
- Market session analysis (Asian/European/US/Overlap)
- Volatility regime detection (Low/Normal/High/Extreme)
- Day-of-week patterns and time-based market behavior
- Categorical feature optimization for market context

Author: Quantitative Trading System
Date: September 2025
"""

import os
import sys
import json
import time
import warnings
import joblib
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Tuple, Optional

# CatBoost and ML libraries
from catboost import CatBoostClassifier, CatBoostRegressor
import optuna
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, r2_score, mean_squared_error
from sklearn.calibration import calibration_curve

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')
optuna.logging.set_verbosity(optuna.logging.WARNING)

# Setup logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CatBoostEnsembleTrainer:
    """
    Comprehensive CatBoost ensemble trainer for Market Context Analysis.
    
    Specializes in categorical features and market regime detection for
    Level 2 of the hierarchical decision-making system.
    """
    
    def __init__(self):
        """Initialize the CatBoost ensemble trainer."""
        
        logger.info("CatBoost Ensemble Trainer initialized for Market Context Analysis")
        
        # Training configuration optimized for CatBoost
        self.config = {
            'optuna_trials': 100,
            'cv_folds': 5,
            'early_stopping_rounds': 50,
            'random_state': 42,
            'verbose': False,
            'thread_count': -1,
            'task_type': 'CPU',  # Can be changed to 'GPU' if available
            'bootstrap_type': 'Bayesian',  # CatBoost-specific
            'sampling_frequency': 'PerTreeLevel',  # CatBoost-specific
            'leaf_estimation_method': 'Newton',  # CatBoost-specific
            'grow_policy': 'SymmetricTree',  # CatBoost-specific
        }
        
        # Model specifications for hierarchical system
        self.model_specs = {
            'signal_direction': {
                'type': 'classifier',
                'objective': 'MultiClass',
                'eval_metric': 'MultiClass',
                'classes_count': 3  # -1, 0, 1
            },
            'signal_probability': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'optimal_entry_long': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'optimal_entry_short': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'tp1_long': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'tp2_long': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'tp1_short': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'tp2_short': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'sl_long': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            },
            'sl_short': {
                'type': 'regressor',
                'objective': 'RMSE',
                'eval_metric': 'RMSE'
            }
        }
        
        # Storage for trained models and metadata
        self.models = {}
        self.feature_columns = []
        self.label_columns = []
        self.categorical_features = []
        self.model_metadata = {}
        self.feature_importance = {}
        self.validation_results = {}
        
        # Load training data
        self._load_training_data()
        
        # Identify categorical features for CatBoost optimization
        self._identify_categorical_features()
    
    def _load_training_data(self):
        """Load the training-ready dataset."""
        
        logger.info("📊 LOADING TRAINING-READY DATASET...")
        
        # Load datasets
        train_path = "data/final_training_ready/train_final_20250923_005430.csv"
        val_path = "data/final_training_ready/val_final_20250923_005430.csv"
        test_path = "data/final_training_ready/test_final_20250923_005430.csv"
        
        self.train_data = pd.read_csv(train_path)
        self.val_data = pd.read_csv(val_path)
        self.test_data = pd.read_csv(test_path)
        
        # Identify feature and label columns
        label_cols = [
            'signal_direction', 'signal_probability',
            'optimal_entry_long', 'optimal_entry_short',
            'tp1_long', 'tp2_long', 'tp1_short', 'tp2_short',
            'sl_long', 'sl_short'
        ]

        # Exclude timestamp and date columns more comprehensively
        exclude_cols = label_cols + ['timestamp', 'date', 'time', 'datetime']
        # Also exclude any column that looks like a timestamp
        exclude_cols.extend([col for col in self.train_data.columns if
                           any(term in col.lower() for term in ['timestamp', 'date', 'time']) or
                           self.train_data[col].dtype == 'datetime64[ns]' or
                           (self.train_data[col].dtype == 'object' and
                            self.train_data[col].astype(str).str.contains(r'\d{4}-\d{2}-\d{2}').any())])

        self.feature_columns = [col for col in self.train_data.columns if col not in exclude_cols]
        self.label_columns = [col for col in label_cols if col in self.train_data.columns]
        
        logger.info(f"✅ Loaded training data:")
        logger.info(f"   Train: {len(self.train_data):,} records")
        logger.info(f"   Validation: {len(self.val_data):,} records")
        logger.info(f"   Test: {len(self.test_data):,} records")
        logger.info(f"   Features: {len(self.feature_columns)}")
        logger.info(f"   Labels: {len(self.label_columns)}")
    
    def _identify_categorical_features(self):
        """Identify categorical features for CatBoost optimization."""
        
        # CatBoost excels with categorical features - identify them
        categorical_patterns = [
            'session', 'regime', 'day_of_week', 'hour', 'minute',
            'volatility_state', 'trend_state', 'market_state',
            'signal_type', 'pattern_type', 'breakout_type'
        ]
        
        self.categorical_features = []
        for col in self.feature_columns:
            # Check if column name contains categorical patterns
            if any(pattern in col.lower() for pattern in categorical_patterns):
                self.categorical_features.append(col)
            # Check if column has limited unique values (likely categorical)
            elif self.train_data[col].nunique() <= 20 and self.train_data[col].dtype in ['object', 'int64']:
                self.categorical_features.append(col)
        
        # Convert categorical features to proper format for CatBoost
        valid_categorical_features = []
        for col in self.categorical_features:
            if col in self.train_data.columns:
                # Check if this column is actually suitable for categorical treatment
                unique_values = self.train_data[col].nunique()
                if unique_values <= 50:  # Only treat as categorical if reasonable number of categories
                    # Clean any string/datetime values that shouldn't be there
                    self.train_data[col] = pd.to_numeric(self.train_data[col], errors='coerce')
                    self.val_data[col] = pd.to_numeric(self.val_data[col], errors='coerce')
                    self.test_data[col] = pd.to_numeric(self.test_data[col], errors='coerce')

                    # Fill NaN values with -1 for categorical features (CatBoost handles this)
                    self.train_data[col] = self.train_data[col].fillna(-1)
                    self.val_data[col] = self.val_data[col].fillna(-1)
                    self.test_data[col] = self.test_data[col].fillna(-1)

                    # Convert to integer for CatBoost categorical features
                    self.train_data[col] = self.train_data[col].astype(int)
                    self.val_data[col] = self.val_data[col].astype(int)
                    self.test_data[col] = self.test_data[col].astype(int)

                    valid_categorical_features.append(col)

        # Update categorical features list to only include valid ones
        self.categorical_features = valid_categorical_features

        # Clean all feature columns to ensure they're numeric
        for col in self.feature_columns:
            if col in self.train_data.columns:
                self.train_data[col] = pd.to_numeric(self.train_data[col], errors='coerce')
                self.val_data[col] = pd.to_numeric(self.val_data[col], errors='coerce')
                self.test_data[col] = pd.to_numeric(self.test_data[col], errors='coerce')

                # Fill NaN values with 0
                self.train_data[col] = self.train_data[col].fillna(0)
                self.val_data[col] = self.val_data[col].fillna(0)
                self.test_data[col] = self.test_data[col].fillna(0)
        
        logger.info(f"🏷️ Identified {len(self.categorical_features)} categorical features for CatBoost optimization")
        if self.categorical_features:
            logger.info(f"   Categorical features: {self.categorical_features[:10]}{'...' if len(self.categorical_features) > 10 else ''}")
    
    def _create_catboost_model(self, model_name: str, best_params: Dict[str, Any]) -> Any:
        """Create CatBoost model with optimized parameters."""
        
        spec = self.model_specs[model_name]
        
        # Base parameters for all CatBoost models
        base_params = {
            'random_state': self.config['random_state'],
            'verbose': self.config['verbose'],
            'thread_count': self.config['thread_count'],
            'task_type': self.config['task_type'],
            'bootstrap_type': self.config['bootstrap_type'],
            'sampling_frequency': self.config['sampling_frequency'],
            'leaf_estimation_method': self.config['leaf_estimation_method'],
            'grow_policy': self.config['grow_policy'],
            'early_stopping_rounds': self.config['early_stopping_rounds'],
        }
        
        # Merge with optimized parameters
        params = {**base_params, **best_params}
        
        if spec['type'] == 'classifier':
            params.update({
                'objective': spec['objective'],
                'eval_metric': spec['eval_metric'],
                'classes_count': spec['classes_count']
            })
            return CatBoostClassifier(**params)
        else:
            params.update({
                'objective': spec['objective'],
                'eval_metric': spec['eval_metric']
            })
            return CatBoostRegressor(**params)
    
    def _optimize_hyperparameters(self, model_name: str, X_train: pd.DataFrame,
                                 y_train: pd.Series, X_val: pd.DataFrame,
                                 y_val: pd.Series, class_mapping: Dict = None) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna for CatBoost."""
        
        logger.info(f"🔧 OPTIMIZING HYPERPARAMETERS FOR {model_name.upper()}...")
        
        spec = self.model_specs[model_name]
        
        def objective(trial):
            # CatBoost-specific hyperparameter space
            params = {
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'depth': trial.suggest_int('depth', 3, 10),
                'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 1, 10),
                'border_count': trial.suggest_int('border_count', 32, 255),
                'bagging_temperature': trial.suggest_float('bagging_temperature', 0, 1),
                'random_strength': trial.suggest_float('random_strength', 0, 10),
                'iterations': trial.suggest_int('iterations', 100, 1000),
            }
            
            # Create model
            model = self._create_catboost_model(model_name, params)
            
            # Prepare categorical feature indices
            cat_features = [i for i, col in enumerate(X_train.columns) if col in self.categorical_features]
            
            # Train model
            model.fit(
                X_train, y_train,
                eval_set=(X_val, y_val),
                cat_features=cat_features,
                verbose=False,
                plot=False
            )
            
            # Get validation predictions
            if spec['type'] == 'classifier':
                val_pred = model.predict(X_val)
                score = 1 - accuracy_score(y_val, val_pred)  # Minimize error
            else:
                val_pred = model.predict(X_val)
                score = mean_squared_error(y_val, val_pred)
            
            return score
        
        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=self.config['optuna_trials'], show_progress_bar=True)
        
        logger.info(f"✅ Best parameters for {model_name}: {study.best_params}")
        logger.info(f"   Best score: {study.best_value:.6f}")
        
        return study.best_params
    
    def train_ensemble(self) -> bool:
        """Train the complete CatBoost ensemble."""
        
        logger.info("🚀 STARTING CATBOOST ENSEMBLE TRAINING")
        logger.info("=" * 70)
        
        try:
            for model_name in self.model_specs.keys():
                logger.info(f"📊 Processing {model_name} ({self.model_specs[model_name]['type']})...")
                
                # Prepare data
                X_train = self.train_data[self.feature_columns].copy()
                X_val = self.val_data[self.feature_columns].copy()
                y_train = self.train_data[model_name].copy()
                y_val = self.val_data[model_name].copy()

                # For classification models, map classes to start from 0
                class_mapping = None
                if self.model_specs[model_name]['type'] == 'classifier':
                    unique_classes = sorted(y_train.dropna().unique())
                    class_mapping = {cls: idx for idx, cls in enumerate(unique_classes)}
                    y_train = y_train.map(class_mapping)
                    y_val = y_val.map(class_mapping)

                # Remove invalid targets
                valid_mask_train = ~(pd.isna(y_train) | np.isinf(y_train))
                valid_mask_val = ~(pd.isna(y_val) | np.isinf(y_val))

                X_train_clean = X_train[valid_mask_train]
                y_train_clean = y_train[valid_mask_train]
                X_val_clean = X_val[valid_mask_val]
                y_val_clean = y_val[valid_mask_val]
                
                logger.info(f"   Valid targets: {len(y_train_clean):,}")
                
                if len(y_train_clean) < 100:
                    logger.warning(f"⚠️ Insufficient data for {model_name}, skipping...")
                    continue
                
                # Optimize hyperparameters
                best_params = self._optimize_hyperparameters(
                    model_name, X_train_clean, y_train_clean, X_val_clean, y_val_clean, class_mapping
                )
                
                # Train final model
                logger.info(f"🎯 TRAINING {model_name.upper()} MODEL...")
                model = self._create_catboost_model(model_name, best_params)
                
                # Prepare categorical feature indices
                cat_features = [i for i, col in enumerate(X_train_clean.columns) if col in self.categorical_features]
                
                # Train with early stopping and validation monitoring
                start_time = time.time()
                
                model.fit(
                    X_train_clean, y_train_clean,
                    eval_set=(X_val_clean, y_val_clean),
                    cat_features=cat_features,
                    verbose=False,
                    plot=False
                )
                
                training_time = time.time() - start_time
                
                # Store model and metadata
                self.models[model_name] = model
                
                # Calculate validation metrics
                val_pred = model.predict(X_val_clean)
                
                if self.model_specs[model_name]['type'] == 'classifier':
                    val_accuracy = accuracy_score(y_val_clean, val_pred)
                    val_score = val_accuracy
                    metric_name = "Accuracy"
                else:
                    val_r2 = r2_score(y_val_clean, val_pred)
                    val_rmse = np.sqrt(mean_squared_error(y_val_clean, val_pred))
                    val_score = val_r2
                    metric_name = "R²"
                
                # Store metadata
                self.model_metadata[model_name] = {
                    'model_type': 'catboost',
                    'task_type': self.model_specs[model_name]['type'],
                    'training_samples': len(X_train_clean),
                    'validation_samples': len(X_val_clean),
                    'training_time_seconds': training_time,
                    'best_iteration': model.get_best_iteration(),
                    'hyperparameters': best_params,
                    'categorical_features': len(cat_features),
                    'validation_score': val_score,
                    'class_mapping': class_mapping if class_mapping else None
                }
                
                # Store feature importance
                if hasattr(model, 'get_feature_importance'):
                    importance = model.get_feature_importance()
                    self.feature_importance[model_name] = dict(zip(X_train_clean.columns, importance))
                
                logger.info(f"✅ {model_name} training completed:")
                logger.info(f"   Training samples: {len(X_train_clean):,}")
                logger.info(f"   Validation samples: {len(X_val_clean):,}")
                logger.info(f"   Training time: {training_time:.2f}s")
                logger.info(f"   Best iteration: {model.get_best_iteration()}")
                logger.info(f"   Validation {metric_name}: {val_score:.4f}")
                if self.model_specs[model_name]['type'] == 'regressor':
                    logger.info(f"   Validation RMSE: {val_rmse:.6f}")
                logger.info(f"   Categorical features used: {len(cat_features)}")
                logger.info(f"✅ {model_name} model trained successfully")
            
            logger.info("=" * 70)
            logger.info("🎉 ENSEMBLE TRAINING COMPLETED")
            logger.info(f"   Models trained: {len(self.models)}")
            logger.info(f"   Total features: {len(self.feature_columns)}")
            
            return True

        except Exception as e:
            logger.error(f"❌ Ensemble training failed: {e}")
            return False

    def validate_ensemble(self) -> Dict[str, Any]:
        """Perform comprehensive ensemble validation."""

        logger.info("🔍 PERFORMING COMPREHENSIVE ENSEMBLE VALIDATION...")

        validation_results = {}

        # 1. Signal accuracy validation
        logger.info("📊 Validating signal accuracy...")
        signal_accuracy = self._validate_signal_accuracy()
        validation_results['signal_accuracy'] = signal_accuracy

        # 2. Probability calibration validation
        logger.info("📈 Validating probability calibration...")
        calibration_results = self._validate_calibration()
        validation_results['calibration'] = calibration_results

        # 3. Temporal stability validation
        logger.info("⏰ Validating temporal stability...")
        temporal_stability = self._validate_temporal_stability()
        validation_results['temporal_stability'] = temporal_stability

        # 4. Risk-reward validation
        logger.info("⚖️ Validating risk-reward ratios...")
        risk_reward = self._validate_risk_reward()
        validation_results['risk_reward'] = risk_reward

        # 5. Inference speed test
        logger.info("⚡ Testing inference speed...")
        speed_results = self._test_inference_speed()
        validation_results['inference_speed'] = speed_results

        # 6. Market context analysis validation (CatBoost specialty)
        logger.info("🏛️ Validating market context analysis...")
        context_analysis = self._validate_market_context_analysis()
        validation_results['market_context'] = context_analysis

        # Overall assessment
        overall_assessment = self._assess_overall_performance(validation_results)
        validation_results['overall_assessment'] = overall_assessment

        logger.info("✅ Comprehensive validation completed")

        return validation_results

    def _validate_signal_accuracy(self) -> Dict[str, Any]:
        """Validate signal accuracy against forward price movements."""

        if 'signal_direction' not in self.models:
            return {'error': 'Signal direction model not trained'}

        model = self.models['signal_direction']
        X_test = self.test_data[self.feature_columns]
        y_test = self.test_data['signal_direction']

        # Remove invalid data
        valid_mask = ~(pd.isna(y_test) | np.isinf(y_test))
        X_test_clean = X_test[valid_mask]
        y_test_clean = y_test[valid_mask]

        # Get predictions
        cat_features = [i for i, col in enumerate(X_test_clean.columns) if col in self.categorical_features]
        predictions = model.predict(X_test_clean)

        # Calculate metrics
        accuracy = accuracy_score(y_test_clean, predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(y_test_clean, predictions, average=None, zero_division=0)

        # Handle class distribution for potentially negative class labels
        unique_classes, class_counts = np.unique(y_test_clean, return_counts=True)
        class_distribution = dict(zip(unique_classes.tolist(), class_counts.tolist()))

        return {
            'test_accuracy': accuracy,
            'precision_by_class': precision.tolist(),
            'recall_by_class': recall.tolist(),
            'f1_by_class': f1.tolist(),
            'class_distribution': class_distribution,
            'meets_threshold': accuracy >= 0.55
        }

    def _validate_calibration(self) -> Dict[str, Any]:
        """Validate probability calibration."""

        if 'signal_probability' not in self.models:
            return {'error': 'Signal probability model not trained'}

        model = self.models['signal_probability']
        X_test = self.test_data[self.feature_columns]
        y_test = self.test_data['signal_probability']

        # Remove invalid data
        valid_mask = ~(pd.isna(y_test) | np.isinf(y_test))
        X_test_clean = X_test[valid_mask]
        y_test_clean = y_test[valid_mask]

        # Get predictions
        predictions = model.predict(X_test_clean)

        # Calculate calibration metrics
        rmse = np.sqrt(mean_squared_error(y_test_clean, predictions))
        r2 = r2_score(y_test_clean, predictions)

        # Calibration error (simplified)
        calibration_error = np.mean(np.abs(predictions - y_test_clean))

        return {
            'rmse': rmse,
            'r2_score': r2,
            'calibration_error': calibration_error,
            'meets_threshold': calibration_error < 0.10
        }

    def _validate_temporal_stability(self) -> Dict[str, Any]:
        """Validate temporal stability using walk-forward analysis."""

        if 'signal_direction' not in self.models:
            return {'error': 'Signal direction model not trained'}

        # Use TimeSeriesSplit for temporal validation
        tscv = TimeSeriesSplit(n_splits=5)
        X = self.train_data[self.feature_columns]
        y = self.train_data['signal_direction']

        # Remove invalid data
        valid_mask = ~(pd.isna(y) | np.isinf(y))
        X_clean = X[valid_mask]
        y_clean = y[valid_mask]

        fold_scores = []
        cat_features = [i for i, col in enumerate(X_clean.columns) if col in self.categorical_features]

        for fold, (train_idx, val_idx) in enumerate(tscv.split(X_clean)):
            X_fold_train, X_fold_val = X_clean.iloc[train_idx], X_clean.iloc[val_idx]
            y_fold_train, y_fold_val = y_clean.iloc[train_idx], y_clean.iloc[val_idx]

            # Create and train model for this fold
            temp_model = CatBoostClassifier(
                iterations=200,
                learning_rate=0.1,
                depth=6,
                verbose=False,
                random_state=42
            )

            temp_model.fit(
                X_fold_train, y_fold_train,
                cat_features=cat_features,
                verbose=False
            )

            # Predict and score
            fold_pred = temp_model.predict(X_fold_val)
            fold_accuracy = accuracy_score(y_fold_val, fold_pred)
            fold_scores.append(fold_accuracy)

        stability_score = np.std(fold_scores)
        mean_score = np.mean(fold_scores)

        return {
            'fold_scores': fold_scores,
            'mean_accuracy': mean_score,
            'stability_std': stability_score,
            'is_stable': stability_score < 0.05
        }

    def _validate_risk_reward(self) -> Dict[str, Any]:
        """Validate risk-reward ratios from TP/SL predictions."""

        required_models = ['tp1_long', 'tp1_short', 'sl_long', 'sl_short']
        if not all(model in self.models for model in required_models):
            return {'error': 'Required TP/SL models not trained'}

        X_test = self.test_data[self.feature_columns]

        # Get predictions for all TP/SL models
        predictions = {}
        for model_name in required_models:
            model = self.models[model_name]
            pred = model.predict(X_test)
            predictions[model_name] = pred

        # Calculate risk-reward ratios
        long_rr = predictions['tp1_long'] / np.abs(predictions['sl_long'])
        short_rr = predictions['tp1_short'] / np.abs(predictions['sl_short'])

        # Remove invalid ratios
        long_rr_clean = long_rr[(long_rr > 0) & (long_rr < 10)]
        short_rr_clean = short_rr[(short_rr > 0) & (short_rr < 10)]

        avg_rr = np.mean(np.concatenate([long_rr_clean, short_rr_clean]))

        return {
            'average_risk_reward': avg_rr,
            'long_avg_rr': np.mean(long_rr_clean),
            'short_avg_rr': np.mean(short_rr_clean),
            'meets_threshold': avg_rr >= 1.5
        }

    def _test_inference_speed(self) -> Dict[str, Any]:
        """Test inference speed for live trading readiness."""

        if not self.models:
            return {'error': 'No models trained'}

        # Test with sample data
        sample_data = self.test_data[self.feature_columns].iloc[:100]

        # Time single prediction
        start_time = time.time()
        for model_name, model in self.models.items():
            _ = model.predict(sample_data.iloc[:1])
        single_time = time.time() - start_time

        # Time batch prediction
        start_time = time.time()
        for model_name, model in self.models.items():
            _ = model.predict(sample_data)
        batch_time = time.time() - start_time

        return {
            'single_prediction_time': single_time,
            'batch_prediction_time': batch_time,
            'predictions_per_second': 100 / batch_time,
            'meets_threshold': single_time < 30.0
        }

    def _validate_market_context_analysis(self) -> Dict[str, Any]:
        """Validate CatBoost's market context analysis capabilities."""

        # Analyze categorical feature importance
        categorical_importance = {}
        for model_name, importance_dict in self.feature_importance.items():
            cat_importance = {k: v for k, v in importance_dict.items() if k in self.categorical_features}
            categorical_importance[model_name] = cat_importance

        # Calculate average importance of categorical features
        all_cat_importance = []
        for model_importance in categorical_importance.values():
            all_cat_importance.extend(model_importance.values())

        avg_cat_importance = np.mean(all_cat_importance) if all_cat_importance else 0

        # Analyze session-specific performance (if session features exist)
        session_analysis = self._analyze_session_performance()

        return {
            'categorical_features_count': len(self.categorical_features),
            'avg_categorical_importance': avg_cat_importance,
            'categorical_importance_by_model': categorical_importance,
            'session_analysis': session_analysis,
            'context_specialization_score': avg_cat_importance * len(self.categorical_features)
        }

    def _analyze_session_performance(self) -> Dict[str, Any]:
        """Analyze performance by market session (CatBoost specialty)."""

        # Look for session-related features
        session_features = [col for col in self.feature_columns if 'session' in col.lower()]

        if not session_features or 'signal_direction' not in self.models:
            return {'error': 'No session features or signal model available'}

        # Simple session analysis
        return {
            'session_features_found': len(session_features),
            'session_features': session_features[:5],  # Show first 5
            'analysis': 'Session-specific analysis requires live market data'
        }

    def _assess_overall_performance(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall ensemble performance and assign grade."""

        # Scoring criteria (same as LightGBM for consistency)
        criteria = {
            'signal_accuracy_55pct': False,
            'no_overfitting': False,
            'calibration_error_10pct': False,
            'risk_reward_1_5': False,
            'inference_speed_30s': False,
            'market_context_specialization': False
        }

        score = 0
        max_score = 120  # 6 criteria * 20 points each

        # Signal accuracy (20 points)
        if validation_results.get('signal_accuracy', {}).get('meets_threshold', False):
            criteria['signal_accuracy_55pct'] = True
            score += 20

        # No overfitting (20 points) - based on temporal stability
        temporal = validation_results.get('temporal_stability', {})
        if temporal.get('is_stable', False) and temporal.get('mean_accuracy', 0) > 0.45:
            criteria['no_overfitting'] = True
            score += 20

        # Calibration error (20 points)
        if validation_results.get('calibration', {}).get('meets_threshold', False):
            criteria['calibration_error_10pct'] = True
            score += 20

        # Risk-reward ratio (20 points)
        if validation_results.get('risk_reward', {}).get('meets_threshold', False):
            criteria['risk_reward_1_5'] = True
            score += 20

        # Inference speed (20 points)
        if validation_results.get('inference_speed', {}).get('meets_threshold', False):
            criteria['inference_speed_30s'] = True
            score += 20

        # Market context specialization (20 points) - CatBoost specific
        context = validation_results.get('market_context', {})
        if context.get('context_specialization_score', 0) > 10:  # Threshold for specialization
            criteria['market_context_specialization'] = True
            score += 20

        # Calculate grade
        percentage = (score / max_score) * 100

        if percentage >= 90:
            grade = 'A'
        elif percentage >= 80:
            grade = 'B'
        elif percentage >= 70:
            grade = 'C'
        elif percentage >= 60:
            grade = 'D'
        else:
            grade = 'F'

        ready_for_live = percentage >= 90 and all([
            criteria['signal_accuracy_55pct'],
            criteria['no_overfitting'],
            criteria['inference_speed_30s']
        ])

        return {
            'overall_score': score,
            'max_score': max_score,
            'percentage': percentage,
            'overall_grade': grade,
            'ready_for_live_trading': ready_for_live,
            'criteria_met': criteria,
            'recommendations': self._generate_recommendations(criteria, validation_results)
        }

    def _generate_recommendations(self, criteria: Dict[str, bool],
                                validation_results: Dict[str, Any]) -> List[str]:
        """Generate specific recommendations for improvement."""

        recommendations = []

        if not criteria['signal_accuracy_55pct']:
            recommendations.append("Improve signal accuracy through better categorical feature engineering")
            recommendations.append("Implement market regime-specific model training")

        if not criteria['calibration_error_10pct']:
            recommendations.append("Apply CatBoost-specific probability calibration techniques")
            recommendations.append("Use target encoding for high-cardinality categorical features")

        if not criteria['risk_reward_1_5']:
            recommendations.append("Optimize TP/SL models with session-specific parameters")
            recommendations.append("Implement volatility-adjusted risk management")

        if not criteria['market_context_specialization']:
            recommendations.append("Add more market session and regime categorical features")
            recommendations.append("Implement time-of-day and day-of-week categorical encoding")

        return recommendations

    def save_ensemble(self) -> Dict[str, str]:
        """Save the trained ensemble models and metadata."""

        logger.info("💾 SAVING TRAINED ENSEMBLE MODELS...")

        # Create models directory
        models_dir = Path("models/catboost_ensemble")
        models_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = {}

        # Save individual models
        for model_name, model in self.models.items():
            model_path = models_dir / f"catboost_{model_name}_{timestamp}.cbm"
            model.save_model(str(model_path))
            saved_files[model_name] = str(model_path)
            logger.info(f"✅ Saved {model_name} model: {model_path}")

        # Save ensemble metadata
        ensemble_metadata = {
            'timestamp': timestamp,
            'models': list(self.models.keys()),
            'feature_columns': self.feature_columns,
            'label_columns': self.label_columns,
            'categorical_features': self.categorical_features,
            'model_metadata': self.model_metadata,
            'feature_importance': self.feature_importance,
            'validation_results': self.validation_results,
            'config': self.config,
            'saved_files': saved_files
        }

        metadata_path = models_dir / f"ensemble_metadata_{timestamp}.json"
        with open(metadata_path, 'w') as f:
            json.dump(ensemble_metadata, f, indent=2, default=str)

        saved_files['metadata'] = str(metadata_path)

        logger.info(f"✅ Saved ensemble metadata: {metadata_path}")
        logger.info(f"📁 Total files saved: {len(saved_files)}")

        return saved_files

    def create_hierarchical_wrapper(self) -> 'HierarchicalCatBoostWrapper':
        """Create hierarchical model wrapper for live trading integration."""

        return HierarchicalCatBoostWrapper(
            models=self.models,
            feature_columns=self.feature_columns,
            categorical_features=self.categorical_features,
            model_metadata=self.model_metadata
        )


class HierarchicalCatBoostWrapper:
    """
    Hierarchical wrapper for CatBoost ensemble specialized for Market Context Analysis.
    Level 2 in the hierarchical decision-making system.
    """

    def __init__(self, models: Dict[str, Any], feature_columns: List[str],
                 categorical_features: List[str], model_metadata: Dict[str, Any]):
        """Initialize the hierarchical wrapper."""

        self.models = models
        self.feature_columns = feature_columns
        self.categorical_features = categorical_features
        self.model_metadata = model_metadata

        logger.info(f"Hierarchical CatBoost Wrapper initialized with {len(models)} models")
        logger.info(f"Specialized for Market Context Analysis with {len(categorical_features)} categorical features")

    def predict(self, features: pd.DataFrame) -> Dict[str, Any]:
        """
        Make hierarchical predictions specialized for market context analysis.

        Args:
            features: DataFrame with feature columns

        Returns:
            Dictionary with predictions in live trading format + market context analysis
        """

        start_time = time.time()

        # Ensure features are in correct format
        if not isinstance(features, pd.DataFrame):
            features = pd.DataFrame(features, columns=self.feature_columns)

        # Select only the features used in training
        feature_data = features[self.feature_columns]

        # Get predictions from all models
        predictions = {}

        for model_name, model in self.models.items():
            try:
                pred = model.predict(feature_data)
                if len(pred) == 1:
                    predictions[model_name] = float(pred[0])
                else:
                    predictions[model_name] = pred.tolist()
            except Exception as e:
                logger.warning(f"Prediction failed for {model_name}: {e}")
                predictions[model_name] = 0.0

        # Convert to live trading format
        trading_prediction = self._convert_to_trading_format(predictions)

        # Add market context analysis (CatBoost specialty)
        market_context = self._analyze_market_context(feature_data)

        # Add metadata
        trading_prediction.update({
            'model_confidence': self._calculate_model_confidence(predictions),
            'market_context_analysis': market_context,
            'level': 2,
            'model_type': 'catboost',
            'specialization': 'market_context_analysis',
            'timestamp': datetime.now(),
            'inference_time_ms': (time.time() - start_time) * 1000
        })

        return trading_prediction

    def _convert_to_trading_format(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Convert model predictions to live trading format."""

        # Extract signal direction (convert to int)
        signal_direction = int(predictions.get('signal_direction', 0))

        # Extract signal probability (ensure in valid range)
        signal_probability = float(predictions.get('signal_probability', 0.5))
        signal_probability = max(0.5, min(1.0, signal_probability))

        # Extract price levels
        optimal_entry_long = float(predictions.get('optimal_entry_long', 0.0))
        optimal_entry_short = float(predictions.get('optimal_entry_short', 0.0))

        tp1_long = float(predictions.get('tp1_long', 0.0))
        tp2_long = float(predictions.get('tp2_long', 0.0))
        tp1_short = float(predictions.get('tp1_short', 0.0))
        tp2_short = float(predictions.get('tp2_short', 0.0))

        sl_long = float(predictions.get('sl_long', 0.0))
        sl_short = float(predictions.get('sl_short', 0.0))

        return {
            'signal_direction': signal_direction,
            'signal_probability': signal_probability,
            'optimal_entry_long': optimal_entry_long,
            'optimal_entry_short': optimal_entry_short,
            'tp1_long': tp1_long,
            'tp2_long': tp2_long,
            'tp1_short': tp1_short,
            'tp2_short': tp2_short,
            'sl_long': sl_long,
            'sl_short': sl_short
        }

    def _analyze_market_context(self, features: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market context using categorical features (CatBoost specialty)."""

        context_analysis = {
            'session_analysis': {},
            'volatility_regime': 'unknown',
            'market_regime': 'unknown',
            'time_context': {},
            'categorical_insights': {}
        }

        # Analyze session-related features
        session_features = [col for col in features.columns if 'session' in col.lower()]
        if session_features:
            for feature in session_features[:3]:  # Analyze top 3 session features
                if feature in features.columns:
                    value = features[feature].iloc[0] if len(features) > 0 else 'unknown'
                    context_analysis['session_analysis'][feature] = str(value)

        # Analyze volatility regime features
        vol_features = [col for col in features.columns if any(term in col.lower() for term in ['vol', 'regime', 'state'])]
        if vol_features:
            vol_feature = vol_features[0]
            if vol_feature in features.columns:
                vol_value = features[vol_feature].iloc[0] if len(features) > 0 else 'unknown'
                context_analysis['volatility_regime'] = str(vol_value)

        # Analyze time-based features
        time_features = [col for col in features.columns if any(term in col.lower() for term in ['hour', 'day', 'time'])]
        if time_features:
            for feature in time_features[:2]:  # Analyze top 2 time features
                if feature in features.columns:
                    value = features[feature].iloc[0] if len(features) > 0 else 'unknown'
                    context_analysis['time_context'][feature] = str(value)

        # Categorical feature insights
        cat_insights = {}
        for cat_feature in self.categorical_features[:5]:  # Top 5 categorical features
            if cat_feature in features.columns:
                value = features[cat_feature].iloc[0] if len(features) > 0 else 'unknown'
                cat_insights[cat_feature] = str(value)

        context_analysis['categorical_insights'] = cat_insights

        return context_analysis

    def _calculate_model_confidence(self, predictions: Dict[str, Any]) -> float:
        """Calculate overall model confidence with market context weighting."""

        # Use signal probability as base confidence
        base_confidence = predictions.get('signal_probability', 0.5)

        # Adjust based on signal strength
        signal_direction = predictions.get('signal_direction', 0)
        if signal_direction == 0:  # Hold signal
            return base_confidence * 0.8  # Lower confidence for hold
        else:
            # CatBoost specialization: higher confidence for categorical pattern recognition
            return min(base_confidence * 1.1, 1.0)  # Slight boost for categorical expertise

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded models."""

        return {
            'model_count': len(self.models),
            'model_names': list(self.models.keys()),
            'feature_count': len(self.feature_columns),
            'categorical_feature_count': len(self.categorical_features),
            'specialization': 'market_context_analysis',
            'level': 2,
            'model_type': 'catboost',
            'metadata': self.model_metadata
        }


def main():
    """Main training execution function."""

    logger.info("🎯 CATBOOST ENSEMBLE TRAINING FOR XAUUSD HIERARCHICAL SYSTEM")
    logger.info("🏛️ LEVEL 2: MARKET CONTEXT ANALYSIS")
    logger.info("=" * 80)

    try:
        # Initialize trainer
        trainer = CatBoostEnsembleTrainer()

        # Train ensemble
        if not trainer.train_ensemble():
            logger.error("❌ Ensemble training failed")
            return False

        # Validate ensemble
        validation_results = trainer.validate_ensemble()
        trainer.validation_results = validation_results

        # Save ensemble
        saved_files = trainer.save_ensemble()

        # Create hierarchical wrapper
        wrapper = trainer.create_hierarchical_wrapper()

        # Print final summary
        logger.info("=" * 80)
        logger.info("🎉 CATBOOST ENSEMBLE TRAINING COMPLETED")
        logger.info("=" * 80)

        logger.info(f"📊 TRAINING SUMMARY:")
        logger.info(f"   Models trained: {len(trainer.models)}")
        logger.info(f"   Features used: {len(trainer.feature_columns)}")
        logger.info(f"   Categorical features: {len(trainer.categorical_features)}")
        logger.info(f"   Files saved: {len(saved_files)}")

        if 'overall_assessment' in validation_results:
            assessment = validation_results['overall_assessment']
            logger.info(f"📈 VALIDATION RESULTS:")
            logger.info(f"   Overall grade: {assessment.get('overall_grade', 'N/A')}")
            logger.info(f"   Ready for live trading: {assessment.get('ready_for_live_trading', False)}")

            if 'criteria_met' in assessment:
                criteria = assessment['criteria_met']
                logger.info(f"   Signal accuracy ≥55%: {criteria.get('signal_accuracy_55pct', False)}")
                logger.info(f"   No overfitting: {criteria.get('no_overfitting', False)}")
                logger.info(f"   Calibration error <10%: {criteria.get('calibration_error_10pct', False)}")
                logger.info(f"   Risk-reward ≥1.5:1: {criteria.get('risk_reward_1_5', False)}")
                logger.info(f"   Inference speed <30s: {criteria.get('inference_speed_30s', False)}")
                logger.info(f"   Market context specialization: {criteria.get('market_context_specialization', False)}")

        logger.info(f"🏛️ MARKET CONTEXT ANALYSIS SPECIALIZATION:")
        logger.info(f"   Level 2 hierarchical decision-making ready")
        logger.info(f"   Categorical feature optimization complete")
        logger.info(f"   Session and regime analysis capabilities enabled")

        logger.info(f"🚀 NEXT STEPS:")
        logger.info(f"   1. Review validation results and recommendations")
        logger.info(f"   2. Test hierarchical wrapper with live data")
        logger.info(f"   3. Integrate with Level 1 (Linear) and Level 3 (LightGBM)")
        logger.info(f"   4. Proceed with XGBoost Level 4 training")

        return True

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
