{% extends "base.html" %}

{% block title %}Results Analysis - {{ super() }}{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-nasa-light mb-2">
                <i class="fas fa-chart-bar text-nasa-accent mr-3"></i>
                Results Analysis
            </h1>
            <p class="text-nasa-gray">Comprehensive trade history and performance analysis</p>
        </div>
        <div class="flex items-center mt-4 sm:mt-0 space-x-4">
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-500/20 text-blue-400">
                <i class="fas fa-clock mr-2"></i>
                Last Updated: <span id="last-update">--:--:--</span>
            </span>
            <button class="inline-flex items-center px-4 py-2 bg-nasa-accent hover:bg-nasa-accent/80 text-nasa-dark font-medium rounded-lg transition-colors">
                <i class="fas fa-download mr-2"></i>
                Export CSV
            </button>
        </div>
    </div>
</div>

<!-- Summary KPIs -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Trades -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-exchange-alt text-nasa-accent text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Total Trades</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="total-trades">0</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                    Active
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-calendar mr-2"></i>
            Last 12 Months
        </div>
    </div>

    <!-- Win Rate -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-percentage text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Win Rate</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="win-rate">0.0%</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                    Excellent
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-bullseye mr-2"></i>
            908 Winning Trades
        </div>
    </div>

    <!-- Average Trade -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-dollar-sign text-purple-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Avg Trade</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="avg-trade">$0.00</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                    +8.2%
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-chart-line mr-2"></i>
            Per Trade Return
        </div>
    </div>

    <!-- Profit Factor -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-trophy text-yellow-500 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Profit Factor</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="profit-factor">1.85</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-500">
                    Strong
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-balance-scale mr-2"></i>
            Risk-Reward Ratio
        </div>
    </div>
</div>

<!-- Trade History Table -->
<div class="trading-card mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h3 class="text-lg font-semibold text-nasa-light mb-4 sm:mb-0">
            <i class="fas fa-history text-nasa-accent mr-2"></i>
            Trade History
        </h3>

        <!-- Filters and Search -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <!-- Search -->
            <div class="relative">
                <input type="text" id="trade-search" placeholder="Search trades..."
                       class="w-full sm:w-64 px-4 py-2 pl-10 bg-nasa-dark/50 border border-nasa-gray/20 rounded-lg text-nasa-light placeholder-nasa-gray focus:outline-none focus:ring-2 focus:ring-nasa-accent focus:border-transparent">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-nasa-gray"></i>
            </div>

            <!-- Date Filter -->
            <select id="date-filter" class="px-4 py-2 bg-nasa-dark/50 border border-nasa-gray/20 rounded-lg text-nasa-light focus:outline-none focus:ring-2 focus:ring-nasa-accent">
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
            </select>

            <!-- Status Filter -->
            <select id="status-filter" class="px-4 py-2 bg-nasa-dark/50 border border-nasa-gray/20 rounded-lg text-nasa-light focus:outline-none focus:ring-2 focus:ring-nasa-accent">
                <option value="all">All Trades</option>
                <option value="win">Winning Trades</option>
                <option value="loss">Losing Trades</option>
                <option value="breakeven">Breakeven</option>
            </select>
        </div>
    </div>

    <!-- Trade Table -->
    <div class="overflow-x-auto">
        <table class="w-full" id="trades-table">
            <thead>
                <tr class="border-b border-nasa-gray/20">
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="date">
                        Date <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="symbol">
                        Symbol <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="side">
                        Side <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="entry">
                        Entry <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="exit">
                        Exit <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="pnl">
                        P&L <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="return">
                        Return % <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray cursor-pointer hover:text-nasa-light" data-sort="duration">
                        Duration <i class="fas fa-sort ml-1"></i>
                    </th>
                    <th class="text-left py-4 px-4 text-sm font-medium text-nasa-gray">
                        Signal
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-nasa-gray/10" id="trades-tbody">
                <!-- Real trade history will be populated here via JavaScript -->
                <!-- If no trades, show empty state -->
                <tr id="no-trades-row">
                    <td colspan="9" class="py-12 px-4 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <div class="w-16 h-16 bg-nasa-dark/30 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-history text-nasa-gray text-2xl"></i>
                            </div>
                            <p class="text-nasa-gray text-lg font-medium mb-2">No Trade History</p>
                            <p class="text-nasa-gray/70 text-sm">No trades have been executed yet</p>
                            <p class="text-nasa-gray/50 text-xs mt-2">Real-time data from live trading system</p>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="flex items-center justify-between mt-6 pt-6 border-t border-nasa-gray/20">
        <div class="text-sm text-nasa-gray">
            Showing <span class="font-medium text-nasa-light">0</span> of <span class="font-medium text-nasa-light">0</span> trades
        </div>
        <div class="flex items-center space-x-2">
            <button class="px-3 py-2 text-sm font-medium text-nasa-gray hover:text-nasa-light border border-nasa-gray/20 rounded-lg hover:bg-nasa-gray/10 transition-colors">
                Previous
            </button>
            <button class="px-3 py-2 text-sm font-medium bg-nasa-accent text-nasa-dark rounded-lg">
                1
            </button>
            <button class="px-3 py-2 text-sm font-medium text-nasa-gray hover:text-nasa-light border border-nasa-gray/20 rounded-lg hover:bg-nasa-gray/10 transition-colors">
                2
            </button>
            <button class="px-3 py-2 text-sm font-medium text-nasa-gray hover:text-nasa-light border border-nasa-gray/20 rounded-lg hover:bg-nasa-gray/10 transition-colors">
                3
            </button>
            <button class="px-3 py-2 text-sm font-medium text-nasa-gray hover:text-nasa-light border border-nasa-gray/20 rounded-lg hover:bg-nasa-gray/10 transition-colors">
                Next
            </button>
        </div>
    </div>
</div>

<!-- Performance Breakdown Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Equity Curve -->
    <div class="chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-chart-line text-nasa-accent mr-2"></i>
                Equity Curve
            </h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-accent/20 text-nasa-accent">1Y</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">6M</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">3M</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">1M</button>
            </div>
        </div>
        <div class="h-80">
            <canvas id="equity-curve-chart"></canvas>
        </div>
    </div>

    <!-- Monthly Returns Heatmap -->
    <div class="chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-calendar-alt text-nasa-accent mr-2"></i>
                Monthly Returns
            </h3>
        </div>
        <div class="h-80">
            <canvas id="monthly-returns-chart"></canvas>
        </div>
    </div>
</div>

<!-- Performance by Asset and Win/Loss Distribution -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Performance by Asset -->
    <div class="chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-coins text-nasa-accent mr-2"></i>
                Performance by Asset
            </h3>
        </div>
        <div class="h-80">
            <canvas id="asset-performance-chart"></canvas>
        </div>
    </div>

    <!-- Win/Loss Distribution -->
    <div class="chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-chart-pie text-nasa-accent mr-2"></i>
                Win/Loss Distribution
            </h3>
        </div>
        <div class="h-80">
            <canvas id="win-loss-chart"></canvas>
        </div>
    </div>
</div>

<!-- Detailed Statistics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Trading Statistics -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-chart-bar text-nasa-accent mr-2"></i>
                Trading Statistics
            </h3>
        </div>

        <div class="space-y-4">
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Total Trades</span>
                <span class="font-semibold text-nasa-light">1,247</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Winning Trades</span>
                <span class="font-semibold text-nasa-light">0 (0.0%)</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Losing Trades</span>
                <span class="font-semibold text-nasa-light">0 (0.0%)</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Average Win</span>
                <span class="font-semibold text-nasa-light">$0.00</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Average Loss</span>
                <span class="font-semibold text-nasa-light">$0.00</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Largest Win</span>
                <span class="font-semibold text-nasa-accent">$127.80</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Largest Loss</span>
                <span class="font-semibold text-nasa-red">-$45.20</span>
            </div>
            <div class="flex items-center justify-between py-3">
                <span class="text-nasa-gray">Profit Factor</span>
                <span class="font-semibold text-nasa-light">1.85</span>
            </div>
        </div>
    </div>

    <!-- Risk Metrics -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-shield-alt text-nasa-accent mr-2"></i>
                Risk Metrics
            </h3>
        </div>

        <div class="space-y-4">
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Sharpe Ratio</span>
                <span class="font-semibold text-nasa-light">2.87</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Sortino Ratio</span>
                <span class="font-semibold text-nasa-light">4.12</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Calmar Ratio</span>
                <span class="font-semibold text-nasa-light">10.35</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Maximum Drawdown</span>
                <span class="font-semibold text-nasa-red">-12.3%</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Recovery Time</span>
                <span class="font-semibold text-nasa-light">18 days</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Volatility</span>
                <span class="font-semibold text-nasa-light">18.4%</span>
            </div>
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <span class="text-nasa-gray">Beta</span>
                <span class="font-semibold text-nasa-light">0.72</span>
            </div>
            <div class="flex items-center justify-between py-3">
                <span class="text-nasa-gray">Alpha</span>
                <span class="font-semibold text-nasa-accent">15.8%</span>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
// Initialize results page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize page functionality
    initializePage();

    // Update last update time
    updateLastUpdateTime();

    // Set up real-time updates if WebSocket is available
    if (window.dashboardInstance && window.dashboardInstance.websocket) {
        console.log('Results page initialized with real-time updates');
    }
});

function initializePage() {
    // Initialize charts
    initializeCharts();

    // Initialize table functionality
    initializeTableFeatures();

    // Initialize filters
    initializeFilters();
}

function initializeCharts() {
    // Destroy existing charts if they exist
    if (window.equityChart) {
        window.equityChart.destroy();
    }
    if (window.monthlyChart) {
        window.monthlyChart.destroy();
    }
    if (window.assetChart) {
        window.assetChart.destroy();
    }
    if (window.winLossChart) {
        window.winLossChart.destroy();
    }

    // Equity Curve Chart
    const equityCtx = document.getElementById('equity-curve-chart');
    if (equityCtx) {
        window.equityChart = new Chart(equityCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Portfolio Value',
                    data: [10000, 10850, 11420, 12180, 11950, 12680, 13240, 12890, 13560, 14120, 14780, 15520],
                    borderColor: '#22C55E',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }

    // Monthly Returns Chart
    const monthlyCtx = document.getElementById('monthly-returns-chart');
    if (monthlyCtx) {
        window.monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Monthly Return (%)',
                    data: [8.5, 5.7, 7.6, -1.9, 6.1, 4.4, -2.7, 5.2, 4.3, 4.1, 4.5, 5.0],
                    backgroundColor: function(context) {
                        return context.parsed.y >= 0 ? '#22C55E' : '#EF4444';
                    },
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    }
                }
            }
        });
    }

    // Asset Performance Chart
    const assetCtx = document.getElementById('asset-performance-chart');
    if (assetCtx) {
        window.assetChart = new Chart(assetCtx, {
            type: 'doughnut',
            data: {
                labels: ['XAUUSD', 'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'],
                datasets: [{
                    data: [45.2, 23.8, 15.6, 10.4, 5.0],
                    backgroundColor: ['#22C55E', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                }
            }
        });
    }

    // Win/Loss Distribution Chart
    const winLossCtx = document.getElementById('win-loss-chart');
    if (winLossCtx) {
        window.winLossChart = new Chart(winLossCtx, {
            type: 'pie',
            data: {
                labels: ['Winning Trades', 'Losing Trades'],
                datasets: [{
                    data: [72.8, 27.2],
                    backgroundColor: ['#22C55E', '#EF4444'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                }
            }
        });
    }
}

function initializeTableFeatures() {
    // Table sorting functionality
    const sortHeaders = document.querySelectorAll('[data-sort]');
    sortHeaders.forEach(header => {
        header.addEventListener('click', () => {
            const sortBy = header.getAttribute('data-sort');
            sortTable(sortBy);
        });
    });
}

function initializeFilters() {
    // Search functionality
    const searchInput = document.getElementById('trade-search');
    if (searchInput) {
        searchInput.addEventListener('input', filterTrades);
    }

    // Date filter
    const dateFilter = document.getElementById('date-filter');
    if (dateFilter) {
        dateFilter.addEventListener('change', filterTrades);
    }

    // Status filter
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', filterTrades);
    }
}

function sortTable(column) {
    // Table sorting logic would go here
    console.log('Sorting by:', column);
}

function filterTrades() {
    // Trade filtering logic would go here
    const searchTerm = document.getElementById('trade-search').value;
    const dateFilter = document.getElementById('date-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    console.log('Filtering trades:', { searchTerm, dateFilter, statusFilter });
}

function updateLastUpdateTime() {
    const timeElement = document.getElementById('last-update');
    if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString();
    }
}

// Export functionality
function exportToCSV() {
    // CSV export logic would go here
    console.log('Exporting trades to CSV...');
}
</script>
{% endblock %}
