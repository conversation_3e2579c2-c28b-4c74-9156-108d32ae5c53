"""
Model-Driven Entry/Exit Logic for Live Trading

Implements sophisticated entry timing, position sizing, and exit management
based on specialized ensemble model predictions and market conditions.
"""

from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
import numpy as np
import pandas as pd
try:
    from utils.logging_utils import LoggerMixin
except ImportError:
    # Fallback for missing utils
    import logging
    class LoggerMixin:
        def __init__(self):
            self.logger = logging.getLogger(self.__class__.__name__)


class EntryTiming(Enum):
    """Entry timing strategies."""
    IMMEDIATE = "immediate"
    PATIENT = "patient"
    MOMENTUM_CHASE = "momentum_chase"
    TECHNICAL_LEVEL = "technical_level"
    WAIT = "wait"


class ExitStrategy(Enum):
    """Exit strategy types."""
    MULTI_TIER = "multi_tier"
    TRAILING_STOP = "trailing_stop"
    TIME_BASED = "time_based"
    MODEL_SIGNAL = "model_signal"


@dataclass
class EntryDecision:
    """Entry decision with timing and sizing."""
    should_enter: bool
    timing_strategy: EntryTiming
    optimal_entry_price: float
    patience_time_seconds: int
    position_size_multiplier: float
    confidence_level: float
    reasoning: str


@dataclass
class ExitLevels:
    """Multi-tier exit level configuration."""
    tp1_price: float
    tp1_percentage: float  # Percentage of position to close
    tp2_price: float
    tp2_percentage: float
    trailing_price: float
    trailing_percentage: float
    sl_price: float
    max_hold_time_minutes: int


@dataclass
class PositionManagement:
    """Position management parameters."""
    base_position_size: float
    confidence_multiplier: float
    volatility_adjustment: float
    regime_multiplier: float
    risk_multiplier: float
    final_position_size: float


class ModelDrivenExecutionEngine(LoggerMixin):
    """
    Model-driven execution engine for optimal entry/exit timing.
    
    Uses specialized ensemble predictions to determine:
    - Optimal entry timing and price levels
    - Dynamic position sizing based on confidence
    - Multi-tier take profit and stop loss levels
    - Trailing stop management
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        
        # Entry timing parameters
        self.entry_patience_seconds = config.get('entry_patience_seconds', 300)
        self.price_improvement_threshold = config.get('price_improvement_threshold', 2.0)  # pips
        
        # Position sizing parameters
        self.base_position_size = config.get('base_position_size', 0.1)
        self.max_position_multiplier = config.get('max_position_multiplier', 2.0)
        self.min_position_multiplier = config.get('min_position_multiplier', 0.1)
        
        # Multi-tier TP/SL parameters
        self.tp1_percentage = config.get('tp1_percentage', 0.4)  # 40% quick profits
        self.tp2_percentage = config.get('tp2_percentage', 0.35)  # 35% swing profits
        self.trailing_percentage = config.get('tp3_percentage', 0.25)  # 25% trend following
        
        # Risk management
        self.max_risk_per_trade = config.get('max_risk_per_trade', 0.02)
        self.volatility_adjustment = config.get('volatility_adjustment', True)
        
        self.logger.info("Model-Driven Execution Engine initialized")
    
    def analyze_entry_opportunity(self, hierarchical_decision: Dict[str, Any],
                                model_predictions: Dict[str, Any],
                                market_data: pd.DataFrame) -> EntryDecision:
        """
        Analyze entry opportunity using model predictions.
        
        Args:
            hierarchical_decision: Result from hierarchical decision framework
            model_predictions: Predictions from all specialized models
            market_data: Current market data
            
        Returns:
            EntryDecision with optimal entry strategy
        """
        try:
            # Check if entry is recommended
            if hierarchical_decision.get('final_decision') not in ['BUY', 'SELL']:
                return EntryDecision(
                    should_enter=False,
                    timing_strategy=EntryTiming.WAIT,
                    optimal_entry_price=0.0,
                    patience_time_seconds=0,
                    position_size_multiplier=0.0,
                    confidence_level=0.0,
                    reasoning="No entry signal from hierarchical decision"
                )
            
            # Get current market price
            current_price = market_data['close'].iloc[-1]
            direction = hierarchical_decision['final_decision']
            confidence = hierarchical_decision.get('overall_confidence', 0.0)
            
            # Determine entry timing strategy
            timing_strategy = self._determine_entry_timing(
                hierarchical_decision, model_predictions, market_data
            )
            
            # Calculate optimal entry price
            optimal_price = self._calculate_optimal_entry_price(
                current_price, direction, timing_strategy, model_predictions, market_data
            )
            
            # Calculate patience time
            patience_time = self._calculate_patience_time(
                timing_strategy, confidence, model_predictions
            )
            
            # Calculate position size multiplier
            position_multiplier = self._calculate_position_size_multiplier(
                hierarchical_decision, model_predictions
            )
            
            return EntryDecision(
                should_enter=True,
                timing_strategy=timing_strategy,
                optimal_entry_price=optimal_price,
                patience_time_seconds=patience_time,
                position_size_multiplier=position_multiplier,
                confidence_level=confidence,
                reasoning=f"Entry strategy: {timing_strategy.value}, confidence: {confidence:.3f}"
            )
            
        except Exception as e:
            self.logger.error(f"Entry analysis failed: {str(e)}")
            return EntryDecision(
                should_enter=False,
                timing_strategy=EntryTiming.WAIT,
                optimal_entry_price=0.0,
                patience_time_seconds=0,
                position_size_multiplier=0.0,
                confidence_level=0.0,
                reasoning=f"Entry analysis error: {str(e)}"
            )
    
    def calculate_exit_levels(self, entry_price: float, direction: str,
                            hierarchical_decision: Dict[str, Any],
                            model_predictions: Dict[str, Any]) -> ExitLevels:
        """
        Calculate multi-tier exit levels using model predictions.
        
        Args:
            entry_price: Entry price of the position
            direction: 'BUY' or 'SELL'
            hierarchical_decision: Hierarchical decision result
            model_predictions: Model predictions
            
        Returns:
            ExitLevels with complete exit strategy
        """
        try:
            # Get TP/SL distances from hierarchical decision
            tp_sl_levels = hierarchical_decision.get('tp_sl_levels', {})
            
            tp1_distance = tp_sl_levels.get('tp1_distance', 15.0)
            tp2_distance = tp_sl_levels.get('tp2_distance', 30.0)
            sl_distance = tp_sl_levels.get('sl_distance', 20.0)
            
            # Convert pips to price levels
            pip_value = 0.1  # For XAUUSD, 1 pip = 0.1
            
            if direction == 'BUY':
                tp1_price = entry_price + (tp1_distance * pip_value)
                tp2_price = entry_price + (tp2_distance * pip_value)
                trailing_price = entry_price + (tp2_distance * 1.5 * pip_value)
                sl_price = entry_price - (sl_distance * pip_value)
            else:  # SELL
                tp1_price = entry_price - (tp1_distance * pip_value)
                tp2_price = entry_price - (tp2_distance * pip_value)
                trailing_price = entry_price - (tp2_distance * 1.5 * pip_value)
                sl_price = entry_price + (sl_distance * pip_value)
            
            # Calculate max hold time from model predictions
            lightgbm_pred = model_predictions.get('lightgbm', {})
            hold_time_estimate = lightgbm_pred.get('hold_time_estimate', 60.0)  # minutes
            max_hold_time = int(hold_time_estimate * 2)  # Allow 2x estimated time
            
            return ExitLevels(
                tp1_price=tp1_price,
                tp1_percentage=self.tp1_percentage,
                tp2_price=tp2_price,
                tp2_percentage=self.tp2_percentage,
                trailing_price=trailing_price,
                trailing_percentage=self.trailing_percentage,
                sl_price=sl_price,
                max_hold_time_minutes=max_hold_time
            )
            
        except Exception as e:
            self.logger.error(f"Exit level calculation failed: {str(e)}")
            # Return safe default levels
            return self._get_default_exit_levels(entry_price, direction)
    
    def _determine_entry_timing(self, hierarchical_decision: Dict[str, Any],
                              model_predictions: Dict[str, Any],
                              market_data: pd.DataFrame) -> EntryTiming:
        """Determine optimal entry timing strategy."""
        
        entry_timing = hierarchical_decision.get('entry_timing', 'PATIENT')
        confidence = hierarchical_decision.get('overall_confidence', 0.0)
        
        # High confidence signals - immediate entry
        if confidence >= 0.8 and entry_timing == 'IMMEDIATE':
            return EntryTiming.IMMEDIATE
        
        # Check for momentum conditions
        lightgbm_pred = model_predictions.get('lightgbm', {})
        if lightgbm_pred.get('momentum_strength', 0.0) > 0.7:
            return EntryTiming.MOMENTUM_CHASE
        
        # Check for technical level opportunities
        if self._near_technical_level(market_data):
            return EntryTiming.TECHNICAL_LEVEL
        
        # Default to patient entry
        return EntryTiming.PATIENT
    
    def _calculate_optimal_entry_price(self, current_price: float, direction: str,
                                     timing_strategy: EntryTiming,
                                     model_predictions: Dict[str, Any],
                                     market_data: pd.DataFrame) -> float:
        """Calculate optimal entry price based on timing strategy."""
        
        pip_value = 0.1
        
        if timing_strategy == EntryTiming.IMMEDIATE:
            # Enter at market price
            return current_price
        
        elif timing_strategy == EntryTiming.MOMENTUM_CHASE:
            # Chase momentum with slight premium
            if direction == 'BUY':
                return current_price + (2 * pip_value)  # Pay 2 pips premium
            else:
                return current_price - (2 * pip_value)
        
        elif timing_strategy == EntryTiming.PATIENT:
            # Wait for better price
            improvement = self.price_improvement_threshold * pip_value
            if direction == 'BUY':
                return current_price - improvement  # Buy lower
            else:
                return current_price + improvement  # Sell higher
        
        elif timing_strategy == EntryTiming.TECHNICAL_LEVEL:
            # Enter at technical level
            return self._get_nearest_technical_level(current_price, direction, market_data)
        
        else:
            return current_price
    
    def _calculate_patience_time(self, timing_strategy: EntryTiming, confidence: float,
                               model_predictions: Dict[str, Any]) -> int:
        """Calculate how long to wait for optimal entry."""
        
        base_patience = self.entry_patience_seconds
        
        if timing_strategy == EntryTiming.IMMEDIATE:
            return 0
        elif timing_strategy == EntryTiming.MOMENTUM_CHASE:
            return min(30, base_patience // 10)  # Very short patience for momentum
        elif timing_strategy == EntryTiming.PATIENT:
            # Longer patience for high confidence signals
            return int(base_patience * (0.5 + confidence))
        elif timing_strategy == EntryTiming.TECHNICAL_LEVEL:
            return base_patience * 2  # More patience for technical levels
        else:
            return base_patience
    
    def _calculate_position_size_multiplier(self, hierarchical_decision: Dict[str, Any],
                                          model_predictions: Dict[str, Any]) -> float:
        """Calculate position size multiplier based on model confidence with detailed logging."""

        self.logger.info("=== MODEL-DRIVEN POSITION SIZE MULTIPLIER CALCULATION ===")

        # Get base multiplier from hierarchical decision (this should be the ensemble multiplier!)
        base_multiplier = hierarchical_decision.get('position_sizing', 1.0)
        self.logger.info(f"Base multiplier from hierarchical decision: {base_multiplier:.3f}")

        # Apply confidence adjustment
        confidence = hierarchical_decision.get('overall_confidence', 0.0)
        confidence_multiplier = 0.5 + (confidence * 1.5)  # 0.5 to 2.0 range
        self.logger.info(f"Confidence: {confidence:.3f} → multiplier: {confidence_multiplier:.3f}")

        # Apply consensus adjustment
        consensus_score = hierarchical_decision.get('consensus_score', 0.0)
        consensus_multiplier = 0.8 + (consensus_score * 0.4)  # 0.8 to 1.2 range
        self.logger.info(f"Consensus: {consensus_score:.3f} → multiplier: {consensus_multiplier:.3f}")

        # Combine multipliers
        final_multiplier = base_multiplier * confidence_multiplier * consensus_multiplier
        self.logger.info(f"Combined: {base_multiplier:.3f} × {confidence_multiplier:.3f} × {consensus_multiplier:.3f} = {final_multiplier:.3f}")

        # Apply limits
        original_multiplier = final_multiplier
        final_multiplier = max(self.min_position_multiplier,
                              min(self.max_position_multiplier, final_multiplier))

        if final_multiplier != original_multiplier:
            self.logger.info(f"Multiplier limited: {original_multiplier:.3f} → {final_multiplier:.3f}")

        self.logger.info(f"FINAL POSITION SIZE MULTIPLIER: {final_multiplier:.3f}")
        self.logger.info("=== END MULTIPLIER CALCULATION ===")

        return final_multiplier
    
    def _near_technical_level(self, market_data: pd.DataFrame) -> bool:
        """Check if price is near a technical level."""
        # Simple implementation - could be enhanced with more sophisticated technical analysis
        current_price = market_data['close'].iloc[-1]
        recent_high = market_data['high'].tail(20).max()
        recent_low = market_data['low'].tail(20).min()
        
        # Check if near recent high/low
        pip_value = 0.1
        threshold = 5 * pip_value  # Within 5 pips
        
        return (abs(current_price - recent_high) < threshold or 
                abs(current_price - recent_low) < threshold)
    
    def _get_nearest_technical_level(self, current_price: float, direction: str,
                                   market_data: pd.DataFrame) -> float:
        """Get nearest technical level for entry."""
        recent_high = market_data['high'].tail(20).max()
        recent_low = market_data['low'].tail(20).min()
        
        if direction == 'BUY':
            # Enter near support (recent low)
            return recent_low + (0.1 * 2)  # 2 pips above support
        else:
            # Enter near resistance (recent high)
            return recent_high - (0.1 * 2)  # 2 pips below resistance
    
    def _get_default_exit_levels(self, entry_price: float, direction: str) -> ExitLevels:
        """Get safe default exit levels."""
        pip_value = 0.1
        
        if direction == 'BUY':
            tp1_price = entry_price + (15 * pip_value)
            tp2_price = entry_price + (30 * pip_value)
            trailing_price = entry_price + (45 * pip_value)
            sl_price = entry_price - (20 * pip_value)
        else:
            tp1_price = entry_price - (15 * pip_value)
            tp2_price = entry_price - (30 * pip_value)
            trailing_price = entry_price - (45 * pip_value)
            sl_price = entry_price + (20 * pip_value)
        
        return ExitLevels(
            tp1_price=tp1_price,
            tp1_percentage=self.tp1_percentage,
            tp2_price=tp2_price,
            tp2_percentage=self.tp2_percentage,
            trailing_price=trailing_price,
            trailing_percentage=self.trailing_percentage,
            sl_price=sl_price,
            max_hold_time_minutes=120
        )
