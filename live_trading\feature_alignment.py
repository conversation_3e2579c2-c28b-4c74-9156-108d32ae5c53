"""
Feature Alignment System for Live Trading

Ensures that live trading feature generation produces exactly the same 354 features
as the training dataset, maintaining the proven profitability metrics.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Set, Optional
from pathlib import Path
import logging

class FeatureAlignmentSystem:
    """
    System to align live trading features with training dataset features.
    
    Ensures that live trading generates exactly the same 354 features that
    the specialized ensemble models were trained on.
    """
    
    def __init__(self, training_features_path: str = "data/features/train_features_391.csv"):
        """
        Initialize feature alignment system.
        
        Args:
            training_features_path: Path to training features CSV file
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.training_features_path = training_features_path
        self.expected_features = None
        self.feature_mapping = {}
        self.load_expected_features()
    
    def load_expected_features(self):
        """Load expected features from training dataset."""
        try:
            if Path(self.training_features_path).exists():
                df = pd.read_csv(self.training_features_path)
                # Exclude datetime column
                self.expected_features = [col for col in df.columns if col != 'datetime']
                self.logger.info(f"Loaded {len(self.expected_features)} expected features from training dataset")
                
                # Create feature mapping for common mismatches
                self._create_feature_mapping()
            else:
                self.logger.error(f"Training features file not found: {self.training_features_path}")
                self.expected_features = []
        except Exception as e:
            self.logger.error(f"Error loading expected features: {e}")
            self.expected_features = []
    
    def _create_feature_mapping(self):
        """Create mapping for common feature name mismatches."""
        # Map live trading feature names to training feature names
        self.feature_mapping = {
            # Volume features - map live trading names to training names
            'vol_technical_regime_10': 'vol_regime_10',
            'vol_technical_regime_20': 'vol_regime_20', 
            'vol_technical_regime_50': 'vol_regime_50',
            
            # Remove features that don't exist in training
            'session_asian': None,
            'session_european': None,
            'session_us': None,
            'overlap_asian_european': None,
            'overlap_european_us': None,
            'session_asian_volume_ratio': None,
            'session_asian_volatility_ratio': None,
            'session_european_volume_ratio': None,
            'session_european_volatility_ratio': None,
            'session_us_volume_ratio': None,
            'session_us_volatility_ratio': None,
            'hour_of_day': None,
            'day_of_week': None,
            'day_of_month': None,
            'month_of_year': None,
            'quarter_of_year': None,
            'is_weekend': None,
            'is_month_end': None,
            'is_quarter_end': None,
            'is_year_end': None,
            
            # Volume features that don't exist in training
            'vwap_10': None,
            'vwap_20': None,
            'vwap_50': None,
            'vwap_deviation_10': None,
            'vwap_deviation_20': None,
            'vwap_deviation_50': None,
            'price_above_vwap_10': None,
            'price_above_vwap_20': None,
            'price_above_vwap_50': None,
            'obv': None,
            'obv_sma_10': None,
            'obv_sma_20': None,
            'obv_momentum': None,
            'volume_z_score': None,
            'volume_spike': None,
            'volume_percentile': None,
            'price_volume_corr_20': None,
            'price_volume_corr_50': None,
            'volume_price_divergence_20': None,
            'volume_price_divergence_50': None,
            'volume_sma_10': None,
            'volume_sma_20': None,
            'volume_sma_50': None,
            'volume_ratio_10': None,
            'volume_ratio_20': None,
            'volume_ratio_50': None,
            'volume_trend_10': None,
            'volume_trend_20': None,
            'volume_trend_50': None,
        }
    
    def align_features(self, live_features: pd.DataFrame) -> pd.DataFrame:
        """
        Align live trading features with training dataset features.
        
        Args:
            live_features: DataFrame with live trading features
            
        Returns:
            DataFrame with aligned features (354 features matching training)
        """
        if self.expected_features is None:
            self.logger.error("Expected features not loaded")
            return live_features
        
        self.logger.info(f"Aligning features: {live_features.shape[1]} live → {len(self.expected_features)} expected")
        
        # Start with empty aligned features
        aligned_features = pd.DataFrame(index=live_features.index)
        
        # Apply feature mapping and selection
        for expected_feature in self.expected_features:
            if expected_feature in live_features.columns:
                # Direct match
                aligned_features[expected_feature] = live_features[expected_feature]
            else:
                # Check if there's a mapping from live feature to expected feature
                mapped_from = None
                for live_name, expected_name in self.feature_mapping.items():
                    if expected_name == expected_feature and live_name in live_features.columns:
                        mapped_from = live_name
                        break
                
                if mapped_from:
                    # Use mapped feature
                    aligned_features[expected_feature] = live_features[mapped_from]
                    self.logger.debug(f"Mapped {mapped_from} → {expected_feature}")
                else:
                    # Feature missing - fill with zeros or NaN
                    aligned_features[expected_feature] = 0.0
                    self.logger.warning(f"Missing feature filled with zeros: {expected_feature}")
        
        # Log alignment results
        missing_count = (aligned_features == 0.0).all().sum()
        self.logger.info(f"Feature alignment complete: {aligned_features.shape[1]} features, {missing_count} filled with zeros")
        
        return aligned_features
    
    def validate_alignment(self, aligned_features: pd.DataFrame) -> Dict[str, any]:
        """
        Validate that aligned features match training expectations.
        
        Args:
            aligned_features: Aligned features DataFrame
            
        Returns:
            Validation results dictionary
        """
        results = {
            'feature_count_match': len(aligned_features.columns) == len(self.expected_features),
            'feature_names_match': set(aligned_features.columns) == set(self.expected_features),
            'missing_features': set(self.expected_features) - set(aligned_features.columns),
            'extra_features': set(aligned_features.columns) - set(self.expected_features),
            'zero_filled_features': [],
            'alignment_score': 0.0
        }
        
        # Check for zero-filled features
        for col in aligned_features.columns:
            if (aligned_features[col] == 0.0).all():
                results['zero_filled_features'].append(col)
        
        # Calculate alignment score
        if results['feature_count_match'] and results['feature_names_match']:
            zero_filled_ratio = len(results['zero_filled_features']) / len(self.expected_features)
            results['alignment_score'] = 1.0 - zero_filled_ratio
        
        return results
    
    def get_feature_statistics(self) -> Dict[str, any]:
        """Get statistics about expected features."""
        if not self.expected_features:
            return {}
        
        # Analyze feature categories
        categories = {}
        for feature in self.expected_features:
            if '_' in feature:
                prefix = feature.split('_')[0]
                if prefix not in categories:
                    categories[prefix] = 0
                categories[prefix] += 1
        
        return {
            'total_features': len(self.expected_features),
            'feature_categories': categories,
            'sample_features': self.expected_features[:10]
        }
