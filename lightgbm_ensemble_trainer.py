#!/usr/bin/env python3
"""
LightGBM Ensemble Model Trainer for XAUUSD Hierarchical Decision-Making System

This script trains a comprehensive LightGBM ensemble with multiple specialized models
for the XAUUSD trading system, implementing hierarchical decision-making with
professional validation and live trading integration.

Author: Experienced Quantitative Trader
Date: 2025-09-23
"""

import pandas as pd
import numpy as np
import lightgbm as lgb
from lightgbm import LGBMClassifier, LGBMRegressor
import optuna
import joblib
import logging
from pathlib import Path
import json
from datetime import datetime, timedelta
import time
from typing import Dict, List, Tuple, Any, Optional
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import accuracy_score, classification_report, mean_squared_error, r2_score
from sklearn.calibration import calibration_curve
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LightGBMEnsembleTrainer:
    """
    Comprehensive LightGBM ensemble trainer for hierarchical decision-making system.
    
    Trains multiple specialized models:
    - Signal Classification Model (signal_direction)
    - Confidence Regression Model (signal_probability)
    - Entry Price Models (optimal_entry_long/short)
    - Take Profit Models (tp1/tp2 for long/short)
    - Stop Loss Models (sl_long/short)
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the ensemble trainer with configuration."""
        
        self.config = config or self._get_default_config()
        
        # Model storage
        self.models = {}
        self.model_metadata = {}
        self.feature_importance = {}
        self.validation_results = {}
        
        # Training data
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.feature_columns = None
        self.label_columns = None
        
        # Performance tracking
        self.training_history = {}
        self.learning_curves = {}
        
        logger.info("LightGBM Ensemble Trainer initialized")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default training configuration."""
        return {
            'optuna_trials': 100,
            'cv_folds': 5,
            'early_stopping_rounds': 50,
            'random_state': 42,
            'n_jobs': -1,
            'verbose': -1,
            'hyperparameter_ranges': {
                'learning_rate': [0.01, 0.3],
                'num_leaves': [31, 300],
                'max_depth': [3, 15],
                'min_child_samples': [20, 100],
                'subsample': [0.8, 1.0],
                'colsample_bytree': [0.8, 1.0],
                'reg_alpha': [0.0, 1.0],
                'reg_lambda': [0.0, 1.0]
            },
            'model_types': {
                'signal_direction': 'classifier',
                'signal_probability': 'regressor',
                'optimal_entry_long': 'regressor',
                'optimal_entry_short': 'regressor',
                'tp1_long': 'regressor',
                'tp2_long': 'regressor',
                'tp1_short': 'regressor',
                'tp2_short': 'regressor',
                'sl_long': 'regressor',
                'sl_short': 'regressor'
            }
        }
    
    def load_training_data(self) -> bool:
        """Load the final training-ready dataset."""
        
        logger.info("📊 LOADING TRAINING-READY DATASET...")
        
        try:
            # Load all splits
            train_path = Path("data/final_training_ready/train_final_20250923_005430.csv")
            val_path = Path("data/final_training_ready/val_final_20250923_005430.csv")
            test_path = Path("data/final_training_ready/test_final_20250923_005430.csv")
            
            if not all([train_path.exists(), val_path.exists(), test_path.exists()]):
                logger.error("Training-ready dataset files not found")
                return False
            
            # Load datasets
            self.train_data = pd.read_csv(train_path)
            self.val_data = pd.read_csv(val_path)
            self.test_data = pd.read_csv(test_path)
            
            # Convert datetime columns
            for df in [self.train_data, self.val_data, self.test_data]:
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
            
            # Define feature and label columns
            self.label_columns = [
                'signal_direction', 'signal_probability', 'optimal_entry_long', 
                'optimal_entry_short', 'tp1_long', 'tp2_long', 'tp1_short', 
                'tp2_short', 'sl_long', 'sl_short'
            ]
            
            self.feature_columns = [col for col in self.train_data.columns 
                                  if col not in self.label_columns]
            
            logger.info(f"✅ Loaded training data:")
            logger.info(f"  Train: {len(self.train_data):,} records")
            logger.info(f"  Validation: {len(self.val_data):,} records")
            logger.info(f"  Test: {len(self.test_data):,} records")
            logger.info(f"  Features: {len(self.feature_columns)}")
            logger.info(f"  Labels: {len(self.label_columns)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load training data: {e}")
            return False
    
    def optimize_hyperparameters(self, model_name: str, model_type: str) -> Dict[str, Any]:
        """Optimize hyperparameters using Optuna."""
        
        logger.info(f"🔧 OPTIMIZING HYPERPARAMETERS FOR {model_name.upper()}...")
        
        def objective(trial):
            # Sample hyperparameters
            params = {
                'learning_rate': trial.suggest_float('learning_rate', *self.config['hyperparameter_ranges']['learning_rate']),
                'num_leaves': trial.suggest_int('num_leaves', *self.config['hyperparameter_ranges']['num_leaves']),
                'max_depth': trial.suggest_int('max_depth', *self.config['hyperparameter_ranges']['max_depth']),
                'min_child_samples': trial.suggest_int('min_child_samples', *self.config['hyperparameter_ranges']['min_child_samples']),
                'subsample': trial.suggest_float('subsample', *self.config['hyperparameter_ranges']['subsample']),
                'colsample_bytree': trial.suggest_float('colsample_bytree', *self.config['hyperparameter_ranges']['colsample_bytree']),
                'reg_alpha': trial.suggest_float('reg_alpha', *self.config['hyperparameter_ranges']['reg_alpha']),
                'reg_lambda': trial.suggest_float('reg_lambda', *self.config['hyperparameter_ranges']['reg_lambda']),
                'random_state': self.config['random_state'],
                'n_jobs': self.config['n_jobs'],
                'verbose': self.config['verbose']
            }
            
            # Get training data
            X_train = self.train_data[self.feature_columns]
            y_train = self.train_data[model_name]
            X_val = self.val_data[self.feature_columns]
            y_val = self.val_data[model_name]
            
            # Handle missing values in target
            train_mask = ~y_train.isnull()
            val_mask = ~y_val.isnull()
            
            X_train_clean = X_train[train_mask]
            y_train_clean = y_train[train_mask]
            X_val_clean = X_val[val_mask]
            y_val_clean = y_val[val_mask]
            
            if len(X_train_clean) == 0 or len(X_val_clean) == 0:
                return float('inf')
            
            try:
                if model_type == 'classifier':
                    model = LGBMClassifier(**params)
                    model.fit(
                        X_train_clean, y_train_clean,
                        eval_set=[(X_val_clean, y_val_clean)],
                        callbacks=[lgb.early_stopping(self.config['early_stopping_rounds']), lgb.log_evaluation(0)]
                    )
                    y_pred = model.predict(X_val_clean)
                    score = accuracy_score(y_val_clean, y_pred)
                    return 1 - score  # Minimize (1 - accuracy)
                
                else:  # regressor
                    model = LGBMRegressor(**params)
                    model.fit(
                        X_train_clean, y_train_clean,
                        eval_set=[(X_val_clean, y_val_clean)],
                        callbacks=[lgb.early_stopping(self.config['early_stopping_rounds']), lgb.log_evaluation(0)]
                    )
                    y_pred = model.predict(X_val_clean)
                    score = mean_squared_error(y_val_clean, y_pred)
                    return score  # Minimize MSE
                    
            except Exception as e:
                logger.warning(f"Trial failed for {model_name}: {e}")
                return float('inf')
        
        # Run optimization
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=self.config['optuna_trials'], show_progress_bar=True)
        
        best_params = study.best_params
        best_params.update({
            'random_state': self.config['random_state'],
            'n_jobs': self.config['n_jobs'],
            'verbose': self.config['verbose']
        })
        
        logger.info(f"✅ Best parameters for {model_name}: {best_params}")
        logger.info(f"   Best score: {study.best_value:.6f}")
        
        return best_params
    
    def train_single_model(self, model_name: str, model_type: str, 
                          best_params: Dict[str, Any]) -> Tuple[Any, Dict[str, Any]]:
        """Train a single specialized model."""
        
        logger.info(f"🎯 TRAINING {model_name.upper()} MODEL...")
        
        # Get training data
        X_train = self.train_data[self.feature_columns]
        y_train = self.train_data[model_name]
        X_val = self.val_data[self.feature_columns]
        y_val = self.val_data[model_name]
        
        # Handle missing values in target
        train_mask = ~y_train.isnull()
        val_mask = ~y_val.isnull()
        
        X_train_clean = X_train[train_mask]
        y_train_clean = y_train[train_mask]
        X_val_clean = X_val[val_mask]
        y_val_clean = y_val[val_mask]
        
        if len(X_train_clean) == 0:
            logger.error(f"No valid training data for {model_name}")
            return None, {}
        
        # Initialize model
        if model_type == 'classifier':
            model = LGBMClassifier(**best_params)
        else:
            model = LGBMRegressor(**best_params)
        
        # Train with early stopping and validation monitoring
        start_time = time.time()

        eval_results = {}

        if len(X_val_clean) > 0:
            # Train with validation set
            model.fit(
                X_train_clean, y_train_clean,
                eval_set=[(X_train_clean, y_train_clean), (X_val_clean, y_val_clean)],
                eval_names=['train', 'valid'],
                callbacks=[
                    lgb.early_stopping(self.config['early_stopping_rounds']),
                    lgb.log_evaluation(0),
                    lgb.record_evaluation(eval_results)
                ]
            )
        else:
            # Train without validation set
            model.fit(X_train_clean, y_train_clean)

        training_time = time.time() - start_time

        # Get best iteration (handle case where early stopping wasn't used)
        best_iteration = getattr(model, 'best_iteration', getattr(model, 'best_iteration_', len(eval_results.get('train', {}).get(list(eval_results.get('train', {}).keys())[0] if eval_results.get('train') else [], [1])) if eval_results else 1))
        
        # Calculate validation metrics
        if len(X_val_clean) > 0:
            y_val_pred = model.predict(X_val_clean)
            
            if model_type == 'classifier':
                val_accuracy = accuracy_score(y_val_clean, y_val_pred)
                val_metrics = {
                    'accuracy': val_accuracy,
                    'classification_report': classification_report(y_val_clean, y_val_pred, output_dict=True)
                }
            else:
                val_mse = mean_squared_error(y_val_clean, y_val_pred)
                val_r2 = r2_score(y_val_clean, y_val_pred)
                val_metrics = {
                    'mse': val_mse,
                    'rmse': np.sqrt(val_mse),
                    'r2': val_r2
                }
        else:
            val_metrics = {}
        
        # Store feature importance
        feature_importance = dict(zip(self.feature_columns, model.feature_importances_))
        
        # Create metadata
        metadata = {
            'model_name': model_name,
            'model_type': model_type,
            'training_samples': len(X_train_clean),
            'validation_samples': len(X_val_clean),
            'training_time_seconds': training_time,
            'best_iteration': best_iteration,
            'hyperparameters': best_params,
            'validation_metrics': val_metrics,
            'feature_importance': feature_importance,
            'eval_results': eval_results,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"✅ {model_name} training completed:")
        logger.info(f"   Training samples: {len(X_train_clean):,}")
        logger.info(f"   Validation samples: {len(X_val_clean):,}")
        logger.info(f"   Training time: {training_time:.2f}s")
        logger.info(f"   Best iteration: {best_iteration}")
        
        if model_type == 'classifier' and 'accuracy' in val_metrics:
            logger.info(f"   Validation accuracy: {val_metrics['accuracy']:.4f}")
        elif model_type == 'regressor' and 'r2' in val_metrics:
            logger.info(f"   Validation R²: {val_metrics['r2']:.4f}")
            logger.info(f"   Validation RMSE: {val_metrics['rmse']:.6f}")
        
        return model, metadata

    def train_ensemble(self) -> bool:
        """Train the complete LightGBM ensemble."""

        logger.info("🚀 STARTING LIGHTGBM ENSEMBLE TRAINING")
        logger.info("=" * 70)

        try:
            # Load training data
            if not self.load_training_data():
                return False

            # Train each specialized model
            for model_name, model_type in self.config['model_types'].items():

                # Skip if target column doesn't exist or has no valid data
                if model_name not in self.train_data.columns:
                    logger.warning(f"⚠️ Target column {model_name} not found, skipping...")
                    continue

                valid_targets = self.train_data[model_name].dropna()
                if len(valid_targets) == 0:
                    logger.warning(f"⚠️ No valid targets for {model_name}, skipping...")
                    continue

                logger.info(f"📊 Processing {model_name} ({model_type})...")
                logger.info(f"   Valid targets: {len(valid_targets):,}")

                # Optimize hyperparameters
                best_params = self.optimize_hyperparameters(model_name, model_type)

                # Train model
                model, metadata = self.train_single_model(model_name, model_type, best_params)

                if model is not None:
                    self.models[model_name] = model
                    self.model_metadata[model_name] = metadata
                    self.feature_importance[model_name] = metadata['feature_importance']

                    logger.info(f"✅ {model_name} model trained successfully")
                else:
                    logger.error(f"❌ Failed to train {model_name} model")

            logger.info("=" * 70)
            logger.info(f"🎉 ENSEMBLE TRAINING COMPLETED")
            logger.info(f"   Models trained: {len(self.models)}")
            logger.info(f"   Total features: {len(self.feature_columns)}")

            return len(self.models) > 0

        except Exception as e:
            logger.error(f"❌ Ensemble training failed: {e}")
            return False

    def validate_ensemble(self) -> Dict[str, Any]:
        """Perform comprehensive ensemble validation."""

        logger.info("🔍 PERFORMING COMPREHENSIVE ENSEMBLE VALIDATION...")

        validation_results = {
            'signal_accuracy': {},
            'calibration_results': {},
            'temporal_stability': {},
            'risk_reward_validation': {},
            'inference_speed': {},
            'overall_assessment': {}
        }

        try:
            # 1. Signal Accuracy Validation
            logger.info("📊 Validating signal accuracy...")
            validation_results['signal_accuracy'] = self._validate_signal_accuracy()

            # 2. Probability Calibration Validation
            logger.info("📈 Validating probability calibration...")
            validation_results['calibration_results'] = self._validate_calibration()

            # 3. Temporal Stability Validation
            logger.info("⏰ Validating temporal stability...")
            validation_results['temporal_stability'] = self._validate_temporal_stability()

            # 4. Risk-Reward Validation
            logger.info("⚖️ Validating risk-reward ratios...")
            validation_results['risk_reward_validation'] = self._validate_risk_reward()

            # 5. Inference Speed Testing
            logger.info("⚡ Testing inference speed...")
            validation_results['inference_speed'] = self._test_inference_speed()

            # 6. Overall Assessment
            validation_results['overall_assessment'] = self._calculate_overall_assessment(validation_results)

            logger.info("✅ Comprehensive validation completed")

            return validation_results

        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            return validation_results

    def _validate_signal_accuracy(self) -> Dict[str, Any]:
        """Validate signal accuracy against forward price movements."""

        if 'signal_direction' not in self.models:
            return {'error': 'Signal direction model not found'}

        # Get test predictions
        X_test = self.test_data[self.feature_columns]
        y_test = self.test_data['signal_direction'].dropna()

        if len(y_test) == 0:
            return {'error': 'No valid test targets'}

        # Get predictions
        test_mask = ~self.test_data['signal_direction'].isnull()
        X_test_clean = X_test[test_mask]
        y_test_clean = y_test

        y_pred = self.models['signal_direction'].predict(X_test_clean)

        # Calculate accuracy metrics
        overall_accuracy = accuracy_score(y_test_clean, y_pred)

        # Separate accuracy by signal type
        long_mask = y_test_clean == 1
        short_mask = y_test_clean == -1
        hold_mask = y_test_clean == 0

        long_accuracy = accuracy_score(y_test_clean[long_mask], y_pred[long_mask]) if long_mask.sum() > 0 else 0
        short_accuracy = accuracy_score(y_test_clean[short_mask], y_pred[short_mask]) if short_mask.sum() > 0 else 0
        hold_accuracy = accuracy_score(y_test_clean[hold_mask], y_pred[hold_mask]) if hold_mask.sum() > 0 else 0

        return {
            'overall_accuracy': overall_accuracy,
            'long_accuracy': long_accuracy,
            'short_accuracy': short_accuracy,
            'hold_accuracy': hold_accuracy,
            'total_samples': len(y_test_clean),
            'long_samples': long_mask.sum(),
            'short_samples': short_mask.sum(),
            'hold_samples': hold_mask.sum(),
            'meets_target': overall_accuracy >= 0.55  # 55% target
        }

    def _validate_calibration(self) -> Dict[str, Any]:
        """Validate probability calibration."""

        if 'signal_probability' not in self.models:
            return {'error': 'Signal probability model not found'}

        # Get test predictions
        X_test = self.test_data[self.feature_columns]
        y_test = self.test_data['signal_probability'].dropna()

        if len(y_test) == 0:
            return {'error': 'No valid test targets'}

        test_mask = ~self.test_data['signal_probability'].isnull()
        X_test_clean = X_test[test_mask]
        y_test_clean = y_test

        y_pred = self.models['signal_probability'].predict(X_test_clean)

        # Calculate calibration metrics
        mse = mean_squared_error(y_test_clean, y_pred)
        r2 = r2_score(y_test_clean, y_pred)

        # Check if predictions are in valid range [0.5, 1.0]
        valid_range = ((y_pred >= 0.5) & (y_pred <= 1.0)).all()

        return {
            'mse': mse,
            'rmse': np.sqrt(mse),
            'r2': r2,
            'valid_range': valid_range,
            'min_prediction': y_pred.min(),
            'max_prediction': y_pred.max(),
            'calibration_error': mse,  # Using MSE as calibration error
            'meets_target': mse < 0.01  # 1% MSE target
        }

    def _validate_temporal_stability(self) -> Dict[str, Any]:
        """Validate temporal stability using walk-forward validation."""

        # Use TimeSeriesSplit for temporal validation
        tscv = TimeSeriesSplit(n_splits=self.config['cv_folds'])

        stability_results = {}

        for model_name, model in self.models.items():
            model_type = self.config['model_types'][model_name]

            X = self.train_data[self.feature_columns]
            y = self.train_data[model_name].dropna()

            if len(y) == 0:
                continue

            # Get clean data
            mask = ~self.train_data[model_name].isnull()
            X_clean = X[mask]
            y_clean = y

            fold_scores = []

            for fold, (train_idx, val_idx) in enumerate(tscv.split(X_clean)):
                X_fold_train, X_fold_val = X_clean.iloc[train_idx], X_clean.iloc[val_idx]
                y_fold_train, y_fold_val = y_clean.iloc[train_idx], y_clean.iloc[val_idx]

                try:
                    # Create and train fold model
                    if model_type == 'classifier':
                        fold_model = LGBMClassifier(**self.model_metadata[model_name]['hyperparameters'])
                        fold_model.fit(X_fold_train, y_fold_train)
                        y_pred = fold_model.predict(X_fold_val)
                        score = accuracy_score(y_fold_val, y_pred)
                    else:
                        fold_model = LGBMRegressor(**self.model_metadata[model_name]['hyperparameters'])
                        fold_model.fit(X_fold_train, y_fold_train)
                        y_pred = fold_model.predict(X_fold_val)
                        score = r2_score(y_fold_val, y_pred)

                    fold_scores.append(score)

                except Exception as e:
                    logger.warning(f"Fold {fold} failed for {model_name}: {e}")
                    continue

            if fold_scores:
                stability_results[model_name] = {
                    'mean_score': np.mean(fold_scores),
                    'std_score': np.std(fold_scores),
                    'min_score': np.min(fold_scores),
                    'max_score': np.max(fold_scores),
                    'cv_scores': fold_scores,
                    'stability_coefficient': np.std(fold_scores) / np.mean(fold_scores) if np.mean(fold_scores) != 0 else float('inf')
                }

        return stability_results

    def _validate_risk_reward(self) -> Dict[str, Any]:
        """Validate risk-reward ratios from model predictions."""

        # Get test predictions for all price models
        X_test = self.test_data[self.feature_columns]

        price_predictions = {}
        for price_model in ['optimal_entry_long', 'optimal_entry_short', 'tp1_long', 'tp2_long',
                           'tp1_short', 'tp2_short', 'sl_long', 'sl_short']:
            if price_model in self.models:
                price_predictions[price_model] = self.models[price_model].predict(X_test)

        if len(price_predictions) < 8:
            return {'error': 'Not all price models available'}

        # Calculate risk-reward ratios
        rr_results = {}

        # Long positions
        if all(key in price_predictions for key in ['optimal_entry_long', 'tp1_long', 'sl_long']):
            long_risk = price_predictions['optimal_entry_long'] - price_predictions['sl_long']
            long_reward = price_predictions['tp1_long'] - price_predictions['optimal_entry_long']

            # Filter out invalid ratios
            valid_mask = (long_risk > 0) & (long_reward > 0)
            if valid_mask.sum() > 0:
                long_rr = long_reward[valid_mask] / long_risk[valid_mask]

                rr_results['long'] = {
                    'mean_rr': long_rr.mean(),
                    'median_rr': np.median(long_rr),
                    'min_rr': long_rr.min(),
                    'max_rr': long_rr.max(),
                    'above_1_5': (long_rr >= 1.5).sum(),
                    'above_2_0': (long_rr >= 2.0).sum(),
                    'total_valid': len(long_rr),
                    'pct_above_1_5': (long_rr >= 1.5).sum() / len(long_rr) * 100
                }

        # Short positions
        if all(key in price_predictions for key in ['optimal_entry_short', 'tp1_short', 'sl_short']):
            short_risk = price_predictions['sl_short'] - price_predictions['optimal_entry_short']
            short_reward = price_predictions['optimal_entry_short'] - price_predictions['tp1_short']

            # Filter out invalid ratios
            valid_mask = (short_risk > 0) & (short_reward > 0)
            if valid_mask.sum() > 0:
                short_rr = short_reward[valid_mask] / short_risk[valid_mask]

                rr_results['short'] = {
                    'mean_rr': short_rr.mean(),
                    'median_rr': np.median(short_rr),
                    'min_rr': short_rr.min(),
                    'max_rr': short_rr.max(),
                    'above_1_5': (short_rr >= 1.5).sum(),
                    'above_2_0': (short_rr >= 2.0).sum(),
                    'total_valid': len(short_rr),
                    'pct_above_1_5': (short_rr >= 1.5).sum() / len(short_rr) * 100
                }

        # Overall assessment
        overall_meets_target = True
        if 'long' in rr_results:
            overall_meets_target &= rr_results['long']['mean_rr'] >= 1.5
        if 'short' in rr_results:
            overall_meets_target &= rr_results['short']['mean_rr'] >= 1.5

        rr_results['meets_target'] = overall_meets_target

        return rr_results

    def _test_inference_speed(self) -> Dict[str, Any]:
        """Test model inference speed for live trading readiness."""

        # Create sample data for speed testing
        sample_size = 1000
        X_sample = self.test_data[self.feature_columns].sample(n=min(sample_size, len(self.test_data)), random_state=42)

        speed_results = {}

        for model_name, model in self.models.items():

            # Test single prediction speed
            single_times = []
            for _ in range(10):
                start_time = time.time()
                _ = model.predict(X_sample.iloc[:1])
                single_times.append((time.time() - start_time) * 1000)  # Convert to ms

            # Test batch prediction speed
            batch_times = []
            for _ in range(5):
                start_time = time.time()
                _ = model.predict(X_sample)
                batch_times.append((time.time() - start_time) * 1000)  # Convert to ms

            speed_results[model_name] = {
                'single_prediction_ms': {
                    'mean': np.mean(single_times),
                    'std': np.std(single_times),
                    'min': np.min(single_times),
                    'max': np.max(single_times)
                },
                'batch_prediction_ms': {
                    'mean': np.mean(batch_times),
                    'std': np.std(batch_times),
                    'min': np.min(batch_times),
                    'max': np.max(batch_times)
                }
            }

        # Test complete ensemble prediction speed
        ensemble_times = []
        for _ in range(10):
            start_time = time.time()

            # Simulate complete hierarchical prediction
            sample_row = X_sample.iloc[:1]
            predictions = {}

            for model_name, model in self.models.items():
                predictions[model_name] = model.predict(sample_row)[0]

            ensemble_times.append((time.time() - start_time) * 1000)

        speed_results['ensemble_complete'] = {
            'mean_ms': np.mean(ensemble_times),
            'std_ms': np.std(ensemble_times),
            'max_ms': np.max(ensemble_times),
            'meets_30s_target': np.max(ensemble_times) < 30000  # 30 seconds in ms
        }

        return speed_results

    def _calculate_overall_assessment(self, validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall assessment of the ensemble."""

        assessment = {
            'criteria_met': {},
            'overall_grade': 'F',
            'ready_for_live_trading': False,
            'recommendations': []
        }

        # Check each success criterion
        criteria = {
            'signal_accuracy_55pct': False,
            'no_overfitting': True,  # Assume true unless proven otherwise
            'calibration_error_10pct': False,
            'risk_reward_1_5': False,
            'inference_speed_30s': False,
            'price_logic_consistent': True  # Assume true for now
        }

        # Signal accuracy check
        if 'signal_accuracy' in validation_results and 'meets_target' in validation_results['signal_accuracy']:
            criteria['signal_accuracy_55pct'] = validation_results['signal_accuracy']['meets_target']

        # Calibration check
        if 'calibration_results' in validation_results and 'meets_target' in validation_results['calibration_results']:
            criteria['calibration_error_10pct'] = validation_results['calibration_results']['meets_target']

        # Risk-reward check
        if 'risk_reward_validation' in validation_results and 'meets_target' in validation_results['risk_reward_validation']:
            criteria['risk_reward_1_5'] = validation_results['risk_reward_validation']['meets_target']

        # Inference speed check
        if 'inference_speed' in validation_results and 'ensemble_complete' in validation_results['inference_speed']:
            criteria['inference_speed_30s'] = validation_results['inference_speed']['ensemble_complete']['meets_30s_target']

        # Overfitting check (temporal stability)
        if 'temporal_stability' in validation_results:
            stability_scores = []
            for model_name, stability in validation_results['temporal_stability'].items():
                if 'stability_coefficient' in stability:
                    stability_scores.append(stability['stability_coefficient'])

            if stability_scores:
                avg_stability = np.mean(stability_scores)
                criteria['no_overfitting'] = avg_stability < 0.2  # Low coefficient indicates stability

        assessment['criteria_met'] = criteria

        # Calculate grade
        criteria_passed = sum(criteria.values())
        total_criteria = len(criteria)

        if criteria_passed == total_criteria:
            assessment['overall_grade'] = 'A+'
            assessment['ready_for_live_trading'] = True
        elif criteria_passed >= total_criteria * 0.9:
            assessment['overall_grade'] = 'A'
            assessment['ready_for_live_trading'] = True
        elif criteria_passed >= total_criteria * 0.8:
            assessment['overall_grade'] = 'B+'
            assessment['ready_for_live_trading'] = True
        elif criteria_passed >= total_criteria * 0.7:
            assessment['overall_grade'] = 'B'
            assessment['ready_for_live_trading'] = False
        else:
            assessment['overall_grade'] = 'C'
            assessment['ready_for_live_trading'] = False

        # Generate recommendations
        if not criteria['signal_accuracy_55pct']:
            assessment['recommendations'].append("Improve signal accuracy - consider feature engineering or different algorithms")

        if not criteria['calibration_error_10pct']:
            assessment['recommendations'].append("Improve probability calibration - consider calibration techniques")

        if not criteria['risk_reward_1_5']:
            assessment['recommendations'].append("Optimize TP/SL levels for better risk-reward ratios")

        if not criteria['inference_speed_30s']:
            assessment['recommendations'].append("Optimize model complexity for faster inference")

        if not criteria['no_overfitting']:
            assessment['recommendations'].append("Address overfitting - consider regularization or simpler models")

        return assessment

    def save_ensemble(self) -> Dict[str, str]:
        """Save the trained ensemble models and metadata."""

        logger.info("💾 SAVING TRAINED ENSEMBLE MODELS...")

        # Create models directory
        models_dir = Path("models/lightgbm_ensemble")
        models_dir.mkdir(parents=True, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_files = {}

        # Save individual models
        for model_name, model in self.models.items():
            model_path = models_dir / f"lightgbm_{model_name}_{timestamp}.pkl"
            joblib.dump(model, model_path)
            saved_files[model_name] = str(model_path)
            logger.info(f"✅ Saved {model_name} model: {model_path}")

        # Save ensemble metadata
        ensemble_metadata = {
            'timestamp': timestamp,
            'models': list(self.models.keys()),
            'feature_columns': self.feature_columns,
            'label_columns': self.label_columns,
            'model_metadata': self.model_metadata,
            'feature_importance': self.feature_importance,
            'validation_results': self.validation_results,
            'config': self.config,
            'saved_files': saved_files
        }

        metadata_path = models_dir / f"ensemble_metadata_{timestamp}.json"
        with open(metadata_path, 'w') as f:
            json.dump(ensemble_metadata, f, indent=2, default=str)

        saved_files['metadata'] = str(metadata_path)

        logger.info(f"✅ Saved ensemble metadata: {metadata_path}")
        logger.info(f"📁 Total files saved: {len(saved_files)}")

        return saved_files

    def create_hierarchical_wrapper(self) -> 'HierarchicalLightGBMWrapper':
        """Create hierarchical model wrapper for live trading integration."""

        return HierarchicalLightGBMWrapper(
            models=self.models,
            feature_columns=self.feature_columns,
            model_metadata=self.model_metadata
        )


class HierarchicalLightGBMWrapper:
    """
    Hierarchical wrapper for LightGBM ensemble that outputs predictions
    in the format expected by the live trading system.
    """

    def __init__(self, models: Dict[str, Any], feature_columns: List[str],
                 model_metadata: Dict[str, Any]):
        """Initialize the hierarchical wrapper."""

        self.models = models
        self.feature_columns = feature_columns
        self.model_metadata = model_metadata

        logger.info(f"Hierarchical LightGBM Wrapper initialized with {len(models)} models")

    def predict(self, features: pd.DataFrame) -> Dict[str, Any]:
        """
        Make hierarchical predictions in the format expected by live trading system.

        Args:
            features: DataFrame with feature columns

        Returns:
            Dictionary with predictions in live trading format
        """

        start_time = time.time()

        # Ensure features are in correct format
        if not isinstance(features, pd.DataFrame):
            features = pd.DataFrame(features, columns=self.feature_columns)

        # Select only the features used in training
        feature_data = features[self.feature_columns]

        # Get predictions from all models
        predictions = {}

        for model_name, model in self.models.items():
            try:
                pred = model.predict(feature_data)
                if len(pred) == 1:
                    predictions[model_name] = float(pred[0])
                else:
                    predictions[model_name] = pred.tolist()
            except Exception as e:
                logger.warning(f"Prediction failed for {model_name}: {e}")
                predictions[model_name] = 0.0

        # Convert to live trading format
        trading_prediction = self._convert_to_trading_format(predictions)

        # Add metadata
        trading_prediction.update({
            'model_confidence': self._calculate_model_confidence(predictions),
            'timestamp': datetime.now(),
            'inference_time_ms': (time.time() - start_time) * 1000
        })

        return trading_prediction

    def _convert_to_trading_format(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """Convert model predictions to live trading format."""

        # Extract signal direction (convert to int)
        signal_direction = int(predictions.get('signal_direction', 0))

        # Extract signal probability (ensure in valid range)
        signal_probability = float(predictions.get('signal_probability', 0.5))
        signal_probability = max(0.5, min(1.0, signal_probability))

        # Extract price levels
        optimal_entry_long = float(predictions.get('optimal_entry_long', 0.0))
        optimal_entry_short = float(predictions.get('optimal_entry_short', 0.0))

        tp1_long = float(predictions.get('tp1_long', 0.0))
        tp2_long = float(predictions.get('tp2_long', 0.0))
        tp1_short = float(predictions.get('tp1_short', 0.0))
        tp2_short = float(predictions.get('tp2_short', 0.0))

        sl_long = float(predictions.get('sl_long', 0.0))
        sl_short = float(predictions.get('sl_short', 0.0))

        return {
            'signal_direction': signal_direction,
            'signal_probability': signal_probability,
            'optimal_entry_long': optimal_entry_long,
            'optimal_entry_short': optimal_entry_short,
            'tp1_long': tp1_long,
            'tp2_long': tp2_long,
            'tp1_short': tp1_short,
            'tp2_short': tp2_short,
            'sl_long': sl_long,
            'sl_short': sl_short
        }

    def _calculate_model_confidence(self, predictions: Dict[str, Any]) -> float:
        """Calculate overall model confidence."""

        # Use signal probability as base confidence
        base_confidence = predictions.get('signal_probability', 0.5)

        # Adjust based on signal strength
        signal_direction = predictions.get('signal_direction', 0)
        if signal_direction == 0:  # Hold signal
            return base_confidence * 0.8  # Lower confidence for hold
        else:
            return base_confidence

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded models."""

        return {
            'model_count': len(self.models),
            'model_names': list(self.models.keys()),
            'feature_count': len(self.feature_columns),
            'metadata': self.model_metadata
        }


def main():
    """Main training execution function."""

    logger.info("🎯 LIGHTGBM ENSEMBLE TRAINING FOR XAUUSD HIERARCHICAL SYSTEM")
    logger.info("=" * 80)

    try:
        # Initialize trainer
        trainer = LightGBMEnsembleTrainer()

        # Train ensemble
        if not trainer.train_ensemble():
            logger.error("❌ Ensemble training failed")
            return False

        # Validate ensemble
        validation_results = trainer.validate_ensemble()
        trainer.validation_results = validation_results

        # Save ensemble
        saved_files = trainer.save_ensemble()

        # Create hierarchical wrapper
        wrapper = trainer.create_hierarchical_wrapper()

        # Print final summary
        logger.info("=" * 80)
        logger.info("🎉 LIGHTGBM ENSEMBLE TRAINING COMPLETED")
        logger.info("=" * 80)

        logger.info(f"📊 TRAINING SUMMARY:")
        logger.info(f"   Models trained: {len(trainer.models)}")
        logger.info(f"   Features used: {len(trainer.feature_columns)}")
        logger.info(f"   Files saved: {len(saved_files)}")

        if 'overall_assessment' in validation_results:
            assessment = validation_results['overall_assessment']
            logger.info(f"📈 VALIDATION RESULTS:")
            logger.info(f"   Overall grade: {assessment.get('overall_grade', 'N/A')}")
            logger.info(f"   Ready for live trading: {assessment.get('ready_for_live_trading', False)}")

            if 'criteria_met' in assessment:
                criteria = assessment['criteria_met']
                logger.info(f"   Signal accuracy ≥55%: {criteria.get('signal_accuracy_55pct', False)}")
                logger.info(f"   No overfitting: {criteria.get('no_overfitting', False)}")
                logger.info(f"   Calibration error <10%: {criteria.get('calibration_error_10pct', False)}")
                logger.info(f"   Risk-reward ≥1.5:1: {criteria.get('risk_reward_1_5', False)}")
                logger.info(f"   Inference speed <30s: {criteria.get('inference_speed_30s', False)}")

        logger.info(f"🚀 NEXT STEPS:")
        logger.info(f"   1. Review validation results and recommendations")
        logger.info(f"   2. Test hierarchical wrapper with live data")
        logger.info(f"   3. Integrate with MT5 live trading system")
        logger.info(f"   4. Begin paper trading validation")

        return True

    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
