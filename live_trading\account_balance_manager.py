"""
Account Balance Manager

Centralized manager for retrieving and managing real-time account balance
from MT5, with fallback mechanisms and comprehensive logging.
"""

import MetaTrader5 as mt5
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import time
from dataclasses import dataclass

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))
from data_collection.mt5_collector.mt5_client import MT5Client
from data_collection.error_handling.logger import LoggerMixin
from data_collection.error_handling.exceptions import MT5DataError


@dataclass
class AccountInfo:
    """Account information structure."""
    balance: float
    equity: float
    margin_used: float
    margin_free: float
    margin_level: float
    profit: float
    currency: str
    leverage: int
    server: str
    login: int
    timestamp: datetime
    source: str  # 'mt5', 'fallback', 'cached'


class AccountBalanceManager(LoggerMixin):
    """
    Centralized account balance manager with real-time MT5 integration.
    
    Features:
    - Real-time balance retrieval from MT5
    - Intelligent caching to reduce API calls
    - Fallback mechanisms when MT5 unavailable
    - Comprehensive logging and error handling
    - Thread-safe operations
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize Account Balance Manager.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        
        # MT5 client
        self.mt5_client = MT5Client()
        self.is_connected = False
        
        # Cache settings
        self.cache_duration_seconds = config.get('balance_cache_duration', 30)  # 30 seconds default
        self.last_update_time = None
        self.cached_account_info = None
        
        # Fallback settings
        self.fallback_balance = config.get('fallback_balance', 470.0)  # User's real balance
        self.use_fallback = config.get('use_fallback_when_disconnected', True)
        
        # Connection retry settings
        self.max_retry_attempts = config.get('balance_retry_attempts', 3)
        self.retry_delay_seconds = config.get('balance_retry_delay', 1.0)
        
        # Statistics
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'fallback_uses': 0,
            'connection_errors': 0,
            'last_successful_update': None
        }
        
        self.logger.info("AccountBalanceManager initialized")
    
    def connect(self) -> bool:
        """Connect to MT5 for account info retrieval."""
        try:
            if self.mt5_client.connect():
                self.is_connected = True
                self.logger.info("AccountBalanceManager connected to MT5")
                
                # Get initial account info to validate connection
                initial_info = self._fetch_mt5_account_info()
                if initial_info:
                    self.logger.info(f"Initial account balance: ${initial_info.balance:,.2f}")
                    return True
                else:
                    self.logger.warning("Connected to MT5 but failed to get account info")
                    return False
            
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to connect AccountBalanceManager to MT5: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5."""
        if self.is_connected:
            self.mt5_client.disconnect()
            self.is_connected = False
            self.logger.info("AccountBalanceManager disconnected from MT5")
    
    def get_account_info(self, force_refresh: bool = False) -> AccountInfo:
        """
        Get current account information with intelligent caching.
        
        Args:
            force_refresh: Force refresh from MT5, bypass cache
            
        Returns:
            AccountInfo object with current account data
        """
        self.stats['total_requests'] += 1
        
        # Check cache first (unless force refresh)
        if not force_refresh and self._is_cache_valid():
            self.stats['cache_hits'] += 1
            self.logger.debug("Using cached account info")
            return self.cached_account_info
        
        # Try to get fresh data from MT5
        account_info = self._get_fresh_account_info()
        
        if account_info:
            self.cached_account_info = account_info
            self.last_update_time = datetime.now()
            self.stats['successful_requests'] += 1
            self.stats['last_successful_update'] = datetime.now()
            
            self.logger.debug(f"Fresh account info retrieved: ${account_info.balance:,.2f}")
            return account_info
        
        # Fallback mechanisms
        return self._get_fallback_account_info()
    
    def get_balance(self, force_refresh: bool = False) -> float:
        """
        Get current account balance.
        
        Args:
            force_refresh: Force refresh from MT5
            
        Returns:
            Current account balance
        """
        account_info = self.get_account_info(force_refresh)
        return account_info.balance
    
    def get_equity(self, force_refresh: bool = False) -> float:
        """
        Get current account equity.
        
        Args:
            force_refresh: Force refresh from MT5
            
        Returns:
            Current account equity
        """
        account_info = self.get_account_info(force_refresh)
        return account_info.equity
    
    def get_free_margin(self, force_refresh: bool = False) -> float:
        """
        Get current free margin.
        
        Args:
            force_refresh: Force refresh from MT5
            
        Returns:
            Current free margin
        """
        account_info = self.get_account_info(force_refresh)
        return account_info.margin_free
    
    def _is_cache_valid(self) -> bool:
        """Check if cached data is still valid."""
        if not self.cached_account_info or not self.last_update_time:
            return False
        
        time_since_update = (datetime.now() - self.last_update_time).total_seconds()
        return time_since_update < self.cache_duration_seconds
    
    def _get_fresh_account_info(self) -> Optional[AccountInfo]:
        """Get fresh account info from MT5 with retry logic."""
        for attempt in range(self.max_retry_attempts):
            try:
                # Ensure connection
                if not self.is_connected:
                    if not self.connect():
                        continue
                
                # Fetch account info
                account_info = self._fetch_mt5_account_info()
                if account_info:
                    return account_info
                
            except Exception as e:
                self.logger.warning(f"Attempt {attempt + 1} failed to get account info: {str(e)}")
                self.stats['connection_errors'] += 1
                
                if attempt < self.max_retry_attempts - 1:
                    time.sleep(self.retry_delay_seconds)
        
        return None
    
    def _fetch_mt5_account_info(self) -> Optional[AccountInfo]:
        """Fetch account info directly from MT5."""
        try:
            # Use direct MT5 module for complete account information
            if not mt5.initialize():
                self.logger.error("Failed to initialize MT5 for account info")
                return None

            account_info = mt5.account_info()
            if not account_info:
                self.logger.error(f"Failed to get MT5 account info: {mt5.last_error()}")
                return None

            return AccountInfo(
                balance=float(account_info.balance),
                equity=float(account_info.equity),
                margin_used=float(account_info.margin),
                margin_free=float(account_info.margin_free),
                margin_level=float(account_info.margin_level) if account_info.margin_level else 0.0,
                profit=float(account_info.profit),
                currency=str(account_info.currency),
                leverage=int(account_info.leverage),
                server=str(account_info.server),
                login=int(account_info.login),
                timestamp=datetime.now(),
                source='mt5'
            )

        except Exception as e:
            self.logger.error(f"Error fetching MT5 account info: {str(e)}")
            return None
    
    def _get_fallback_account_info(self) -> AccountInfo:
        """Get fallback account info when MT5 unavailable."""
        self.stats['fallback_uses'] += 1
        
        if self.use_fallback:
            self.logger.warning(f"Using fallback account balance: ${self.fallback_balance:,.2f}")
            
            return AccountInfo(
                balance=self.fallback_balance,
                equity=self.fallback_balance,  # Assume no floating P&L
                margin_used=0.0,
                margin_free=self.fallback_balance,
                margin_level=0.0,
                profit=0.0,
                currency='USD',
                leverage=100,  # Typical forex leverage
                server='Fallback',
                login=0,
                timestamp=datetime.now(),
                source='fallback'
            )
        else:
            # Use cached data if available, even if stale
            if self.cached_account_info:
                self.logger.warning("Using stale cached account info")
                stale_info = self.cached_account_info
                stale_info.source = 'cached'
                stale_info.timestamp = datetime.now()
                return stale_info
            
            # Last resort - return minimal fallback
            self.logger.error("No account info available, using minimal fallback")
            return AccountInfo(
                balance=100.0,  # Minimal fallback
                equity=100.0,
                margin_used=0.0,
                margin_free=100.0,
                margin_level=0.0,
                profit=0.0,
                currency='USD',
                leverage=100,
                server='Emergency',
                login=0,
                timestamp=datetime.now(),
                source='emergency'
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """Get manager statistics."""
        return {
            **self.stats,
            'is_connected': self.is_connected,
            'cache_valid': self._is_cache_valid(),
            'cache_age_seconds': (datetime.now() - self.last_update_time).total_seconds() 
                               if self.last_update_time else None,
            'success_rate': self.stats['successful_requests'] / max(self.stats['total_requests'], 1),
            'fallback_rate': self.stats['fallback_uses'] / max(self.stats['total_requests'], 1)
        }
    
    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.disconnect()
