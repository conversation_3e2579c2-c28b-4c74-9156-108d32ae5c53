{% extends "base.html" %}

{% block title %}System Health - {{ super() }}{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-nasa-light mb-2">
                <i class="fas fa-server text-nasa-accent mr-3"></i>
                System Health
            </h1>
            <p class="text-nasa-gray">Real-time system monitoring and performance metrics</p>
        </div>
        <div class="flex items-center mt-4 sm:mt-0 space-x-4">
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-nasa-accent rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-nasa-light" id="system-status">All Systems Operational</span>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-nasa-accent/20 text-nasa-accent">
                <i class="fas fa-shield-check mr-2"></i>
                Secure
            </span>
        </div>
    </div>
</div>

<!-- System Status Overview -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- MT5 Connection -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-plug text-nasa-accent text-xl" id="mt5-icon"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">MT5 Connection</h3>
                    <p class="text-2xl font-bold text-nasa-accent" id="mt5-status">Connected</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                    Online
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-server mr-2"></i>
            Server: <span id="mt5-server">MetaQuotes-Demo</span>
        </div>
    </div>

    <!-- Model Engine -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-brain text-blue-400 text-xl" id="model-icon"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Model Engine</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="model-status">Active</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                    Running
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-cogs mr-2"></i>
            Models: <span id="model-count">4</span> loaded
        </div>
    </div>

    <!-- Data Feed -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-stream text-yellow-500 text-xl" id="data-icon"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Data Feed</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="data-status">Streaming</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-500">
                    Live
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-tachometer-alt mr-2"></i>
            Latency: <span id="data-latency">12ms</span>
        </div>
    </div>

    <!-- System Uptime -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-clock text-purple-400 text-xl" id="uptime-icon"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">System Uptime</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="system-uptime">2d 14h 32m</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                    Stable
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-calendar mr-2"></i>
            Started: <span id="start-time">Dec 19, 09:15</span>
        </div>
    </div>
</div>

<!-- Resource Usage Monitoring -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- CPU Usage -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-microchip text-nasa-accent mr-2"></i>
                CPU Usage
            </h3>
            <span class="text-2xl font-bold text-nasa-light" id="cpu-percentage">24.5%</span>
        </div>

        <!-- CPU Progress Bar -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-nasa-gray">Current Load</span>
                <span class="text-sm font-medium text-nasa-light">Normal</span>
            </div>
            <div class="w-full bg-nasa-gray/20 rounded-full h-3">
                <div class="bg-gradient-to-r from-blue-500 to-blue-400 h-3 rounded-full transition-all duration-300"
                     id="cpu-progress" style="width: 24.5%"></div>
            </div>
        </div>

        <!-- CPU Load Averages -->
        <div class="grid grid-cols-3 gap-4">
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">1min</div>
                <div class="text-lg font-semibold text-nasa-light" id="cpu-1min">22.1%</div>
            </div>
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">5min</div>
                <div class="text-lg font-semibold text-nasa-light" id="cpu-5min">25.8%</div>
            </div>
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">15min</div>
                <div class="text-lg font-semibold text-nasa-light" id="cpu-15min">28.2%</div>
            </div>
        </div>
    </div>

    <!-- Memory Usage -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-memory text-nasa-accent mr-2"></i>
                Memory Usage
            </h3>
            <span class="text-2xl font-bold text-nasa-light" id="memory-percentage">68.2%</span>
        </div>

        <!-- Memory Progress Bar -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-nasa-gray">RAM Usage</span>
                <span class="text-sm font-medium text-yellow-500">Moderate</span>
            </div>
            <div class="w-full bg-nasa-gray/20 rounded-full h-3">
                <div class="bg-gradient-to-r from-yellow-500 to-yellow-400 h-3 rounded-full transition-all duration-300"
                     id="memory-progress" style="width: 68.2%"></div>
            </div>
        </div>

        <!-- Memory Details -->
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">Used</div>
                <div class="text-lg font-semibold text-nasa-light" id="memory-used">5.46 GB</div>
            </div>
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">Available</div>
                <div class="text-lg font-semibold text-nasa-light" id="memory-available">2.54 GB</div>
            </div>
        </div>
    </div>

    <!-- Disk Usage -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-hdd text-nasa-accent mr-2"></i>
                Disk Usage
            </h3>
            <span class="text-2xl font-bold text-nasa-light" id="disk-percentage">45.8%</span>
        </div>

        <!-- Disk Progress Bar -->
        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm text-nasa-gray">Storage Usage</span>
                <span class="text-sm font-medium text-nasa-accent">Healthy</span>
            </div>
            <div class="w-full bg-nasa-gray/20 rounded-full h-3">
                <div class="bg-gradient-to-r from-nasa-accent to-green-400 h-3 rounded-full transition-all duration-300"
                     id="disk-progress" style="width: 45.8%"></div>
            </div>
        </div>

        <!-- Disk Details -->
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">Used</div>
                <div class="text-lg font-semibold text-nasa-light" id="disk-used">458 GB</div>
            </div>
            <div class="text-center">
                <div class="text-sm text-nasa-gray mb-1">Free</div>
                <div class="text-lg font-semibold text-nasa-light" id="disk-free">542 GB</div>
            </div>
        </div>
    </div>
</div>

<!-- Service Status and Network Monitoring -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Service Status -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-cogs text-nasa-accent mr-2"></i>
                Service Status
            </h3>
            <span class="text-sm text-nasa-accent">5 Services Running</span>
        </div>

        <div class="space-y-4">
            <!-- MT5 Terminal -->
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-nasa-accent rounded-full mr-3 animate-pulse"></div>
                    <span class="text-nasa-light font-medium">MT5 Terminal</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                        Running
                    </span>
                    <span class="text-sm text-nasa-gray">99.9%</span>
                </div>
            </div>

            <!-- Model Engine -->
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-nasa-accent rounded-full mr-3 animate-pulse"></div>
                    <span class="text-nasa-light font-medium">Model Engine</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                        Active
                    </span>
                    <span class="text-sm text-nasa-gray">99.8%</span>
                </div>
            </div>

            <!-- Data Collector -->
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-nasa-accent rounded-full mr-3 animate-pulse"></div>
                    <span class="text-nasa-light font-medium">Data Collector</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                        Streaming
                    </span>
                    <span class="text-sm text-nasa-gray">99.9%</span>
                </div>
            </div>

            <!-- WebSocket Server -->
            <div class="flex items-center justify-between py-3 border-b border-nasa-gray/10">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-nasa-accent rounded-full mr-3 animate-pulse"></div>
                    <span class="text-nasa-light font-medium">WebSocket Server</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                        Connected
                    </span>
                    <span class="text-sm text-nasa-gray">100%</span>
                </div>
            </div>

            <!-- Risk Manager -->
            <div class="flex items-center justify-between py-3">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3 animate-pulse"></div>
                    <span class="text-nasa-light font-medium">Risk Manager</span>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-500">
                        Monitoring
                    </span>
                    <span class="text-sm text-nasa-gray">99.5%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Network Status -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-network-wired text-nasa-accent mr-2"></i>
                Network Status
            </h3>
            <span class="text-sm text-nasa-accent">Real-time</span>
        </div>

        <!-- Network Stats -->
        <div class="grid grid-cols-2 gap-6 mb-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-400 mb-1" id="network-in">1.2 MB/s</div>
                <div class="text-sm text-nasa-gray">Incoming</div>
                <div class="flex items-center justify-center mt-2">
                    <i class="fas fa-arrow-down text-blue-400 mr-1"></i>
                    <span class="text-xs text-nasa-gray">Download</span>
                </div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-500 mb-1" id="network-out">0.8 MB/s</div>
                <div class="text-sm text-nasa-gray">Outgoing</div>
                <div class="flex items-center justify-center mt-2">
                    <i class="fas fa-arrow-up text-yellow-500 mr-1"></i>
                    <span class="text-xs text-nasa-gray">Upload</span>
                </div>
            </div>
        </div>

        <!-- Network Chart -->
        <div class="h-40">
            <canvas id="network-chart"></canvas>
        </div>
    </div>
</div>

<!-- System Logs and Model Version Info -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- System Logs -->
    <div class="lg:col-span-2 trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-terminal text-nasa-accent mr-2"></i>
                System Logs
            </h3>
            <div class="flex space-x-2">
                <button onclick="refreshLogs()" class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-accent/20 text-nasa-accent hover:bg-nasa-accent/30 transition-colors">
                    <i class="fas fa-sync-alt mr-1"></i> Refresh
                </button>
                <button onclick="clearLogs()" class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-red/20 text-nasa-red hover:bg-nasa-red/30 transition-colors">
                    <i class="fas fa-trash mr-1"></i> Clear
                </button>
            </div>
        </div>

        <div class="bg-nasa-dark/50 rounded-lg p-4 h-80 overflow-y-auto font-mono text-sm" id="system-logs">
            <div class="log-entry text-nasa-accent mb-2">[2024-12-21 00:50:18] <span class="text-nasa-accent">INFO:</span> Dashboard application initialized successfully</div>
            <div class="log-entry text-blue-400 mb-2">[2024-12-21 00:50:18] <span class="text-blue-400">INFO:</span> WebSocket handler initialized</div>
            <div class="log-entry text-nasa-accent mb-2">[2024-12-21 00:50:21] <span class="text-nasa-accent">INFO:</span> WebSocket connected. Total connections: 1</div>
            <div class="log-entry text-yellow-500 mb-2">[2024-12-21 00:50:25] <span class="text-yellow-500">WARN:</span> MT5 provider not available - real data integration needed</div>
            <div class="log-entry text-blue-400 mb-2">[2024-12-21 00:50:30] <span class="text-blue-400">INFO:</span> Model engine predictions updated</div>
            <div class="log-entry text-nasa-accent mb-2">[2024-12-21 00:50:35] <span class="text-nasa-accent">INFO:</span> System health check completed</div>
            <div class="log-entry text-purple-400 mb-2">[2024-12-21 00:50:40] <span class="text-purple-400">DEBUG:</span> Processing market data feed</div>
            <div class="log-entry text-nasa-accent mb-2">[2024-12-21 00:50:45] <span class="text-nasa-accent">INFO:</span> Risk management rules applied</div>
            <div class="log-entry text-blue-400 mb-2">[2024-12-21 00:50:50] <span class="text-blue-400">INFO:</span> Trade signal generated: BUY XAUUSD</div>
            <div class="log-entry text-nasa-accent mb-2">[2024-12-21 00:50:55] <span class="text-nasa-accent">INFO:</span> Position opened successfully</div>
        </div>
    </div>

    <!-- Model Version Info -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-code-branch text-nasa-accent mr-2"></i>
                Model Versions
            </h3>
        </div>

        <div class="space-y-4">
            <div class="border border-nasa-gray/20 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium text-nasa-light">LightGBM</span>
                    <span class="text-xs px-2 py-1 bg-nasa-accent/20 text-nasa-accent rounded">v3.3.5</span>
                </div>
                <div class="text-sm text-nasa-gray">Last Updated: Dec 19, 2024</div>
                <div class="text-xs text-nasa-gray mt-1">Accuracy: 89.2%</div>
            </div>

            <div class="border border-nasa-gray/20 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium text-nasa-light">CatBoost</span>
                    <span class="text-xs px-2 py-1 bg-blue-500/20 text-blue-400 rounded">v1.2.2</span>
                </div>
                <div class="text-sm text-nasa-gray">Last Updated: Dec 18, 2024</div>
                <div class="text-xs text-nasa-gray mt-1">Accuracy: 86.8%</div>
            </div>

            <div class="border border-nasa-gray/20 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium text-nasa-light">XGBoost</span>
                    <span class="text-xs px-2 py-1 bg-purple-500/20 text-purple-400 rounded">v1.7.4</span>
                </div>
                <div class="text-sm text-nasa-gray">Last Updated: Dec 17, 2024</div>
                <div class="text-xs text-nasa-gray mt-1">Accuracy: 84.5%</div>
            </div>

            <div class="border border-nasa-gray/20 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-medium text-nasa-light">Linear</span>
                    <span class="text-xs px-2 py-1 bg-yellow-500/20 text-yellow-500 rounded">v1.0.0</span>
                </div>
                <div class="text-sm text-nasa-gray">Last Updated: Dec 15, 2024</div>
                <div class="text-xs text-nasa-gray mt-1">Accuracy: 82.1%</div>
            </div>
        </div>
    </div>
</div>

<!-- Health Check Status -->
<div class="trading-card">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-nasa-light">
            <i class="fas fa-heartbeat text-nasa-accent mr-2"></i>
            Health Check Status
        </h3>
        <span class="text-sm text-nasa-gray">Last Check: <span id="last-health-check">2 minutes ago</span></span>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="flex items-center justify-between p-4 border border-nasa-gray/20 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-database text-nasa-accent mr-3"></i>
                <span class="text-nasa-light">Database</span>
            </div>
            <div class="flex items-center">
                <div class="w-2 h-2 bg-nasa-accent rounded-full mr-2"></div>
                <span class="text-sm text-nasa-accent">OK</span>
            </div>
        </div>

        <div class="flex items-center justify-between p-4 border border-nasa-gray/20 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-wifi text-nasa-accent mr-3"></i>
                <span class="text-nasa-light">Connectivity</span>
            </div>
            <div class="flex items-center">
                <div class="w-2 h-2 bg-nasa-accent rounded-full mr-2"></div>
                <span class="text-sm text-nasa-accent">OK</span>
            </div>
        </div>

        <div class="flex items-center justify-between p-4 border border-nasa-gray/20 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-shield-alt text-nasa-accent mr-3"></i>
                <span class="text-nasa-light">Security</span>
            </div>
            <div class="flex items-center">
                <div class="w-2 h-2 bg-nasa-accent rounded-full mr-2"></div>
                <span class="text-sm text-nasa-accent">OK</span>
            </div>
        </div>

        <div class="flex items-center justify-between p-4 border border-nasa-gray/20 rounded-lg">
            <div class="flex items-center">
                <i class="fas fa-chart-line text-nasa-accent mr-3"></i>
                <span class="text-nasa-light">Performance</span>
            </div>
            <div class="flex items-center">
                <div class="w-2 h-2 bg-nasa-accent rounded-full mr-2"></div>
                <span class="text-sm text-nasa-accent">OK</span>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_scripts %}
<script>
// Initialize system page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize page
    initializePage();

    // Start real-time updates
    startSystemMonitoring();

    // Set up WebSocket updates if available
    if (window.dashboardInstance && window.dashboardInstance.websocket) {
        console.log('System page initialized with real-time monitoring');
    }
});

function initializePage() {
    // Initialize network chart
    initializeNetworkChart();

    // Update health check timestamp
    updateHealthCheckTime();
}

function initializeNetworkChart() {
    // Destroy existing chart if it exists
    if (window.networkChart) {
        window.networkChart.destroy();
    }

    const networkCtx = document.getElementById('network-chart');
    if (networkCtx) {
        window.networkChart = new Chart(networkCtx, {
            type: 'line',
            data: {
                labels: ['10s', '8s', '6s', '4s', '2s', 'Now'],
                datasets: [{
                    label: 'Incoming',
                    data: [0.8, 1.1, 1.3, 1.0, 1.2, 1.2],
                    borderColor: '#3B82F6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }, {
                    label: 'Outgoing',
                    data: [0.5, 0.7, 0.9, 0.6, 0.8, 0.8],
                    borderColor: '#EAB308',
                    backgroundColor: 'rgba(234, 179, 8, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#F8FAFC' }
                    }
                },
                scales: {
                    x: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' }
                    },
                    y: {
                        ticks: { color: '#64748B' },
                        grid: { color: 'rgba(100, 116, 139, 0.1)' },
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

function startSystemMonitoring() {
    // Update system metrics every 5 seconds
    setInterval(updateSystemMetrics, 5000);

    // Update uptime every minute
    setInterval(updateUptime, 60000);
}

function updateSystemMetrics() {
    // Simulate real-time updates
    updateResourceUsage();
    updateNetworkStats();

    console.log('System metrics updated');
}

function updateResourceUsage() {
    // Simulate CPU usage fluctuation
    const cpuUsage = Math.random() * 20 + 20; // 20-40%
    document.getElementById('cpu-percentage').textContent = cpuUsage.toFixed(1) + '%';
    document.getElementById('cpu-progress').style.width = cpuUsage + '%';

    // Simulate memory usage
    const memUsage = Math.random() * 10 + 65; // 65-75%
    document.getElementById('memory-percentage').textContent = memUsage.toFixed(1) + '%';
    document.getElementById('memory-progress').style.width = memUsage + '%';
}

function updateNetworkStats() {
    // Simulate network traffic
    const incomingMB = (Math.random() * 0.5 + 0.8).toFixed(1);
    const outgoingMB = (Math.random() * 0.3 + 0.6).toFixed(1);

    document.getElementById('network-in').textContent = incomingMB + ' MB/s';
    document.getElementById('network-out').textContent = outgoingMB + ' MB/s';
}

function updateUptime() {
    // This would normally get real uptime from server
    const uptimeElement = document.getElementById('system-uptime');
    if (uptimeElement) {
        // Simulate uptime increment
        console.log('Uptime updated');
    }
}

function updateHealthCheckTime() {
    const healthCheckElement = document.getElementById('last-health-check');
    if (healthCheckElement) {
        healthCheckElement.textContent = 'Just now';

        // Update to relative time after a few seconds
        setTimeout(() => {
            healthCheckElement.textContent = '1 minute ago';
        }, 60000);
    }
}

function refreshLogs() {
    const logsContainer = document.getElementById('system-logs');
    if (logsContainer) {
        // Add a new log entry
        const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
        const newLog = `<div class="log-entry text-blue-400 mb-2">[${timestamp}] <span class="text-blue-400">INFO:</span> System logs refreshed</div>`;
        logsContainer.innerHTML = newLog + logsContainer.innerHTML;

        // Scroll to top
        logsContainer.scrollTop = 0;
    }
    console.log('System logs refreshed');
}

function clearLogs() {
    const logsContainer = document.getElementById('system-logs');
    if (logsContainer) {
        const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
        logsContainer.innerHTML = `<div class="log-entry text-nasa-accent mb-2">[${timestamp}] <span class="text-nasa-accent">INFO:</span> System logs cleared</div>`;
    }
    console.log('System logs cleared');
}
</script>
{% endblock %}
