#!/usr/bin/env python3
"""
Updated Dashboard Runner
Runs the comprehensive live trading dashboard with real data integration.
"""

import sys
import asyncio
import uvicorn
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from dashboard.factories import DashboardFactory, DashboardType
from dashboard.config import DashboardConfig
from data_collection.error_handling.logger import LoggerMixin


class UpdatedDashboardRunner(LoggerMixin):
    """Runner for the updated live trading dashboard."""
    
    def __init__(self):
        """Initialize the dashboard runner."""
        super().__init__()
        self.config = None
        self.dashboard_factory = None
        self.web_server = None
        
    def initialize(self):
        """Initialize the dashboard system."""
        try:
            self.logger.info("🚀 Initializing Updated Live Trading Dashboard...")
            
            # Create base configuration
            from data_collection.config import Config as BaseConfig
            base_config = BaseConfig()

            # Create dashboard configuration
            self.config = DashboardConfig(
                base_config=base_config,
                dashboard_title="XAUUSD AI Trading Dashboard - Live Data",
                theme="dark",
                host="0.0.0.0",
                port=8081,
                debug=False,
                auto_reload=False,
                update_interval_seconds=30
            )
            
            # Configure data sources for real data (update existing dict)
            self.config.data_sources.update({
                'live_trading': {
                    'enabled': True,
                    'symbol': 'XAUUSD!',
                    'update_interval': 30
                },
                'real_time_performance': {
                    'enabled': True,
                    'historical_days': 30
                },
                'system_health': {
                    'enabled': True,
                    'monitoring_interval': 60
                }
            })
            
            # Create dashboard factory
            self.dashboard_factory = DashboardFactory(self.config)
            
            self.logger.info("✅ Dashboard configuration initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize dashboard: {str(e)}")
            return False
    
    def create_dashboard(self):
        """Create the dashboard with real data providers."""
        try:
            self.logger.info("🔧 Creating dashboard components...")
            
            # Create data providers directly
            from dashboard.data_providers import LiveTradingDataProvider, RealTimePerformanceProvider

            live_trading_provider = LiveTradingDataProvider(self.config)
            real_time_provider = RealTimePerformanceProvider(self.config)
            
            # Initialize providers
            live_trading_provider.initialize()
            real_time_provider.initialize()
            
            # Create web server with data providers
            from dashboard.web_server import FastAPIServer

            self.web_server = FastAPIServer(
                self.config,
                data_providers={
                    'live_trading': live_trading_provider,
                    'real_time_performance': real_time_provider
                }
            )
            
            if not self.web_server.initialize():
                raise Exception("Failed to initialize web server")
            
            self.logger.info("✅ Dashboard components created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to create dashboard: {str(e)}")
            return False
    
    def run_dashboard(self):
        """Run the dashboard server."""
        try:
            self.logger.info("🌐 Starting dashboard web server...")
            
            # Get server configuration
            host = self.config.host
            port = self.config.port
            debug = self.config.debug
            
            self.logger.info(f"📡 Dashboard will be available at: http://{host}:{port}")
            self.logger.info("🔄 Real-time data updates every 30 seconds")
            self.logger.info("📊 Showing live trading system data")
            
            # Run the server
            uvicorn.run(
                self.web_server.app,
                host=host,
                port=port,
                log_level="info" if debug else "warning",
                access_log=debug
            )
            
        except KeyboardInterrupt:
            self.logger.info("🛑 Dashboard stopped by user")
        except Exception as e:
            self.logger.error(f"❌ Dashboard server error: {str(e)}")
    
    def test_data_connectivity(self):
        """Test connectivity to data sources."""
        try:
            self.logger.info("🔍 Testing data connectivity...")
            
            # Test live trading data
            from dashboard.data_providers import LiveTradingDataProvider
            live_provider = LiveTradingDataProvider(self.config)
            live_provider.initialize()
            
            live_status = live_provider.get_live_trading_status()
            self.logger.info(f"📊 Live Trading Status: {live_status.get('status', 'UNKNOWN')}")
            
            # Test model data through the live provider's data method
            live_data = live_provider.get_data()
            model_data = live_data.get('model_performance', {})
            models_available = len([m for m in model_data.get('models', {}).values() if m.get('available')])
            self.logger.info(f"🤖 Models Available: {models_available}/4")
            
            health_data = live_data.get('system_health', {})
            self.logger.info(f"💚 System Health: {health_data.get('overall_health', 'UNKNOWN')}")
            
            # Test real-time performance data
            from dashboard.data_providers import RealTimePerformanceProvider
            rt_provider = RealTimePerformanceProvider(self.config)
            rt_provider.initialize()
            
            rt_metrics = rt_provider.get_real_time_metrics()
            self.logger.info(f"⚡ Real-time Metrics: {rt_metrics.get('timestamp', 'No timestamp')}")
            
            self.logger.info("✅ Data connectivity test completed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Data connectivity test failed: {str(e)}")
            return False


def main():
    """Main entry point."""
    print("=" * 80)
    print("🚀 XAUUSD AI Trading Dashboard - Updated with Live Data")
    print("=" * 80)
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    runner = UpdatedDashboardRunner()
    
    # Initialize
    if not runner.initialize():
        print("❌ Failed to initialize dashboard")
        return 1
    
    # Test data connectivity
    if not runner.test_data_connectivity():
        print("⚠️  Data connectivity issues detected, but continuing...")
    
    # Create dashboard
    if not runner.create_dashboard():
        print("❌ Failed to create dashboard")
        return 1
    
    print()
    print("🎯 Dashboard Features:")
    print("   • Real-time live trading system data")
    print("   • Specialized ensemble model status")
    print("   • System health monitoring")
    print("   • Responsive design for all devices")
    print("   • 30-second auto-refresh")
    print()
    print("🌐 Access the dashboard at: http://localhost:8081")
    print("🔄 Press Ctrl+C to stop the server")
    print()
    
    # Run dashboard
    runner.run_dashboard()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
