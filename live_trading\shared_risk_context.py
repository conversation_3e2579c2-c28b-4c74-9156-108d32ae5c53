#!/usr/bin/env python3
"""
Shared Risk Context System

This module provides a communication channel between the hierarchical decision framework
and five-layer risk manager, enabling coordinated risk management while maintaining
component independence.

Key Features:
- Shared risk state for coordination
- Priority-based decision resolution
- Emergency override capabilities
- Performance optimization through caching
- Thread-safe operations for real-time trading
"""

import threading
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging


class RiskLevel(Enum):
    """Risk severity levels for prioritization."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5

    def __lt__(self, other):
        if self.__class__ is other.__class__:
            return self.value < other.value
        return NotImplemented

    def __le__(self, other):
        if self.__class__ is other.__class__:
            return self.value <= other.value
        return NotImplemented

    def __gt__(self, other):
        if self.__class__ is other.__class__:
            return self.value > other.value
        return NotImplemented

    def __ge__(self, other):
        if self.__class__ is other.__class__:
            return self.value >= other.value
        return NotImplemented


class RiskSource(Enum):
    """Source of risk assessment."""
    HIERARCHICAL_DECISION = "hierarchical_decision"
    FIVE_LAYER_RISK = "five_layer_risk"
    EXTERNAL_SIGNAL = "external_signal"
    SYSTEM_HEALTH = "system_health"


@dataclass
class RiskAssessment:
    """Individual risk assessment from a component."""
    source: RiskSource
    level: RiskLevel
    confidence: float  # 0.0 to 1.0
    reasoning: str
    timestamp: datetime
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """Check if this assessment has expired."""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at


@dataclass
class SharedRiskState:
    """Shared risk state between components."""
    # Market regime information
    market_regime: str = "UNKNOWN"
    market_regime_confidence: float = 0.5
    market_regime_numeric: float = 0.5
    
    # Session information
    current_session: str = "unknown"
    session_multiplier: float = 1.0
    
    # Volatility information
    volatility_regime: str = "normal"
    volatility_state: str = "NORMAL"
    
    # Risk assessments from different components
    risk_assessments: Dict[RiskSource, RiskAssessment] = field(default_factory=dict)
    
    # Emergency flags
    emergency_stop: bool = False
    block_new_trades: bool = False
    close_positions: bool = False
    
    # Performance metrics
    last_updated: datetime = field(default_factory=datetime.now)
    update_count: int = 0
    
    def get_highest_risk_level(self) -> RiskLevel:
        """Get the highest active risk level."""
        if self.emergency_stop:
            return RiskLevel.EMERGENCY
            
        active_assessments = [
            assessment for assessment in self.risk_assessments.values()
            if not assessment.is_expired()
        ]
        
        if not active_assessments:
            return RiskLevel.LOW
            
        return max(assessment.level for assessment in active_assessments)
    
    def should_block_trades(self) -> bool:
        """Determine if trades should be blocked based on current risk state."""
        if self.emergency_stop or self.block_new_trades:
            return True
            
        highest_risk = self.get_highest_risk_level()
        return highest_risk >= RiskLevel.CRITICAL


class SharedRiskContextManager:
    """
    Thread-safe manager for shared risk context between components.
    
    This class enables communication between hierarchical decision framework
    and five-layer risk manager while maintaining their independence.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()
        self._state = SharedRiskState()
        self._subscribers: List[callable] = []
        
        # Performance optimization - cached decisions
        self._decision_cache: Dict[str, Tuple[bool, datetime]] = {}
        self._cache_ttl = timedelta(seconds=30)  # 30-second cache for performance
        
        self.logger.info("🔗 SharedRiskContextManager initialized")
    
    def update_market_regime(self, regime: str, confidence: float, numeric_value: float,
                           source: RiskSource = RiskSource.HIERARCHICAL_DECISION):
        """Update market regime information."""
        with self._lock:
            self._state.market_regime = regime
            self._state.market_regime_confidence = confidence
            self._state.market_regime_numeric = numeric_value
            self._state.last_updated = datetime.now()
            self._state.update_count += 1
            
            self.logger.info(f"📊 Market regime updated: {regime} (confidence: {confidence:.3f}, numeric: {numeric_value:.3f})")
            self._notify_subscribers("market_regime_updated")
    
    def update_session_info(self, session: str, multiplier: float,
                          source: RiskSource = RiskSource.HIERARCHICAL_DECISION):
        """Update session information."""
        with self._lock:
            self._state.current_session = session
            self._state.session_multiplier = multiplier
            self._state.last_updated = datetime.now()
            
            self.logger.info(f"🕐 Session updated: {session} (multiplier: {multiplier:.3f})")
            self._notify_subscribers("session_updated")
    
    def update_volatility_info(self, regime: str, state: str,
                             source: RiskSource = RiskSource.HIERARCHICAL_DECISION):
        """Update volatility information."""
        with self._lock:
            self._state.volatility_regime = regime
            self._state.volatility_state = state
            self._state.last_updated = datetime.now()
            
            self.logger.info(f"🌪️ Volatility updated: regime={regime}, state={state}")
            self._notify_subscribers("volatility_updated")
    
    def add_risk_assessment(self, assessment: RiskAssessment):
        """Add or update a risk assessment from a component."""
        with self._lock:
            self._state.risk_assessments[assessment.source] = assessment
            self._state.last_updated = datetime.now()
            
            # Handle emergency conditions
            if assessment.level == RiskLevel.EMERGENCY:
                self._state.emergency_stop = True
                self.logger.critical(f"🚨 EMERGENCY STOP triggered by {assessment.source.value}: {assessment.reasoning}")
            elif assessment.level == RiskLevel.CRITICAL:
                self._state.block_new_trades = True
                self.logger.error(f"⛔ BLOCKING NEW TRADES due to {assessment.source.value}: {assessment.reasoning}")
            
            self._clear_decision_cache()  # Clear cache when risk state changes
            self._notify_subscribers("risk_assessment_updated")
    
    def set_emergency_stop(self, reason: str, source: RiskSource = RiskSource.SYSTEM_HEALTH):
        """Set emergency stop flag."""
        with self._lock:
            self._state.emergency_stop = True
            
            emergency_assessment = RiskAssessment(
                source=source,
                level=RiskLevel.EMERGENCY,
                confidence=1.0,
                reasoning=reason,
                timestamp=datetime.now()
            )
            
            self._state.risk_assessments[source] = emergency_assessment
            self.logger.critical(f"🚨 EMERGENCY STOP SET: {reason}")
            self._notify_subscribers("emergency_stop_set")
    
    def clear_emergency_stop(self, source: RiskSource = RiskSource.SYSTEM_HEALTH):
        """Clear emergency stop flag."""
        with self._lock:
            self._state.emergency_stop = False
            self._state.block_new_trades = False
            
            # Remove emergency assessment from this source
            if source in self._state.risk_assessments:
                del self._state.risk_assessments[source]
            
            self.logger.info(f"✅ Emergency stop cleared by {source.value}")
            self._clear_decision_cache()
            self._notify_subscribers("emergency_stop_cleared")
    
    def should_allow_trade(self, cache_key: Optional[str] = None) -> Tuple[bool, str]:
        """
        Determine if a trade should be allowed based on current risk state.
        
        Args:
            cache_key: Optional key for caching decision (for performance)
            
        Returns:
            Tuple of (allow_trade, reasoning)
        """
        # Check cache first for performance
        if cache_key and cache_key in self._decision_cache:
            cached_decision, cached_time = self._decision_cache[cache_key]
            if datetime.now() - cached_time < self._cache_ttl:
                return cached_decision, "Cached decision"
        
        with self._lock:
            # Emergency conditions
            if self._state.emergency_stop:
                decision = (False, "Emergency stop active")
            elif self._state.block_new_trades:
                decision = (False, "New trades blocked due to high risk")
            elif self._state.should_block_trades():
                decision = (False, f"Risk level too high: {self._state.get_highest_risk_level().name}")
            else:
                decision = (True, "Risk conditions acceptable")
            
            # Cache decision for performance
            if cache_key:
                self._decision_cache[cache_key] = (decision, datetime.now())
            
            return decision
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk summary."""
        with self._lock:
            active_assessments = {
                source.value: assessment for source, assessment in self._state.risk_assessments.items()
                if not assessment.is_expired()
            }
            
            return {
                'market_regime': self._state.market_regime,
                'market_regime_confidence': self._state.market_regime_confidence,
                'current_session': self._state.current_session,
                'session_multiplier': self._state.session_multiplier,
                'volatility_regime': self._state.volatility_regime,
                'highest_risk_level': self._state.get_highest_risk_level().name,
                'emergency_stop': self._state.emergency_stop,
                'block_new_trades': self._state.block_new_trades,
                'active_assessments': len(active_assessments),
                'last_updated': self._state.last_updated.isoformat(),
                'update_count': self._state.update_count
            }
    
    def subscribe(self, callback: callable):
        """Subscribe to risk state changes."""
        self._subscribers.append(callback)
        self.logger.info(f"📡 New subscriber added: {callback.__name__}")
    
    def _notify_subscribers(self, event_type: str):
        """Notify all subscribers of state changes."""
        for callback in self._subscribers:
            try:
                callback(event_type, self._state)
            except Exception as e:
                self.logger.error(f"Error notifying subscriber {callback.__name__}: {e}")
    
    def _clear_decision_cache(self):
        """Clear the decision cache when risk state changes."""
        self._decision_cache.clear()
        self.logger.debug("🗑️ Decision cache cleared due to risk state change")


# Global shared risk context instance
_shared_risk_context = None
_context_lock = threading.Lock()


def get_shared_risk_context() -> SharedRiskContextManager:
    """Get the global shared risk context instance (singleton pattern)."""
    global _shared_risk_context
    
    if _shared_risk_context is None:
        with _context_lock:
            if _shared_risk_context is None:
                _shared_risk_context = SharedRiskContextManager()
    
    return _shared_risk_context
