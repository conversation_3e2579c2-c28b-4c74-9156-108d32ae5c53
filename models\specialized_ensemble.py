"""
Specialized Ensemble Architecture Implementation

This module implements the professional-grade specialized ensemble architecture
where each model has distinct roles and outputs, following Option B specifications.

Architecture:
- LightGBM: Signal Generator (40% weight) - Direction & Strength Detection
- CatBoost: Market Regime Analyst (25% weight) - Timing & Context Analysis  
- XGBoost: Risk Manager (25% weight) - Position & Risk Assessment
- Linear: Stability Monitor (10% weight) - Baseline & Health Check
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from data_collection.error_handling.logger import LoggerMixin
from models.base import BaseModelTrainer, TrainingResult, PredictionResult


class ModelRole(Enum):
    """Enumeration of specialized model roles."""
    SIGNAL_GENERATOR = "signal_generator"      # LightGBM
    MARKET_REGIME_ANALYST = "market_regime_analyst"  # CatBoost
    RISK_MANAGER = "risk_manager"              # XGBoost
    STABILITY_MONITOR = "stability_monitor"    # Linear


@dataclass
class SpecializedOutput:
    """Container for specialized model outputs."""
    model_role: ModelRole
    model_name: str
    outputs: Dict[str, Any]
    confidence: float
    timestamp: datetime
    processing_time: float


@dataclass
class EnsembleDecision:
    """Final ensemble trading decision."""
    # Core decision
    trade_direction: str  # STRONG_SELL, SELL, HOLD, BUY, STRONG_BUY
    position_size: float  # 0.0 - 2.0 multiplier
    entry_timing: int     # Bars to wait before entry
    
    # Risk management
    stop_loss_pips: float
    take_profit_levels: List[float]  # [TP1, TP2, TP3]
    
    # Confidence metrics
    overall_confidence: float
    signal_quality: float
    market_suitability: float
    risk_assessment: float
    system_health: float
    
    # Decision attribution
    primary_driver: str
    contributing_models: List[str]
    decision_weights: Dict[str, float]
    
    # Metadata
    timestamp: datetime
    processing_time: float


class LightGBMSignalGenerator(LoggerMixin):
    """
    LightGBM specialized as Signal Generator.
    
    Responsibilities:
    - Primary trading signal generation
    - Direction classification with confidence
    - Signal strength and persistence estimation
    - Return expectation calculation
    """
    
    def __init__(self, model, config: Dict[str, Any]):
        """Initialize LightGBM Signal Generator."""
        # Handle both single model and multi-output model dictionary
        if isinstance(model, dict):
            self.models = model  # Multi-output models
            self.model = None    # No single model
        else:
            self.model = model   # Single model
            self.models = None   # No multi-output

        self.config = config
        self.role = ModelRole.SIGNAL_GENERATOR
        self.weight = 0.40  # 40% decision weight

        # 🔍 DEBUG: Track previous features to detect changes
        self._previous_feature_hash = None
        self._current_cycle_hash = None
        self._cycle_call_count = 0

        self.logger.info("LightGBM Signal Generator initialized")
    
    def generate_signals(self, features: pd.DataFrame) -> SpecializedOutput:
        """
        Generate specialized signal outputs.

        Returns:
            SpecializedOutput with signal-specific predictions
        """
        start_time = datetime.now()

        try:
            # 🔍 DEBUG: Log model prediction process
            self.logger.info(f"🔍 LightGBM Signal Generation - Features shape: {features.shape}")

            # 🔍 CRITICAL DEBUG: Log feature values to detect if they're identical
            import hashlib
            feature_hash = hashlib.md5(str(features.values).encode()).hexdigest()[:8]
            feature_sample = features.iloc[0, :10].values if len(features.columns) >= 10 else features.iloc[0].values
            self.logger.info(f"🔍 LIGHTGBM INPUT FEATURES:")
            self.logger.info(f"   Feature hash: {feature_hash}")
            self.logger.info(f"   Sample values: {feature_sample}")
            self.logger.info(f"   Feature columns: {list(features.columns[:10])}")

            # 🔍 Compare with previous features and track cycle calls
            if self._current_cycle_hash != feature_hash:
                # New cycle detected
                self._current_cycle_hash = feature_hash
                self._cycle_call_count = 1

                if self._previous_feature_hash == feature_hash:
                    self.logger.warning(f"⚠️ LightGBM receiving same features as previous cycle: {feature_hash}")
                    self.logger.warning(f"⚠️ This may indicate static market conditions or data issues")
                else:
                    self.logger.info(f"✅ LightGBM features changed: {self._previous_feature_hash} → {feature_hash}")

                self._previous_feature_hash = feature_hash
            else:
                # Same cycle, multiple calls (expected behavior)
                self._cycle_call_count += 1
                self.logger.debug(f"🔄 LightGBM call #{self._cycle_call_count} within same cycle (hash: {feature_hash})")
                self.logger.debug(f"🔄 This is expected behavior - same features within trading cycle")

            # Get base predictions from LightGBM (handle multi-output structure)
            if self.models:
                # Multi-output model dictionary
                self.logger.info(f"🔍 Using multi-output models: {list(self.models.keys())}")
                base_predictions = {}
                for target_name, target_model in self.models.items():
                    if hasattr(target_model, 'predict'):
                        pred = target_model.predict(features)
                        base_predictions[target_name] = pred
                        self.logger.info(f"🔍 {target_name} prediction: {pred[:5] if hasattr(pred, '__len__') else pred}")
                    else:
                        self.logger.warning(f"Model for {target_name} has no predict method")
                        base_predictions[target_name] = np.zeros(len(features))
            else:
                # Single model
                self.logger.info(f"🔍 Using single model: {type(self.model)}")
                base_predictions = self.model.predict(features)
                self.logger.info(f"🔍 Single model prediction: {base_predictions[:5] if hasattr(base_predictions, '__len__') else base_predictions}")

            # Convert to specialized signal outputs
            outputs = self._convert_to_signal_outputs(base_predictions, features)
            self.logger.info(f"🔍 Converted signal outputs: {outputs}")

            # Calculate signal confidence
            confidence = self._calculate_signal_confidence(outputs, features)
            self.logger.info(f"🔍 Calculated signal confidence: {confidence}")
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return SpecializedOutput(
                model_role=self.role,
                model_name="lightgbm",
                outputs=outputs,
                confidence=confidence,
                timestamp=datetime.now(),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Signal generation failed: {str(e)}")
            return self._create_fallback_signal_output()
    
    def _convert_to_signal_outputs(self, predictions, features: pd.DataFrame) -> Dict[str, Any]:
        """Convert base predictions to specialized signal outputs."""
        # Handle both dictionary and array predictions
        if isinstance(predictions, dict):
            # Multi-output predictions - use signal_probability as primary signal
            signal_strength = predictions.get('signal_probability', np.zeros(len(features)))
            if isinstance(signal_strength, np.ndarray) and signal_strength.ndim > 1:
                signal_strength = signal_strength.flatten()
        else:
            # Array predictions
            if predictions.ndim > 1:
                signal_strength = predictions[:, 0] if predictions.shape[1] > 0 else np.zeros(len(features))
            else:
                signal_strength = predictions
        
        # Convert to classification
        direction = self._classify_direction(signal_strength)
        
        # Calculate additional signal metrics
        return {
            'direction_classification': direction,
            'signal_strength': float(np.mean(np.abs(signal_strength))),
            'confidence_score': float(np.mean(np.abs(signal_strength))),
            'expected_pips': float(np.mean(signal_strength) * 20),  # Scale to pips
            'signal_persistence': self._estimate_persistence(signal_strength),
            'market_agreement': self._calculate_market_agreement(features)
        }
    
    def _classify_direction(self, signal_strength: np.ndarray) -> str:
        """Classify signal into direction categories."""
        avg_signal = np.mean(signal_strength)
        
        if avg_signal > 0.8:
            return "STRONG_BUY"
        elif avg_signal > 0.3:
            return "BUY"
        elif avg_signal < -0.8:
            return "STRONG_SELL"
        elif avg_signal < -0.3:
            return "SELL"
        else:
            return "HOLD"
    
    def _estimate_persistence(self, signal_strength: np.ndarray) -> int:
        """Estimate signal persistence in bars."""
        # Simple persistence estimation based on signal consistency
        consistency = 1.0 - np.std(signal_strength) / (np.mean(np.abs(signal_strength)) + 1e-8)
        return int(max(1, min(30, consistency * 20)))
    
    def _calculate_market_agreement(self, features: pd.DataFrame) -> float:
        """Calculate market agreement level."""
        # Use cross-asset features to assess market agreement
        cross_asset_cols = [col for col in features.columns if any(asset in col.lower() 
                           for asset in ['dxy', 'spy', 'vix', 'tlt'])]
        
        if cross_asset_cols:
            cross_asset_data = features[cross_asset_cols].fillna(0)
            # Simple agreement metric based on correlation consistency
            return float(min(1.0, max(0.0, np.mean(np.abs(cross_asset_data.corr().values)))))
        
        return 0.5  # Neutral if no cross-asset data
    
    def _calculate_signal_confidence(self, outputs: Dict[str, Any], features: pd.DataFrame) -> float:
        """Calculate overall signal confidence."""
        
        signal_strength = outputs.get('signal_strength', 0.0)
        market_agreement = outputs.get('market_agreement', 0.5)
        
        # Combine factors for confidence
        confidence = (signal_strength )
        return float(min(1.0, max(0.0, confidence)))
    
    def _create_fallback_signal_output(self) -> SpecializedOutput:
        """Create fallback output when signal generation fails."""
        return SpecializedOutput(
            model_role=self.role,
            model_name="lightgbm",
            outputs={
                'direction_classification': "HOLD",
                'signal_strength': 0.0,
                'confidence_score': 0.0,
                'expected_pips': 0.0,
                'signal_persistence': 1,
                'market_agreement': 0.5
            },
            confidence=0.0,
            timestamp=datetime.now(),
            processing_time=0.0
        )


class CatBoostMarketRegimeAnalyst(LoggerMixin):
    """
    CatBoost specialized as Market Regime Analyst.
    
    Responsibilities:
    - Market regime classification
    - Session analysis and timing optimization
    - Volatility state assessment
    - Entry timing recommendations
    """
    
    def __init__(self, model, config: Dict[str, Any]):
        """Initialize CatBoost Market Regime Analyst."""
        # Handle both single model and multi-output model dictionary
        if isinstance(model, dict):
            self.models = model  # Multi-output models
            self.model = None    # No single model
        else:
            self.model = model   # Single model
            self.models = None   # No multi-output

        self.config = config
        self.role = ModelRole.MARKET_REGIME_ANALYST
        self.weight = 0.25  # 25% decision weight

        self.logger.info("CatBoost Market Regime Analyst initialized")
    
    def analyze_market_regime(self, features: pd.DataFrame) -> SpecializedOutput:
        """
        Analyze market regime and timing.
        
        Returns:
            SpecializedOutput with regime-specific analysis
        """
        start_time = datetime.now()
        
        try:
            # Get base predictions from CatBoost (handle multi-output structure)
            if self.models:
                # Multi-output model dictionary
                base_predictions = {}
                for target_name, target_model in self.models.items():
                    if hasattr(target_model, 'predict'):
                        base_predictions[target_name] = target_model.predict(features)
                    else:
                        self.logger.warning(f"Model for {target_name} has no predict method")
                        base_predictions[target_name] = np.zeros(len(features))
            else:
                # Single model
                base_predictions = self.model.predict(features)

            # Convert to specialized regime outputs
            outputs = self._convert_to_regime_outputs(base_predictions, features)
            
            # Calculate regime confidence
            confidence = self._calculate_regime_confidence(outputs, features)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return SpecializedOutput(
                model_role=self.role,
                model_name="catboost",
                outputs=outputs,
                confidence=confidence,
                timestamp=datetime.now(),
                processing_time=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Regime analysis failed: {str(e)}")
            return self._create_fallback_regime_output()
    
    def _convert_to_regime_outputs(self, predictions: np.ndarray, features: pd.DataFrame) -> Dict[str, Any]:
        """Convert base predictions to specialized regime outputs."""
        # Analyze current market conditions
        current_time = datetime.now()
        
        return {
            'market_regime': self._classify_market_regime(predictions, features),
            'session_favorability': self._assess_session_favorability(current_time, features),
            'volatility_state': self._classify_volatility_state(features),
            'optimal_entry_delay': self._calculate_optimal_entry_delay(predictions, features),
            'regime_confidence': self._assess_regime_stability(features)
        }
    
    def _classify_market_regime(self, predictions: np.ndarray, features: pd.DataFrame) -> str:
        """Classify current market regime."""
        # Use volatility and trend indicators to classify regime
        volatility_cols = [col for col in features.columns if 'volatility' in col.lower() or 'atr' in col.lower()]
        trend_cols = [col for col in features.columns if any(indicator in col.lower() 
                     for indicator in ['ma', 'ema', 'trend', 'momentum'])]
        
        if volatility_cols and trend_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            trend_data = features[trend_cols].fillna(0).mean(axis=1)
            
            avg_vol = np.mean(vol_data)
            avg_trend = np.mean(np.abs(trend_data))
            
            if avg_vol > 0.7 and avg_trend > 0.5:
                return "VOLATILE"
            elif avg_trend > 0.6:
                return "TRENDING"
            elif avg_vol < 0.3:
                return "QUIET"
            else:
                return "RANGING"
        
        return "RANGING"  # Default
    
    def _assess_session_favorability(self, current_time: datetime, features: pd.DataFrame) -> str:
        """Assess current session favorability."""
        hour = current_time.hour
        
        # Define session favorability based on historical patterns
        if 8 <= hour <= 12:  # European session
            return "EXCELLENT"
        elif 13 <= hour <= 17:  # US/European overlap
            return "GOOD"
        elif 0 <= hour <= 4:   # Asian session
            return "FAIR"
        else:
            return "POOR"
    
    def _classify_volatility_state(self, features: pd.DataFrame) -> str:
        """Classify current volatility state."""
        volatility_cols = [col for col in features.columns if 'volatility' in col.lower() or 'atr' in col.lower()]
        
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            
            if avg_vol > 0.8:
                return "EXTREME"
            elif avg_vol > 0.6:
                return "HIGH"
            elif avg_vol < 0.3:
                return "LOW"
            else:
                return "NORMAL"
        
        return "NORMAL"  # Default
    
    def _calculate_optimal_entry_delay(self, predictions: np.ndarray, features: pd.DataFrame) -> int:
        """Calculate optimal entry delay in bars."""
        # Simple delay calculation based on regime and volatility
        regime = self._classify_market_regime(predictions, features)
        vol_state = self._classify_volatility_state(features)
        
        if regime == "VOLATILE" or vol_state == "EXTREME":
            return 5  # Wait for volatility to settle
        elif regime == "TRENDING":
            return 2  # Quick entry in trending markets
        else:
            return 3  # Standard delay
    
    def _assess_regime_stability(self, features: pd.DataFrame) -> float:
        """Assess regime stability/confidence."""
        # Use feature consistency to assess regime stability
        if len(features) > 10:
            recent_features = features.tail(10)
            stability = 1.0 - np.mean(np.std(recent_features.fillna(0), axis=0))
            return float(min(1.0, max(0.0, stability)))
        
        return 0.5  # Neutral if insufficient data
    
    def _calculate_regime_confidence(self, outputs: Dict[str, Any], features: pd.DataFrame) -> float:
        """Calculate overall regime analysis confidence."""
        regime_confidence = outputs.get('regime_confidence', 0.5)
        session_quality = {'EXCELLENT': 1.0, 'GOOD': 0.8, 'FAIR': 0.6, 'POOR': 0.4}.get(
            outputs.get('session_favorability', 'FAIR'), 0.6)
        
        # Combine factors
        confidence = (regime_confidence * 0.6 + session_quality * 0.4)
        return float(min(1.0, max(0.0, confidence)))
    
    def _create_fallback_regime_output(self) -> SpecializedOutput:
        """Create fallback output when regime analysis fails."""
        return SpecializedOutput(
            model_role=self.role,
            model_name="catboost",
            outputs={
                'market_regime': "RANGING",
                'session_favorability': "FAIR",
                'volatility_state': "NORMAL",
                'optimal_entry_delay': 3,
                'regime_confidence': 0.5
            },
            confidence=0.5,
            timestamp=datetime.now(),
            processing_time=0.0
        )


class XGBoostRiskManager(LoggerMixin):
    """
    XGBoost specialized as Risk Manager.

    Responsibilities:
    - Position sizing optimization
    - Stop loss calculation
    - Take profit level determination
    - Risk classification and assessment
    - Trade quality scoring
    """

    def __init__(self, model, config: Dict[str, Any]):
        """Initialize XGBoost Risk Manager."""
        # Handle both single model and multi-output model dictionary
        if isinstance(model, dict):
            self.models = model  # Multi-output models
            self.model = None    # No single model
        else:
            self.model = model   # Single model
            self.models = None   # No multi-output

        self.config = config
        self.role = ModelRole.RISK_MANAGER
        self.weight = 0.25  # 25% decision weight

        self.logger.info("XGBoost Risk Manager initialized")

    def assess_risk(self, features: pd.DataFrame, signal_info: Dict[str, Any] = None) -> SpecializedOutput:
        """
        Assess risk and determine position parameters.

        Args:
            features: Market features
            signal_info: Information from signal generator

        Returns:
            SpecializedOutput with risk management parameters
        """
        start_time = datetime.now()

        try:
            # Get base predictions from XGBoost (handle multi-output structure)
            if self.models:
                # Multi-output model dictionary
                base_predictions = {}
                for target_name, target_model in self.models.items():
                    if hasattr(target_model, 'predict'):
                        try:
                            # Check if this is an XGBoost model (xgboost.core.Booster)
                            if 'xgboost' in str(type(target_model)).lower():
                                # XGBoost Booster needs DMatrix format
                                import xgboost as xgb
                                dmatrix = xgb.DMatrix(features)
                                base_predictions[target_name] = target_model.predict(dmatrix)
                            else:
                                # Regular model (sklearn, etc.)
                                base_predictions[target_name] = target_model.predict(features)
                        except Exception as e:
                            self.logger.warning(f"Prediction failed for {target_name}: {str(e)}")
                            base_predictions[target_name] = np.zeros(len(features))
                    else:
                        self.logger.warning(f"Model for {target_name} has no predict method")
                        base_predictions[target_name] = np.zeros(len(features))
            else:
                # Single model
                try:
                    if 'xgboost' in str(type(self.model)).lower():
                        # XGBoost Booster model
                        import xgboost as xgb
                        dmatrix = xgb.DMatrix(features)
                        base_predictions = self.model.predict(dmatrix)
                    else:
                        # Regular model
                        base_predictions = self.model.predict(features)
                except Exception as e:
                    self.logger.warning(f"Single model prediction failed: {str(e)}")
                    base_predictions = np.zeros(len(features))

            # Convert to specialized risk outputs
            outputs = self._convert_to_risk_outputs(base_predictions, features, signal_info)

            # Calculate risk confidence
            confidence = self._calculate_risk_confidence(outputs, features)

            processing_time = (datetime.now() - start_time).total_seconds()

            return SpecializedOutput(
                model_role=self.role,
                model_name="xgboost",
                outputs=outputs,
                confidence=confidence,
                timestamp=datetime.now(),
                processing_time=processing_time
            )

        except Exception as e:
            self.logger.error(f"Risk assessment failed: {str(e)}")
            return self._create_fallback_risk_output()

    def _convert_to_risk_outputs(self, predictions, features: pd.DataFrame,
                                signal_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """Convert base predictions to specialized risk outputs."""
        # Handle both numpy array and dictionary predictions
        if isinstance(predictions, dict):
            # Multi-output model predictions - use first prediction as base risk
            first_key = list(predictions.keys())[0]
            base_predictions = predictions[first_key]
            base_risk = np.mean(base_predictions) if len(base_predictions) > 0 else 0.0
        else:
            # Single numpy array predictions
            base_risk = np.mean(predictions) if predictions.size > 0 else 0.0

        # Calculate position sizing
        position_multiplier = self._calculate_position_size(base_risk, features, signal_info)

        # Calculate stop loss
        stop_loss_pips = self._calculate_stop_loss(base_risk, features, signal_info)

        # Calculate take profit levels
        take_profit_levels = self._calculate_take_profit_levels(base_risk, features, signal_info)

        # Classify risk level
        risk_classification = self._classify_risk_level(base_risk, features)

        # Calculate trade quality
        trade_quality = self._calculate_trade_quality(base_risk, features, signal_info)

        return {
            'position_size_multiplier': position_multiplier,
            'stop_loss_pips': stop_loss_pips,
            'take_profit_levels': take_profit_levels,
            'risk_classification': risk_classification,
            'trade_quality_score': trade_quality
        }

    def _calculate_position_size(self, base_risk: float, features: pd.DataFrame,
                               signal_info: Dict[str, Any] = None) -> float:
        """Calculate optimal position size multiplier."""
        # Base position size from risk assessment
        base_size = max(0.1, min(2.0, 1.0 - abs(base_risk)))

        # Adjust based on signal confidence if available
        if signal_info and 'confidence_score' in signal_info:
            confidence_adj = signal_info['confidence_score']
            base_size *= (0.5 + 0.5 * confidence_adj)

        # Adjust based on volatility
        volatility_cols = [col for col in features.columns if 'volatility' in col.lower()]
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            vol_adj = max(0.5, min(1.5, 1.0 - avg_vol * 0.5))
            base_size *= vol_adj

        return float(max(0.1, min(2.0, base_size)))

    def _calculate_stop_loss(self, base_risk: float, features: pd.DataFrame,
                           signal_info: Dict[str, Any] = None) -> float:
        """Calculate optimal stop loss in pips for XAUUSD."""
        # XAUUSD-appropriate base stop loss (much wider than forex pairs)
        base_sl = 60.0 + abs(base_risk) * 40.0  # 60-100 pips range for XAUUSD

        # Adjust based on volatility (XAUUSD is inherently more volatile)
        volatility_cols = [col for col in features.columns if 'atr' in col.lower() or 'volatility' in col.lower()]
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            vol_adj = 1.0 + avg_vol * 0.8  # More aggressive volatility adjustment for gold
            base_sl *= vol_adj

        # Use signal strength to adjust SL
        if signal_info and 'expected_pips' in signal_info:
            expected_pips = abs(signal_info['expected_pips'])
            if expected_pips > 0:
                # SL should be proportional to expected move (risk-reward ratio)
                base_sl = max(base_sl, expected_pips * 0.4)  # SL = 40% of expected move

        return float(max(30.0, min(150.0, base_sl)))  # XAUUSD appropriate range

    def _calculate_take_profit_levels(self, base_risk: float, features: pd.DataFrame,
                                    signal_info: Dict[str, Any] = None) -> List[float]:
        """Calculate multiple take profit levels for XAUUSD."""
        # XAUUSD-appropriate base take profit calculation
        base_tp = 80.0 - abs(base_risk) * 30.0  # 50-80 pips base range for XAUUSD

        # Adjust based on expected pips from signal (more aggressive for gold)
        if signal_info and 'expected_pips' in signal_info:
            expected_pips = abs(signal_info['expected_pips'])
            if expected_pips > 0:
                base_tp = max(base_tp, expected_pips * 0.7)  # 70% of expected move

        # Adjust based on volatility (XAUUSD moves in larger ranges)
        volatility_cols = [col for col in features.columns if 'atr' in col.lower() or 'volatility' in col.lower()]
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            vol_adj = 1.0 + avg_vol * 0.6  # Increase TPs in high volatility
            base_tp *= vol_adj

        # Create multiple TP levels appropriate for XAUUSD
        tp1 = max(40.0, base_tp * 0.7)      # Conservative first TP (40% of position)
        tp2 = max(80.0, base_tp * 1.2)      # Standard second TP (35% of position)
        tp3 = max(120.0, base_tp * 2.0)     # Aggressive third TP (25% of position)

        return [float(tp1), float(tp2), float(tp3)]

    def _classify_risk_level(self, base_risk: float, features: pd.DataFrame) -> str:
        """Classify overall risk level."""
        risk_score = abs(base_risk)

        # Adjust based on market conditions
        volatility_cols = [col for col in features.columns if 'volatility' in col.lower()]
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            risk_score += avg_vol * 0.3

        if risk_score > 0.8:
            return "EXTREME"
        elif risk_score > 0.6:
            return "HIGH"
        elif risk_score < 0.3:
            return "LOW"
        else:
            return "MEDIUM"

    def _calculate_trade_quality(self, base_risk: float, features: pd.DataFrame,
                               signal_info: Dict[str, Any] = None) -> float:
        """Calculate overall trade quality score."""
        # Base quality from risk assessment
        base_quality = max(0.0, 1.0 - abs(base_risk))

        # Adjust based on signal quality if available
        if signal_info and 'confidence_score' in signal_info:
            signal_quality = signal_info['confidence_score']
            base_quality = (base_quality * 0.6 + signal_quality * 0.4)

        # Adjust based on market conditions
        volatility_cols = [col for col in features.columns if 'volatility' in col.lower()]
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            vol_penalty = min(0.3, avg_vol * 0.2)  # Penalize high volatility
            base_quality -= vol_penalty

        return float(max(0.0, min(1.0, base_quality)))

    def _calculate_risk_confidence(self, outputs: Dict[str, Any], features: pd.DataFrame) -> float:
        """Calculate overall risk assessment confidence."""
        trade_quality = outputs.get('trade_quality_score', 0.5)
        risk_level = outputs.get('risk_classification', 'MEDIUM')

        # Risk level confidence mapping
        risk_confidence = {'LOW': 0.9, 'MEDIUM': 0.7, 'HIGH': 0.5, 'EXTREME': 0.3}.get(risk_level, 0.6)

        # Combine factors
        confidence = (trade_quality * 0.6 + risk_confidence * 0.4)
        return float(min(1.0, max(0.0, confidence)))

    def _create_fallback_risk_output(self) -> SpecializedOutput:
        """Create fallback output when risk assessment fails."""
        return SpecializedOutput(
            model_role=self.role,
            model_name="xgboost",
            outputs={
                'position_size_multiplier': 0.5,  # Conservative sizing
                'stop_loss_pips': 80.0,  # XAUUSD appropriate fallback
                'take_profit_levels': [60.0, 120.0, 200.0],  # XAUUSD appropriate fallback
                'risk_classification': "MEDIUM",
                'trade_quality_score': 0.5
            },
            confidence=0.3,  # Low confidence for fallback
            timestamp=datetime.now(),
            processing_time=0.0
        )


class LinearStabilityMonitor(LoggerMixin):
    """
    Linear Model specialized as Stability Monitor.

    Responsibilities:
    - Baseline signal validation
    - System health monitoring
    - Market stress detection
    - Correlation breakdown alerts
    - Overall system stability assessment
    """

    def __init__(self, model, config: Dict[str, Any]):
        """Initialize Linear Stability Monitor."""
        # Handle both single model and multi-output model dictionary
        if isinstance(model, dict):
            self.models = model  # Multi-output models
            self.model = None    # No single model
        else:
            self.model = model   # Single model
            self.models = None   # No multi-output

        self.config = config
        self.role = ModelRole.STABILITY_MONITOR
        self.weight = 0.10  # 10% decision weight

        # Historical baselines for comparison
        self.baseline_metrics = {
            'correlation_threshold': 0.3,
            'volatility_threshold': 0.7,
            'health_threshold': 0.5
        }

        self.logger.info("Linear Stability Monitor initialized")

    def monitor_stability(self, features: pd.DataFrame, other_outputs: List[SpecializedOutput] = None) -> SpecializedOutput:
        """
        Monitor system stability and health.

        Args:
            features: Market features
            other_outputs: Outputs from other specialized models

        Returns:
            SpecializedOutput with stability assessment
        """
        start_time = datetime.now()

        try:
            # Get base predictions from Linear model (handle multi-output structure)
            if self.models:
                # Multi-output model dictionary
                base_predictions = {}
                for target_name, target_model in self.models.items():
                    if hasattr(target_model, 'predict'):
                        base_predictions[target_name] = target_model.predict(features)
                    else:
                        self.logger.warning(f"Model for {target_name} has no predict method")
                        base_predictions[target_name] = np.zeros(len(features))
            else:
                # Single model
                base_predictions = self.model.predict(features)

            # Convert to specialized stability outputs
            outputs = self._convert_to_stability_outputs(base_predictions, features, other_outputs)

            # Calculate stability confidence
            confidence = self._calculate_stability_confidence(outputs, features)

            processing_time = (datetime.now() - start_time).total_seconds()

            return SpecializedOutput(
                model_role=self.role,
                model_name="linear",
                outputs=outputs,
                confidence=confidence,
                timestamp=datetime.now(),
                processing_time=processing_time
            )

        except Exception as e:
            self.logger.error(f"Stability monitoring failed: {str(e)}")
            return self._create_fallback_stability_output()

    def _convert_to_stability_outputs(self, predictions: np.ndarray, features: pd.DataFrame,
                                    other_outputs: List[SpecializedOutput] = None) -> Dict[str, Any]:
        """Convert base predictions to specialized stability outputs."""
        # Simple baseline signal
        baseline_signal = self._generate_baseline_signal(predictions)

        # System health assessment
        system_health = self._assess_system_health(features, other_outputs)

        # Market stress detection
        market_stress = self._detect_market_stress(features)

        # Correlation breakdown detection
        correlation_alert = self._detect_correlation_breakdown(features)

        # Overall stability score
        stability_score = self._calculate_stability_score(features, other_outputs)

        return {
            'baseline_signal': baseline_signal,
            'system_health_score': system_health,
            'market_stress_indicator': market_stress,
            'correlation_breakdown_alert': correlation_alert,
            'overall_stability_score': stability_score
        }

    def _generate_baseline_signal(self, predictions) -> str:
        """Generate simple baseline trading signal."""
        # Handle both numpy array and other prediction formats
        if isinstance(predictions, np.ndarray):
            if predictions.size == 0:
                return "HOLD"
            # 🔧 CRITICAL FIX: Use only the first output (linear_signal) instead of averaging all outputs
            # The linear model outputs 7 values: [linear_signal, trend_direction, momentum_strength,
            # volatility_level, simple_tp_distance, simple_sl_distance, hold_recommendation]
            # We only want the linear_signal (index 0) for baseline signal determination
            if predictions.ndim > 1:
                linear_signal = predictions[0, 0] if predictions.shape[0] > 0 else 0.0  # First sample, first output
            else:
                linear_signal = predictions[0] if len(predictions) > 0 else 0.0  # First output
        else:
            # Handle other formats (like MultiOutputRegressor results)
            try:
                if hasattr(predictions, '__len__') and len(predictions) > 0:
                    # For multi-output predictions, use only the first output (linear_signal)
                    if hasattr(predictions[0], '__len__'):
                        linear_signal = predictions[0][0]  # First sample, first output
                    else:
                        linear_signal = predictions[0]  # First output
                else:
                    linear_signal = 0.0
            except:
                return "HOLD"

        # Apply threshold logic to linear_signal only (not average of all outputs)
        if linear_signal > 0.3:
            signal = "BUY"
        elif linear_signal < -0.3:
            signal = "SELL"
        else:
            signal = "HOLD"

        # 🔧 DEBUG: Add logging to track baseline signal generation (can be removed later)
        if hasattr(self, 'logger'):
            self.logger.debug(f"🔧 Baseline signal: linear_signal={linear_signal:.4f} → {signal}")

        return signal

    def _assess_system_health(self, features: pd.DataFrame, other_outputs: List[SpecializedOutput] = None) -> float:
        """Assess overall system health."""
        health_score = 1.0

        # Check feature quality
        if len(features) > 0:
            nan_ratio = features.isnull().sum().sum() / (features.shape[0] * features.shape[1])
            health_score -= min(0.3, nan_ratio * 2)  # Penalize missing data

        # Check model agreement if other outputs available
        if other_outputs and len(other_outputs) > 1:
            confidences = [output.confidence for output in other_outputs]
            avg_confidence = np.mean(confidences)
            if avg_confidence < 0.5:
                health_score -= 0.2  # Penalize low model confidence

        return float(max(0.0, min(1.0, health_score)))

    def _detect_market_stress(self, features: pd.DataFrame) -> float:
        """Detect market stress indicators."""
        stress_score = 0.0

        # Check volatility indicators
        volatility_cols = [col for col in features.columns if 'volatility' in col.lower() or 'vix' in col.lower()]
        if volatility_cols:
            vol_data = features[volatility_cols].fillna(0).mean(axis=1)
            avg_vol = np.mean(vol_data)
            if avg_vol > self.baseline_metrics['volatility_threshold']:
                stress_score += 0.4

        # Check correlation breakdown
        if self._detect_correlation_breakdown(features):
            stress_score += 0.3

        # Check extreme price movements
        price_cols = [col for col in features.columns if any(price in col.lower()
                     for price in ['return', 'change', 'momentum'])]
        if price_cols:
            price_data = features[price_cols].fillna(0).mean(axis=1)
            extreme_moves = np.sum(np.abs(price_data) > 2.0) / len(price_data)
            stress_score += min(0.3, extreme_moves)

        return float(min(1.0, stress_score))

    def _detect_correlation_breakdown(self, features: pd.DataFrame) -> bool:
        """Detect correlation breakdown between assets."""
        # Check cross-asset correlations
        cross_asset_cols = [col for col in features.columns if any(asset in col.lower()
                           for asset in ['dxy', 'spy', 'vix', 'tlt', 'gold'])]

        if len(cross_asset_cols) >= 2:
            cross_asset_data = features[cross_asset_cols].fillna(0)
            if len(cross_asset_data) > 10:
                corr_matrix = cross_asset_data.corr()
                avg_correlation = np.mean(np.abs(corr_matrix.values[np.triu_indices_from(corr_matrix.values, k=1)]))

                return avg_correlation < self.baseline_metrics['correlation_threshold']

        return False  # No breakdown detected

    def _calculate_stability_score(self, features: pd.DataFrame, other_outputs: List[SpecializedOutput] = None) -> float:
        """Calculate overall system stability score."""
        stability_score = 1.0

        # Factor in system health
        system_health = self._assess_system_health(features, other_outputs)
        stability_score *= system_health

        # Factor in market stress (inverse relationship)
        market_stress = self._detect_market_stress(features)
        stability_score *= (1.0 - market_stress * 0.5)

        # Factor in correlation stability
        if self._detect_correlation_breakdown(features):
            stability_score *= 0.7  # Reduce stability if correlations break down

        return float(max(0.0, min(1.0, stability_score)))

    def _calculate_stability_confidence(self, outputs: Dict[str, Any], features: pd.DataFrame) -> float:
        """Calculate overall stability monitoring confidence."""
        stability_score = outputs.get('overall_stability_score', 0.5)
        system_health = outputs.get('system_health_score', 0.5)

        # Higher confidence when system is stable and healthy
        confidence = (stability_score * 0.6 + system_health * 0.4)
        return float(min(1.0, max(0.0, confidence)))

    def _create_fallback_stability_output(self) -> SpecializedOutput:
        """Create fallback output when stability monitoring fails."""
        return SpecializedOutput(
            model_role=self.role,
            model_name="linear",
            outputs={
                'baseline_signal': "HOLD",
                'system_health_score': 0.5,
                'market_stress_indicator': 0.5,
                'correlation_breakdown_alert': False,
                'overall_stability_score': 0.5
            },
            confidence=0.5,
            timestamp=datetime.now(),
            processing_time=0.0
        )


class SpecializedEnsembleOrchestrator(LoggerMixin):
    """
    Main orchestrator for the specialized ensemble architecture.

    Implements the 4-level hierarchical decision process:
    - Level 1: Linear safety check (system health validation)
    - Level 2: CatBoost market context (regime suitability)
    - Level 3: LightGBM signal quality (entry/exit confidence)
    - Level 4: XGBoost risk-adjusted final decision
    """

    def __init__(self, models: Dict[str, Any], config: Dict[str, Any]):
        """
        Initialize Specialized Ensemble Orchestrator.

        Args:
            models: Dictionary of trained models {model_name: model_instance}
            config: Ensemble configuration
        """
        self.config = config

        # Initialize specialized model components
        self.signal_generator = LightGBMSignalGenerator(
            models.get('lightgbm'), config.get('lightgbm_config', {})
        ) if 'lightgbm' in models else None

        self.regime_analyst = CatBoostMarketRegimeAnalyst(
            models.get('catboost'), config.get('catboost_config', {})
        ) if 'catboost' in models else None

        self.risk_manager = XGBoostRiskManager(
            models.get('xgboost'), config.get('xgboost_config', {})
        ) if 'xgboost' in models else None

        self.stability_monitor = LinearStabilityMonitor(
            models.get('linear'), config.get('linear_config', {})
        ) if 'linear' in models else None

        # Decision thresholds
        self.decision_thresholds = {
            'health_threshold': 0.3,        # Minimum health for trading
            'consensus_threshold': 0.6,     # Minimum model agreement
            'confidence_threshold': 0.5,    # Minimum signal confidence
            'stability_threshold': 0.4      # Minimum stability score
        }

        # Performance tracking
        self.decision_history = []
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'paused_decisions': 0
        }

        self.logger.info("Specialized Ensemble Orchestrator initialized")

    def predict(self, features: pd.DataFrame):
        """
        Compatibility method for live trading system.

        This method provides the interface expected by LiveModelEngine while
        leveraging the specialized ensemble architecture.

        Args:
            features: Market features for prediction

        Returns:
            Prediction result compatible with existing live trading system
        """
        try:
            # Step 1: Gather specialized outputs first
            specialized_outputs = self._gather_specialized_outputs(features)

            # Step 2: Use the specialized ensemble decision-making process
            decision = self._apply_hierarchical_decision_process(specialized_outputs, features)

            # Step 3: Convert EnsembleDecision to format expected by LiveModelEngine
            # Pass specialized_outputs so we can extract actual model values
            prediction_result = self._convert_decision_to_prediction(decision, features, specialized_outputs)

            return prediction_result

        except Exception as e:
            self.logger.error(f"Prediction failed in specialized ensemble: {str(e)}")
            # Return a fallback prediction result
            return self._create_fallback_prediction()

    def make_trading_decision(self, features: pd.DataFrame) -> EnsembleDecision:
        """
        Make comprehensive trading decision using hierarchical process.

        Args:
            features: Market features for decision making

        Returns:
            EnsembleDecision with complete trading parameters
        """
        start_time = datetime.now()

        try:
            # Step 1: Gather specialized outputs
            specialized_outputs = self._gather_specialized_outputs(features)

            # Step 2: Apply hierarchical decision process
            decision = self._apply_hierarchical_decision_process(specialized_outputs, features)

            # Step 3: Update performance tracking
            self._update_performance_tracking(decision)

            # Step 4: Log decision
            self._log_decision(decision, specialized_outputs)

            return decision

        except Exception as e:
            self.logger.error(f"Trading decision failed: {str(e)}")
            return self._create_emergency_decision()

    def _gather_specialized_outputs(self, features: pd.DataFrame) -> Dict[str, SpecializedOutput]:
        """Gather outputs from all specialized models."""
        outputs = {}

        # Signal Generator (LightGBM)
        if self.signal_generator:
            try:
                outputs['signal'] = self.signal_generator.generate_signals(features)
            except Exception as e:
                self.logger.warning(f"Signal generation failed: {str(e)}")
                outputs['signal'] = self.signal_generator._create_fallback_signal_output()

        # Market Regime Analyst (CatBoost)
        if self.regime_analyst:
            try:
                outputs['regime'] = self.regime_analyst.analyze_market_regime(features)
            except Exception as e:
                self.logger.warning(f"Regime analysis failed: {str(e)}")
                outputs['regime'] = self.regime_analyst._create_fallback_regime_output()

        # Risk Manager (XGBoost)
        if self.risk_manager:
            try:
                signal_info = outputs.get('signal', {}).outputs if 'signal' in outputs else None
                outputs['risk'] = self.risk_manager.assess_risk(features, signal_info)
            except Exception as e:
                self.logger.warning(f"Risk assessment failed: {str(e)}")
                outputs['risk'] = self.risk_manager._create_fallback_risk_output()

        # Stability Monitor (Linear)
        if self.stability_monitor:
            try:
                other_outputs = [output for key, output in outputs.items() if key != 'stability']
                outputs['stability'] = self.stability_monitor.monitor_stability(features, other_outputs)
            except Exception as e:
                self.logger.warning(f"Stability monitoring failed: {str(e)}")
                outputs['stability'] = self.stability_monitor._create_fallback_stability_output()

        return outputs

    def _apply_hierarchical_decision_process(self, outputs: Dict[str, SpecializedOutput],
                                           features: pd.DataFrame) -> EnsembleDecision:
        """Apply the 4-level hierarchical decision process."""

        # Level 1: Safety Check (Linear Model)
        safety_check = self._level_1_safety_check(outputs.get('stability'))
        if not safety_check['passed']:
            return self._create_pause_decision(safety_check['reason'])

        # Level 2: Market Context (CatBoost)
        context_check = self._level_2_context_check(outputs.get('regime'))

        # Level 3: Signal Quality (LightGBM)
        signal_check = self._level_3_signal_check(outputs.get('signal'), outputs.get('stability'))
        if not signal_check['passed']:
            return self._create_no_trade_decision(signal_check['reason'])

        # Level 4: Final Decision (XGBoost + Integration)
        final_decision = self._level_4_final_decision(outputs, context_check, signal_check)

        return final_decision

    def _level_1_safety_check(self, stability_output: SpecializedOutput) -> Dict[str, Any]:
        """Level 1: System health and safety validation."""
        if not stability_output:
            return {'passed': False, 'reason': 'No stability monitoring available'}

        health_score = stability_output.outputs.get('system_health_score', 0.0)
        stability_score = stability_output.outputs.get('overall_stability_score', 0.0)

        # Check health threshold
        if health_score < self.decision_thresholds['health_threshold']:
            return {'passed': False, 'reason': f'System health too low: {health_score:.3f}'}

        # Check stability threshold
        if stability_score < self.decision_thresholds['stability_threshold']:
            return {'passed': False, 'reason': f'System stability too low: {stability_score:.3f}'}

        # Check for correlation breakdown
        if stability_output.outputs.get('correlation_breakdown_alert', False):
            return {'passed': False, 'reason': 'Correlation breakdown detected'}

        return {'passed': True, 'health_score': health_score, 'stability_score': stability_score}

    def _level_2_context_check(self, regime_output: SpecializedOutput) -> Dict[str, Any]:
        """Level 2: Market context and regime suitability."""
        if not regime_output:
            return {'context_quality': 0.5, 'size_adjustment': 1.0, 'timing_delay': 3}

        regime = regime_output.outputs.get('market_regime', 'RANGING')
        session_favorability = regime_output.outputs.get('session_favorability', 'FAIR')
        volatility_state = regime_output.outputs.get('volatility_state', 'NORMAL')

        # Calculate context quality
        regime_quality = {'TRENDING': 0.9, 'RANGING': 0.7, 'VOLATILE': 0.5, 'QUIET': 0.6}.get(regime, 0.6)
        session_quality = {'EXCELLENT': 1.0, 'GOOD': 0.8, 'FAIR': 0.6, 'POOR': 0.4}.get(session_favorability, 0.6)
        vol_quality = {'LOW': 0.8, 'NORMAL': 1.0, 'HIGH': 0.6, 'EXTREME': 0.3}.get(volatility_state, 0.7)

        context_quality = (regime_quality * 0.4 + session_quality * 0.4 + vol_quality * 0.2)

        # Determine size adjustment
        size_adjustment = 1.0
        if volatility_state in ['HIGH', 'EXTREME']:
            size_adjustment *= 0.5  # Reduce size in high volatility
        if session_favorability == 'POOR':
            size_adjustment *= 0.7  # Reduce size in poor sessions

        # Determine timing delay
        timing_delay = regime_output.outputs.get('optimal_entry_delay', 3)

        return {
            'context_quality': context_quality,
            'size_adjustment': size_adjustment,
            'timing_delay': timing_delay,
            'regime': regime,
            'session': session_favorability,
            'volatility': volatility_state
        }

    def _level_3_signal_check(self, signal_output: SpecializedOutput,
                             stability_output: SpecializedOutput) -> Dict[str, Any]:
        """Level 3: Signal quality and confidence validation."""
        if not signal_output:
            return {'passed': False, 'reason': 'No signal available'}

        signal_confidence = signal_output.outputs.get('confidence_score', 0.0)
        signal_strength = signal_output.outputs.get('signal_strength', 0.0)
        direction = signal_output.outputs.get('direction_classification', 'HOLD')

        # Debug logging for signal analysis
        self.logger.info(f"🔍 Signal Check: confidence={signal_confidence:.3f} (threshold={self.decision_thresholds['confidence_threshold']:.3f}), "
                        f"strength={signal_strength:.3f}, direction={direction}")

        # Check signal confidence threshold
        if signal_confidence < self.decision_thresholds['confidence_threshold']:
            return {'passed': False, 'reason': f'Signal confidence too low: {signal_confidence:.3f} < {self.decision_thresholds["confidence_threshold"]:.3f}'}

        # Check for HOLD signal
        if direction == 'HOLD':
            return {'passed': False, 'reason': 'Signal indicates HOLD'}

        # Check baseline agreement if stability monitor available
        if stability_output:
            baseline_signal = stability_output.outputs.get('baseline_signal', 'HOLD')
            if baseline_signal == 'HOLD' and direction in ['STRONG_BUY', 'STRONG_SELL']:
                return {'passed': False, 'reason': 'Strong signal conflicts with baseline HOLD'}

        return {
            'passed': True,
            'signal_confidence': signal_confidence,
            'signal_strength': signal_strength,
            'direction': direction,
            'expected_pips': signal_output.outputs.get('expected_pips', 0.0),
            'persistence': signal_output.outputs.get('signal_persistence', 1)
        }

    def _level_4_final_decision(self, outputs: Dict[str, SpecializedOutput],
                               context_check: Dict[str, Any],
                               signal_check: Dict[str, Any]) -> EnsembleDecision:
        """Level 4: Final risk-adjusted decision integration."""

        # Get risk assessment
        risk_output = outputs.get('risk')
        if not risk_output:
            return self._create_no_trade_decision('No risk assessment available')

        # Extract components
        signal_direction = signal_check['direction']
        base_position_size = risk_output.outputs.get('position_size_multiplier', 0.5)
        stop_loss_pips = risk_output.outputs.get('stop_loss_pips', 80.0)  # XAUUSD appropriate default
        take_profit_levels = risk_output.outputs.get('take_profit_levels', [60.0, 120.0, 200.0])  # XAUUSD appropriate defaults

        # Apply context adjustments
        adjusted_position_size = base_position_size * context_check['size_adjustment']
        entry_timing = context_check['timing_delay']

        # Calculate confidence metrics
        overall_confidence = self._calculate_overall_confidence(outputs)
        signal_quality = signal_check['signal_confidence']
        market_suitability = context_check['context_quality']
        risk_assessment = risk_output.confidence
        system_health = outputs.get('stability', type('obj', (object,), {'confidence': 0.5})).confidence

        # Determine primary driver and weights
        primary_driver, decision_weights = self._determine_decision_attribution(outputs)

        # Create final decision
        return EnsembleDecision(
            trade_direction=signal_direction,
            position_size=float(max(0.1, min(2.0, adjusted_position_size))),
            entry_timing=int(entry_timing),
            stop_loss_pips=float(stop_loss_pips),
            take_profit_levels=take_profit_levels,
            overall_confidence=overall_confidence,
            signal_quality=signal_quality,
            market_suitability=market_suitability,
            risk_assessment=risk_assessment,
            system_health=system_health,
            primary_driver=primary_driver,
            contributing_models=[output.model_name for output in outputs.values()],
            decision_weights=decision_weights,
            timestamp=datetime.now(),
            processing_time=sum(output.processing_time for output in outputs.values())
        )

    def _calculate_overall_confidence(self, outputs: Dict[str, SpecializedOutput]) -> float:
        """Calculate geometric mean of all model confidences."""
        confidences = [output.confidence for output in outputs.values() if output.confidence > 0]

        if not confidences:
            return 0.0

        # Geometric mean for conservative confidence estimation
        geometric_mean = np.prod(confidences) ** (1.0 / len(confidences))
        return float(min(1.0, max(0.0, geometric_mean)))

    def _determine_decision_attribution(self, outputs: Dict[str, SpecializedOutput]) -> Tuple[str, Dict[str, float]]:
        """Determine primary decision driver and model weights."""
        # Calculate weighted contributions
        weights = {
            'lightgbm': 0.40,
            'catboost': 0.25,
            'xgboost': 0.25,
            'linear': 0.10
        }

        # Adjust weights based on confidence
        adjusted_weights = {}
        total_weight = 0.0

        for output_type, output in outputs.items():
            model_name = output.model_name
            base_weight = weights.get(model_name, 0.0)
            confidence_adj = output.confidence
            adjusted_weight = base_weight * (0.5 + 0.5 * confidence_adj)
            adjusted_weights[model_name] = adjusted_weight
            total_weight += adjusted_weight

        # Normalize weights
        if total_weight > 0:
            adjusted_weights = {k: v / total_weight for k, v in adjusted_weights.items()}

        # Determine primary driver (highest weighted contribution)
        primary_driver = max(adjusted_weights.items(), key=lambda x: x[1])[0] if adjusted_weights else 'ensemble'

        return primary_driver, adjusted_weights

    def _create_pause_decision(self, reason: str) -> EnsembleDecision:
        """Create decision to pause trading."""
        return EnsembleDecision(
            trade_direction="HOLD",
            position_size=0.0,
            entry_timing=0,
            stop_loss_pips=0.0,
            take_profit_levels=[],
            overall_confidence=0.0,
            signal_quality=0.0,
            market_suitability=0.0,
            risk_assessment=0.0,
            system_health=0.0,
            primary_driver="system_pause",
            contributing_models=[],
            decision_weights={},
            timestamp=datetime.now(),
            processing_time=0.0
        )

    def _create_no_trade_decision(self, reason: str) -> EnsembleDecision:
        """Create decision for no trade."""
        return EnsembleDecision(
            trade_direction="HOLD",
            position_size=0.0,
            entry_timing=0,
            stop_loss_pips=0.0,
            take_profit_levels=[],
            overall_confidence=0.3,
            signal_quality=0.0,
            market_suitability=0.5,
            risk_assessment=0.5,
            system_health=0.7,
            primary_driver="no_trade",
            contributing_models=[],
            decision_weights={},
            timestamp=datetime.now(),
            processing_time=0.0
        )

    def _create_emergency_decision(self) -> EnsembleDecision:
        """Create emergency fallback decision."""
        return EnsembleDecision(
            trade_direction="HOLD",
            position_size=0.0,
            entry_timing=0,
            stop_loss_pips=0.0,
            take_profit_levels=[],
            overall_confidence=0.0,
            signal_quality=0.0,
            market_suitability=0.0,
            risk_assessment=0.0,
            system_health=0.0,
            primary_driver="emergency",
            contributing_models=[],
            decision_weights={},
            timestamp=datetime.now(),
            processing_time=0.0
        )

    def _update_performance_tracking(self, decision: EnsembleDecision):
        """Update performance tracking metrics."""
        self.performance_metrics['total_decisions'] += 1

        if decision.primary_driver == "system_pause":
            self.performance_metrics['paused_decisions'] += 1

        # Store decision in history (keep last 1000)
        self.decision_history.append(decision)
        if len(self.decision_history) > 1000:
            self.decision_history.pop(0)

    def _log_decision(self, decision: EnsembleDecision, outputs: Dict[str, SpecializedOutput]):
        """Log decision details."""
        # Log individual model outputs for debugging
        model_details = []
        for output_type, output in outputs.items():
            model_name = output.model_name
            confidence = output.confidence
            key_outputs = {}

            # Extract key prediction values
            if 'signal_strength' in output.outputs:
                key_outputs['signal_strength'] = f"{output.outputs['signal_strength']:.3f}"
            if 'confidence_score' in output.outputs:
                key_outputs['confidence_score'] = f"{output.outputs['confidence_score']:.3f}"
            if 'direction_classification' in output.outputs:
                key_outputs['direction'] = output.outputs['direction_classification']
            if 'signal_probability' in output.outputs:
                key_outputs['signal_prob'] = f"{output.outputs['signal_probability']:.3f}"

            model_details.append(f"{model_name}(conf={confidence:.3f}, {key_outputs})")

        self.logger.info(f"🔍 Model Details: {' | '.join(model_details)}")
        self.logger.info(f"Trading Decision: {decision.trade_direction} | "
                        f"Size: {decision.position_size:.2f} | "
                        f"Confidence: {decision.overall_confidence:.3f} | "
                        f"Primary: {decision.primary_driver}")

        # Log model contributions
        for model_name, weight in decision.decision_weights.items():
            self.logger.debug(f"  {model_name}: {weight:.3f}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        return {
            'total_decisions': self.performance_metrics['total_decisions'],
            'successful_trades': self.performance_metrics['successful_trades'],
            'failed_trades': self.performance_metrics['failed_trades'],
            'paused_decisions': self.performance_metrics['paused_decisions'],
            'recent_decisions': len(self.decision_history),
            'avg_confidence': np.mean([d.overall_confidence for d in self.decision_history[-100:]]) if self.decision_history else 0.0
        }

    def _convert_decision_to_prediction(self, decision: EnsembleDecision, features: pd.DataFrame, specialized_outputs: Dict[str, SpecializedOutput] = None):
        """
        Convert EnsembleDecision to format expected by LiveModelEngine.

        This bridges the gap between the specialized ensemble architecture
        and the existing live trading system interface.
        """
        try:
            # 🔧 FIX: Extract ACTUAL model outputs from specialized_outputs parameter
            self.logger.info("🔧 Converting decision to prediction - extracting actual model outputs")

            # Extract actual LightGBM signal outputs
            if specialized_outputs and 'signal' in specialized_outputs:
                signal_outputs = specialized_outputs['signal']
                if hasattr(signal_outputs, 'outputs'):
                    lightgbm_outputs = signal_outputs.outputs
                    actual_signal_probability = lightgbm_outputs.get('confidence_score', 0.5)
                    self.logger.info(f"🔧 Using actual LightGBM signal probability: {actual_signal_probability}")
                else:
                    actual_signal_probability = 0.5
                    self.logger.warning("🔧 LightGBM signal outputs have no 'outputs' attribute")
            else:
                actual_signal_probability = 0.5
                self.logger.warning("🔧 No LightGBM signal outputs found, using fallback")

            # Extract actual XGBoost risk outputs
            if specialized_outputs and 'risk' in specialized_outputs:
                risk_outputs = specialized_outputs['risk']
                if hasattr(risk_outputs, 'outputs'):
                    xgboost_outputs = risk_outputs.outputs
                    actual_sl_pips = xgboost_outputs.get('stop_loss_pips', decision.stop_loss_pips)
                    actual_tp_levels = xgboost_outputs.get('take_profit_levels', decision.take_profit_levels)
                    self.logger.info(f"🔧 Using actual XGBoost SL: {actual_sl_pips}, TP: {actual_tp_levels}")
                else:
                    actual_sl_pips = decision.stop_loss_pips
                    actual_tp_levels = decision.take_profit_levels
                    self.logger.warning("🔧 XGBoost risk outputs have no 'outputs' attribute")
            else:
                actual_sl_pips = decision.stop_loss_pips
                actual_tp_levels = decision.take_profit_levels
                self.logger.warning("🔧 No XGBoost risk outputs found, using decision values")

            # Create prediction dictionary with ACTUAL model outputs
            predictions = {
                'signal_probability': actual_signal_probability,
                'tp1_distance': actual_tp_levels[0] if actual_tp_levels and len(actual_tp_levels) > 0 else 0.0,
                'tp2_distance': actual_tp_levels[1] if actual_tp_levels and len(actual_tp_levels) > 1 else 0.0,
                'sl_distance': actual_sl_pips,
                'hold_time_estimate': self._extract_hold_time_estimate_from_outputs(specialized_outputs),
                'volatility_adjustment': self._extract_volatility_adjustment_from_outputs(specialized_outputs),
                'market_regime': self._encode_market_regime(decision),
                'position_size_category': decision.position_size
            }

            self.logger.info(f"🔧 Final predictions with actual model outputs: {predictions}")



            # Also extract individual model outputs for detailed logging
            individual_predictions = {}

            # Extract LightGBM outputs
            if specialized_outputs and 'signal' in specialized_outputs:
                signal_output = specialized_outputs['signal']
                individual_predictions['lightgbm'] = {
                    'confidence': signal_output.confidence,
                    'outputs': signal_output.outputs if hasattr(signal_output, 'outputs') else {},
                    'processing_time': signal_output.processing_time if hasattr(signal_output, 'processing_time') else 0.0
                }

            # Extract CatBoost outputs
            if specialized_outputs and 'regime' in specialized_outputs:
                regime_output = specialized_outputs['regime']
                individual_predictions['catboost'] = {
                    'confidence': regime_output.confidence,
                    'outputs': regime_output.outputs if hasattr(regime_output, 'outputs') else {},
                    'processing_time': regime_output.processing_time if hasattr(regime_output, 'processing_time') else 0.0
                }

            # Extract XGBoost outputs
            if specialized_outputs and 'risk' in specialized_outputs:
                risk_output = specialized_outputs['risk']
                individual_predictions['xgboost'] = {
                    'confidence': risk_output.confidence,
                    'outputs': risk_output.outputs if hasattr(risk_output, 'outputs') else {},
                    'processing_time': risk_output.processing_time if hasattr(risk_output, 'processing_time') else 0.0
                }

            # Extract Linear outputs
            if specialized_outputs and 'stability' in specialized_outputs:
                stability_output = specialized_outputs['stability']
                individual_predictions['linear'] = {
                    'confidence': stability_output.confidence,
                    'outputs': stability_output.outputs if hasattr(stability_output, 'outputs') else {},
                    'processing_time': stability_output.processing_time if hasattr(stability_output, 'processing_time') else 0.0
                }

            # Create a proper PredictionResult object for compatibility
            class PredictionResult:
                def __init__(self, predictions_dict, individual_preds, decision_obj, ensemble_outs):
                    # Convert to numpy array format expected by LiveModelEngine
                    self.values = np.array([[
                        predictions_dict['signal_probability'],
                        predictions_dict['tp1_distance'],
                        predictions_dict['tp2_distance'],
                        predictions_dict['sl_distance'],
                        predictions_dict['hold_time_estimate'],
                        predictions_dict['volatility_adjustment'],
                        predictions_dict['market_regime'],
                        predictions_dict['position_size_category']
                    ]])

                    # Store additional metadata
                    self.decision = decision_obj
                    self.predictions_dict = predictions_dict
                    self.individual_predictions = individual_preds
                    self.ensemble_outputs = ensemble_outs
                    self.success = True

            return PredictionResult(predictions, individual_predictions, decision, specialized_outputs or {})

        except Exception as e:
            self.logger.error(f"Failed to convert decision to prediction: {str(e)}")
            return self._create_fallback_prediction()

    def _extract_hold_time_estimate_from_outputs(self, specialized_outputs: Dict[str, SpecializedOutput]) -> float:
        """Extract hold time estimate from LightGBM signal outputs."""
        try:
            if specialized_outputs and 'signal' in specialized_outputs:
                signal_outputs = specialized_outputs['signal']
                if hasattr(signal_outputs, 'outputs'):
                    return signal_outputs.outputs.get('signal_persistence', 0.0)
            return 0.0
        except:
            return 0.0

    def _extract_volatility_adjustment_from_outputs(self, specialized_outputs: Dict[str, SpecializedOutput]) -> float:
        """Extract volatility adjustment from model outputs."""
        try:
            # Try to get from CatBoost regime analysis
            if specialized_outputs and 'regime' in specialized_outputs:
                regime_outputs = specialized_outputs['regime']
                if hasattr(regime_outputs, 'outputs'):
                    volatility_state = regime_outputs.outputs.get('volatility_state', 'NORMAL')
                    volatility_mapping = {'LOW': 0.5, 'NORMAL': 1.0, 'HIGH': 1.5, 'EXTREME': 2.0}
                    return volatility_mapping.get(volatility_state, 1.0)
            return 1.0
        except:
            return 1.0

    def _encode_market_regime(self, decision: EnsembleDecision) -> float:
        """Encode market regime information as float."""
        # Use overall confidence as a proxy for market regime strength
        return decision.overall_confidence

    def _create_fallback_prediction(self):
        """Create fallback prediction when conversion fails."""
        self.logger.warning("🔧 Using fallback prediction due to conversion failure")

        class FallbackPredictionResult:
            def __init__(self):
                # Neutral prediction values in expected format
                self.values = np.array([[0.5, 0.0, 0.0, 0.0, 0.0, 1.0, 0.5, 0.0]])
                self.decision = None
                self.predictions_dict = {
                    'signal_probability': 0.5,
                    'tp1_distance': 0.0,
                    'tp2_distance': 0.0,
                    'sl_distance': 0.0,
                    'hold_time_estimate': 0.0,
                    'volatility_adjustment': 1.0,
                    'market_regime': 0.5,
                    'position_size_category': 0.0
                }
                self.individual_predictions = {
                    'lightgbm': {'confidence': 0.0, 'outputs': {}, 'processing_time': 0.0},
                    'catboost': {'confidence': 0.0, 'outputs': {}, 'processing_time': 0.0},
                    'xgboost': {'confidence': 0.0, 'outputs': {}, 'processing_time': 0.0},
                    'linear': {'confidence': 0.0, 'outputs': {}, 'processing_time': 0.0}
                }
                self.ensemble_outputs = {}
                self.success = False

        return FallbackPredictionResult()
