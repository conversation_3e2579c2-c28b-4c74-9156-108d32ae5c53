{% extends "base.html" %}

{% block title %}{{ title }} - Dashboard{% endblock %}

{% block content %}
<!-- <PERSON> Header -->
<div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-3xl font-bold text-nasa-light mb-2">
                <i class="fas fa-chart-line text-nasa-accent mr-3"></i>
                XAUUSD AI Trading Dashboard
            </h1>
            <p class="text-nasa-gray">Real-time algorithmic trading system monitoring</p>
        </div>
        <div class="flex items-center mt-4 sm:mt-0 space-x-4">
            <div class="text-right">
                <p class="text-sm text-nasa-gray">Last Update</p>
                <p class="font-semibold text-nasa-light" id="last-update-time">--:--:--</p>
            </div>
            <span id="connection-status" class="connection-status connecting">Connecting...</span>
        </div>
    </div>
</div>

<!-- KPI Cards Section -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Return Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-chart-line text-nasa-accent text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Total Return</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="total-return">0.0%</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                    <i class="fas fa-arrow-up mr-1"></i>
                    0.0%
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-calendar mr-2"></i>
            Forward Test Performance
        </div>
    </div>

    <!-- Sharpe Ratio Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-chart-bar text-blue-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Sharpe Ratio</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="sharpe-ratio">2.34</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-500/20 text-blue-400">
                    Excellent
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-target mr-2"></i>
            Risk-Adjusted Returns
        </div>
    </div>

    <!-- Maximum Drawdown Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-nasa-red/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-arrow-down text-nasa-red text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Max Drawdown</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="max-drawdown">0.0%</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-red/20 text-nasa-red">
                    12d recovery
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-shield-alt mr-2"></i>
            Risk Management
        </div>
    </div>

    <!-- Win Rate Card -->
    <div class="kpi-card">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center mr-4">
                    <i class="fas fa-percentage text-purple-400 text-xl"></i>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-nasa-gray">Win Rate</h3>
                    <p class="text-2xl font-bold text-nasa-light" id="win-rate">0.0%</p>
                </div>
            </div>
            <div class="text-right">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-500/20 text-purple-400">
                    247 trades
                </span>
            </div>
        </div>
        <div class="flex items-center text-sm text-nasa-gray">
            <i class="fas fa-bullseye mr-2"></i>
            Success Rate
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- PnL Curve Chart -->
    <div class="lg:col-span-2 chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-chart-line text-nasa-accent mr-2"></i>
                Portfolio P&L Curve
            </h3>
            <div class="flex space-x-2">
                <button class="px-3 py-1 text-xs font-medium rounded-lg bg-nasa-accent/20 text-nasa-accent">1D</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">7D</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">30D</button>
                <button class="px-3 py-1 text-xs font-medium rounded-lg text-nasa-gray hover:bg-nasa-gray/10">ALL</button>
            </div>
        </div>
        <div class="h-80">
            <canvas id="pnl-chart"></canvas>
        </div>
    </div>

    <!-- Drawdown History Chart -->
    <div class="chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-arrow-down text-nasa-red mr-2"></i>
                Drawdown History
            </h3>
        </div>
        <div class="h-80">
            <canvas id="drawdown-chart"></canvas>
        </div>
    </div>
</div>

<!-- Second Row of Charts -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Return Distribution -->
    <div class="chart-container">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-chart-bar text-blue-400 mr-2"></i>
                Return Distribution
            </h3>
        </div>
        <div class="h-80">
            <canvas id="distribution-chart"></canvas>
        </div>
    </div>

    <!-- AI Signals Panel -->
    <div class="lg:col-span-2 trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-brain text-nasa-accent mr-2"></i>
                AI Trading Signals
            </h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                <i class="fas fa-circle animate-pulse mr-1"></i>
                Live
            </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Current Signal -->
            <div class="bg-nasa-dark/50 rounded-xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-sm font-medium text-nasa-gray">Current Signal</h4>
                    <span class="text-xs text-nasa-gray" id="signal-timestamp">2 min ago</span>
                </div>
                <div class="flex items-center space-x-3">
                    <div class="w-3 h-3 bg-nasa-accent rounded-full animate-pulse"></div>
                    <div>
                        <p class="text-lg font-semibold text-nasa-accent" id="current-signal">BUY</p>
                        <p class="text-sm text-nasa-gray">Confidence: <span id="signal-confidence" class="text-nasa-light font-medium">87%</span></p>
                    </div>
                </div>
            </div>

            <!-- Signal Strength -->
            <div class="bg-nasa-dark/50 rounded-xl p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="text-sm font-medium text-nasa-gray">Signal Strength</h4>
                    <span class="text-xs text-nasa-accent font-medium">Strong</span>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-nasa-gray">LightGBM</span>
                        <span class="text-nasa-light">92%</span>
                    </div>
                    <div class="w-full bg-nasa-dark rounded-full h-2">
                        <div class="bg-nasa-accent h-2 rounded-full" style="width: 92%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- System Health -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-heartbeat text-nasa-accent mr-2"></i>
                System Health
            </h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-nasa-accent/20 text-nasa-accent">
                Healthy
            </span>
        </div>

        <div class="grid grid-cols-3 gap-4 mb-6">
            <div class="text-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-check-circle text-nasa-accent"></i>
                </div>
                <p class="text-sm font-medium text-nasa-light">MT5</p>
                <p class="text-xs text-nasa-gray">Connected</p>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-brain text-nasa-accent"></i>
                </div>
                <p class="text-sm font-medium text-nasa-light">Models</p>
                <p class="text-xs text-nasa-gray">4/4 Active</p>
            </div>
            <div class="text-center">
                <div class="w-12 h-12 bg-nasa-accent/20 rounded-xl flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-database text-nasa-accent"></i>
                </div>
                <p class="text-sm font-medium text-nasa-light">Data Feed</p>
                <p class="text-xs text-nasa-gray">Real-time</p>
            </div>
        </div>

        <div class="space-y-3">
            <div class="flex justify-between items-center">
                <span class="text-sm text-nasa-gray">System Uptime</span>
                <span class="text-sm font-medium text-nasa-light" id="system-uptime">24h 15m</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-nasa-gray">Last Signal</span>
                <span class="text-sm font-medium text-nasa-light" id="last-signal">2 min ago</span>
            </div>
            <div class="flex justify-between items-center">
                <span class="text-sm text-nasa-gray">Memory Usage</span>
                <span class="text-sm font-medium text-nasa-light">45.2%</span>
            </div>
        </div>
    </div>

    <!-- Model Performance -->
    <div class="trading-card">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-nasa-light">
                <i class="fas fa-brain text-nasa-accent mr-2"></i>
                Model Performance
            </h3>
            <span class="text-xs text-nasa-gray">Last 24h</span>
        </div>

        <div class="space-y-4">
            <div class="bg-nasa-dark/50 rounded-xl p-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-nasa-light">LightGBM</span>
                    <span class="text-sm text-nasa-accent">92% Accuracy</span>
                </div>
                <div class="w-full bg-nasa-dark rounded-full h-2">
                    <div class="bg-nasa-accent h-2 rounded-full" style="width: 92%"></div>
                </div>
                <p class="text-xs text-nasa-gray mt-1">Signal Generator (40% weight)</p>
            </div>

            <div class="bg-nasa-dark/50 rounded-xl p-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-nasa-light">CatBoost</span>
                    <span class="text-sm text-blue-400">88% Accuracy</span>
                </div>
                <div class="w-full bg-nasa-dark rounded-full h-2">
                    <div class="bg-blue-400 h-2 rounded-full" style="width: 88%"></div>
                </div>
                <p class="text-xs text-nasa-gray mt-1">Market Regime (25% weight)</p>
            </div>

            <div class="bg-nasa-dark/50 rounded-xl p-4">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-nasa-light">XGBoost</span>
                    <span class="text-sm text-purple-400">85% Accuracy</span>
                </div>
                <div class="w-full bg-nasa-dark rounded-full h-2">
                    <div class="bg-purple-400 h-2 rounded-full" style="width: 85%"></div>
                </div>
                <p class="text-xs text-nasa-gray mt-1">Risk Manager (25% weight)</p>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_scripts %}
<script>
function initializePage() {
    // Initialize charts with dark theme
    initializePnLChart();
    initializeDrawdownChart();
    initializeDistributionChart();

    // Start data updates
    startDataUpdates();
}

function initializePnLChart() {
    const ctx = document.getElementById('pnl-chart').getContext('2d');
    window.pnlChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Portfolio P&L',
                data: [],
                borderColor: '#22C55E',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#F8FAFC'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                }
            }
        }
    });
}

function initializeDrawdownChart() {
    const ctx = document.getElementById('drawdown-chart').getContext('2d');
    window.drawdownChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Drawdown %',
                data: [],
                borderColor: '#EF4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#F8FAFC'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                }
            }
        }
    });
}

function initializeDistributionChart() {
    const ctx = document.getElementById('distribution-chart').getContext('2d');
    window.distributionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Return Distribution',
                data: [],
                backgroundColor: '#22C55E',
                borderColor: '#22C55E',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: {
                        color: '#F8FAFC'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                },
                y: {
                    ticks: {
                        color: '#64748B'
                    },
                    grid: {
                        color: 'rgba(100, 116, 139, 0.1)'
                    }
                }
            }
        }
    });
}

function startDataUpdates() {
    // This will be connected to WebSocket updates
    setInterval(updateDashboardData, 5000);
}

function updateDashboardData() {
    // Fetch latest data via API or WebSocket
    // This is a placeholder - will be implemented with real data
}
</script>
{% endblock %}
