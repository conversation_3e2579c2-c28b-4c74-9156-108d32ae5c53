"""
Real-Time Live Trader Implementation

Main implementation of live trading system with real-time data processing,
model inference, and trade execution coordination.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import time
import threading
from collections import deque

from .base import BaseLiveTrader, LiveTradingResult, LiveTrade, TradeStatus
from .real_time_data import RealTimeDataCollector
from .model_engine import LiveModelEngine
from .trade_execution import TradeExecutionEngine
from .risk_manager import LiveRiskManager
from .hierarchical_decision import HierarchicalDecisionFramework
from .specialized_weights import SpecializedWeightSystem
from .model_driven_execution import ModelDrivenExecutionEngine
from .five_layer_risk import FiveLayerRiskManager
from .data_quality_optimizer import AdvancedDataQualityOptimizer
# ENHANCED: Import risk management factory for clean architecture compliance
from .risk_management_factory import RiskManagementFactory, RiskComponentType

# Import existing system components
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from features.core.factories import FeatureEngineFactory, FeatureType
    from features.config.feature_config import FeatureConfig
    from data_collection.config import Config
    from utils.logging_utils import LoggerMixin
except ImportError as e:
    # Fallback for missing utils
    class LoggerMixin:
        def __init__(self):
            import logging
            self.logger = logging.getLogger(self.__class__.__name__)

    # Try alternative imports
    try:
        from features.core.factories import FeatureEngineFactory, FeatureType
        from features.config.feature_config import FeatureConfig
        from data_collection.config import Config
    except ImportError:
        # Create minimal fallbacks
        class FeatureEngineFactory:
            def __init__(self, *args): pass
            def create_engine(self, feature_type): return None

        class FeatureType:
            PRICE_FEATURES = "price"
            TECHNICAL_INDICATORS = "technical"
            VOLATILITY_FEATURES = "volatility"
            VOLUME_FEATURES = "volume"
            CORRELATION_FEATURES = "correlation"
            REGIME_FEATURES = "regime"
            CONTEXT_FEATURES = "context"
            TEMPORAL_FEATURES = "temporal"

        class Config:
            def __init__(self): pass


class RealTimeLiveTrader(BaseLiveTrader):
    """
    Real-time live trader implementation.
    
    Coordinates all components in a real-time trading loop:
    Data Collection → Feature Engineering → Model Inference → Risk Management → Trade Execution
    """
    
    def __init__(self, config: Dict[str, Any], trader_type: str):
        """
        Initialize real-time live trader.
        
        Args:
            config: Trading configuration
            trader_type: Type of trader
        """
        super().__init__(config, trader_type)
        
        # Trading loop parameters
        self.prediction_interval = config.get('prediction_interval_seconds', 30)
        self.execution_interval = config.get('execution_interval_seconds', 60)
        
        # Component instances
        self.data_collector = None
        self.feature_engine = None
        self.model_engine = None
        self.execution_engine = None
        self.risk_manager = None

        # Specialized ensemble components
        self.hierarchical_decision = None
        self.weight_system = None
        self.execution_optimizer = None
        self.five_layer_risk = None
        
        # Threading control
        self.trading_thread = None
        self.monitoring_thread = None
        self.stop_event = threading.Event()
        
        # Performance tracking
        self.loop_stats = {
            'total_cycles': 0,
            'successful_predictions': 0,
            'failed_predictions': 0,
            'trades_attempted': 0,
            'trades_executed': 0,
            'avg_cycle_time_ms': 0.0,
            'last_cycle_time': None,
            'errors': [],
            'warnings': []
        }
        
        # Feature buffer for real-time processing
        self.feature_buffer = deque(maxlen=100)
        self.last_feature_time = None

        # 🔧 FEATURE CACHING: Cache features within the same trading cycle to avoid duplicate generation
        self._cached_features = None
        self._cached_features_timestamp = None
        self._cached_market_data_hash = None
    
    def start_trading(self) -> LiveTradingResult:
        """Start the real-time trading system."""
        try:
            if self.is_active:
                return LiveTradingResult(
                    success=False,
                    message="Trading system is already active"
                )
            
            self.logger.info("🚀 Starting real-time live trading system...")
            
            # Initialize all components
            init_result = self._initialize_components()
            if not init_result.success:
                return init_result
            
            # Start trading loop
            self.is_active = True
            self.start_time = datetime.now()
            self.stop_event.clear()
            
            # Start main trading thread
            self.trading_thread = threading.Thread(target=self._trading_loop, daemon=True)
            self.trading_thread.start()
            
            # Start monitoring thread
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            self.logger.info("✅ Real-time trading system started successfully")
            
            return LiveTradingResult(
                success=True,
                message="Real-time trading started",
                data={
                    'start_time': self.start_time.isoformat(),
                    'prediction_interval': self.prediction_interval,
                    'execution_interval': self.execution_interval
                }
            )
            
        except Exception as e:
            error_msg = f"Failed to start real-time trading: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def stop_trading(self) -> LiveTradingResult:
        """Stop the real-time trading system."""
        try:
            if not self.is_active:
                return LiveTradingResult(
                    success=True,
                    message="Trading system is not active"
                )
            
            self.logger.info("🛑 Stopping real-time trading system...")
            
            # Signal threads to stop
            self.stop_event.set()
            self.is_active = False
            
            # Wait for threads to finish
            if self.trading_thread and self.trading_thread.is_alive():
                self.trading_thread.join(timeout=10.0)
            
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5.0)
            
            # Disconnect components
            if self.data_collector:
                self.data_collector.disconnect()
            
            if self.execution_engine:
                self.execution_engine.disconnect()
            
            self.logger.info("✅ Real-time trading system stopped")
            
            return LiveTradingResult(
                success=True,
                message="Real-time trading stopped",
                data=self.get_performance_summary()
            )
            
        except Exception as e:
            error_msg = f"Error stopping trading system: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )
    
    def _initialize_components(self) -> LiveTradingResult:
        """Initialize all trading components."""
        try:
            self.logger.info("Initializing trading components...")
            
            # Initialize data collector
            self.data_collector = RealTimeDataCollector(self.config)
            if not self.data_collector.start_streaming():
                raise Exception("Failed to start data streaming")
            
            # Initialize feature engine
            base_config = Config()
            feature_config = FeatureConfig(base_config)
            feature_factory = FeatureEngineFactory(base_config, feature_config)
            
            # Create feature engines - COMPLETE 354-FEATURE SET for specialized ensemble
            # Based on actual training data: train_features_391.csv (354 features)
            # CRITICAL: Must match exact feature set used in model training
            self.feature_engines = {}
            feature_types = [
                # Core price and technical features (240 features)
                FeatureType.PRICE_FEATURES,           # ~128 features (price, returns, volatility)
                FeatureType.TECHNICAL_INDICATORS,     # ~34 features (RSI, MACD, etc.)
                FeatureType.VOLATILITY_FEATURES,      # ~48 features (ATR, Bollinger, etc.)
                FeatureType.VOLUME_FEATURES,          # ~29 features (VWAP, OBV, volume analysis)

                # CRITICAL MISSING FEATURES - Required for 354-feature compatibility (116 features)
                FeatureType.CORRELATION_FEATURES,     # 66 features (DXY, TLT, SPY, VIX, QQQ, IEF correlations)
                FeatureType.REGIME_FEATURES,          # 7 features (regime stability, transition probability)
                FeatureType.CONTEXT_FEATURES,         # 34 features (trend regime + temporal + context)
                FeatureType.TEMPORAL_FEATURES,        # 9 features (additional temporal patterns)
            ]
            
            for feature_type in feature_types:
                try:
                    engine = feature_factory.create_engine(feature_type)
                    self.feature_engines[feature_type] = engine
                except Exception as e:
                    self.logger.warning(f"Failed to create {feature_type} engine: {str(e)}")

            # Connect external data service to correlation feature engine
            self._setup_external_data_service_connection()
            
            # Initialize model engine
            self.model_engine = LiveModelEngine(self.config)
            if not self.model_engine.load_models():
                raise Exception("Failed to load models")
            
            # Initialize risk manager first
            self.risk_manager = LiveRiskManager(self.config)

            # Initialize execution engine with risk manager
            self.execution_engine = TradeExecutionEngine(self.config, self.risk_manager)
            if not self.execution_engine.connect():
                raise Exception("Failed to connect execution engine")

            # ENHANCED: Initialize specialized ensemble components using factory pattern
            self.logger.info("🏗️ Initializing risk management components using factory pattern")

            # Create risk management factory
            self.risk_factory = RiskManagementFactory(self.config)

            # Validate configuration before creating components
            validation_results = self.risk_factory.validate_configuration()
            if not validation_results['valid']:
                error_msg = f"Risk management configuration validation failed: {validation_results['errors']}"
                self.logger.error(error_msg)
                raise Exception(error_msg)

            if validation_results['warnings']:
                for warning in validation_results['warnings']:
                    self.logger.warning(f"⚠️ Risk management warning: {warning}")

            # Create complete risk management system
            try:
                risk_system = self.risk_factory.create_complete_risk_system()

                # Assign components to instance variables for backward compatibility
                self.hierarchical_decision = risk_system['hierarchical_decision']
                self.weight_system = risk_system['weight_system']
                self.execution_optimizer = risk_system['execution_engine']
                self.five_layer_risk = risk_system['five_layer_risk']

                # Store shared risk context reference
                self.shared_risk_context = risk_system.get('shared_risk_context')

                self.logger.info("✅ Risk management system initialized using factory pattern")

            except Exception as e:
                self.logger.error(f"Failed to create risk management system: {e}")
                # Fallback to direct instantiation for backward compatibility
                self.logger.warning("⚠️ Falling back to direct component instantiation")
                self.hierarchical_decision = HierarchicalDecisionFramework(self.config)
                self.weight_system = SpecializedWeightSystem(self.config)
                self.execution_optimizer = ModelDrivenExecutionEngine(self.config)
                self.five_layer_risk = FiveLayerRiskManager(self.config)
                self.shared_risk_context = None

            # Initialize advanced data quality optimizer
            self.data_quality_optimizer = AdvancedDataQualityOptimizer(self.config)

            self.logger.info("✅ All components initialized successfully (including specialized ensemble)")
            
            return LiveTradingResult(
                success=True,
                message="Components initialized"
            )
            
        except Exception as e:
            error_msg = f"Component initialization failed: {str(e)}"
            self.logger.error(error_msg)
            return LiveTradingResult(
                success=False,
                message=error_msg,
                errors=[str(e)]
            )

    def _setup_external_data_service_connection(self):
        """Set up connection between external data service and feature engines."""
        try:
            # Get external data service from factory (if available)
            from live_trading.factories import LiveTradingFactory

            # Create a temporary factory to access the external data service
            # In a real implementation, this would be passed from the orchestrator
            temp_config = type('Config', (), {
                'model_path': self.config.get('model_path', ''),
                'symbol': self.config.get('symbol', 'XAUUSD!'),
                'initial_capital': self.config.get('initial_capital', 400.0),
                'max_risk_per_trade': self.config.get('max_risk_per_trade', 0.02),
                'max_concurrent_trades': self.config.get('max_concurrent_trades', 3),
                'min_signal_confidence': self.config.get('min_signal_confidence', 0.65)
            })()

            factory = LiveTradingFactory(temp_config)
            external_data_service = factory.get_external_data_service()

            if external_data_service is None:
                # Try to create and start the service
                external_data_service = factory.create_external_data_service()
                factory.start_external_data_service()

            # Connect to correlation feature engine
            if FeatureType.CORRELATION_FEATURES in self.feature_engines:
                correlation_engine = self.feature_engines[FeatureType.CORRELATION_FEATURES]
                if hasattr(correlation_engine, 'set_external_data_service'):
                    correlation_engine.set_external_data_service(external_data_service)
                    self.logger.info("✅ Connected external data service to correlation feature engine")
                else:
                    self.logger.warning("Correlation engine does not support external data service")
            else:
                self.logger.warning("Correlation feature engine not found")

        except Exception as e:
            self.logger.warning(f"Failed to setup external data service connection: {str(e)}")
            # This is not critical, system can still work with file-based data

    def _trading_loop(self):
        """Main trading loop."""
        self.logger.info("Trading loop started")
        
        while not self.stop_event.is_set():
            try:
                cycle_start_time = time.time()
                
                # Get latest market data
                latest_data = self.data_collector.get_latest_data(100)

                if latest_data.empty:
                    self.logger.warning("No market data available")
                    time.sleep(5)
                    continue

                # 🔍 DATA VALIDATION LOGGING
                current_price = self.data_collector.get_current_price()
                if current_price:
                    self.logger.info(f"🔍 LIVE DATA VALIDATION:")
                    self.logger.info(f"   Current Price: Bid={current_price['bid']:.2f}, Ask={current_price['ask']:.2f}")
                    self.logger.info(f"   Latest Bar: {latest_data.index[-1]} - Close={latest_data.iloc[-1]['close']:.2f}")
                    self.logger.info(f"   Data Points: {len(latest_data)} bars")

                    # Check data freshness
                    latest_time = latest_data.index[-1]
                    time_diff = (datetime.now() - latest_time).total_seconds() / 60
                    self.logger.info(f"   Data Age: {time_diff:.1f} minutes")

                    # 🔍 CRITICAL: Track if data is actually changing
                    latest_bar = latest_data.iloc[-1]
                    recent_bars = latest_data.tail(5)

                    # Create hash of recent OHLC data
                    import hashlib
                    ohlc_data = recent_bars[['open', 'high', 'low', 'close']].values
                    data_hash = hashlib.md5(str(ohlc_data).encode()).hexdigest()[:8]

                    self.logger.info(f"🔍 DATA CHANGE TRACKING:")
                    self.logger.info(f"   Recent 5 bars hash: {data_hash}")
                    self.logger.info(f"   Latest OHLC: O={latest_bar['open']:.2f}, H={latest_bar['high']:.2f}, L={latest_bar['low']:.2f}, C={latest_bar['close']:.2f}")

                    # Compare with previous cycle
                    if not hasattr(self, '_previous_data_hash'):
                        self._previous_data_hash = None
                        self._previous_close = None

                    if self._previous_data_hash == data_hash:
                        self.logger.error(f"🚨 CRITICAL: DATA NOT CHANGING! Hash {data_hash} same as previous cycle")
                        self.logger.error(f"🚨 This explains why predictions are identical!")
                    else:
                        self.logger.info(f"✅ Data changed: {self._previous_data_hash} → {data_hash}")
                        # 🔧 INVALIDATE FEATURE CACHE: New data means we need fresh features
                        self._cached_features = None
                        self._cached_features_timestamp = None
                        self._cached_market_data_hash = None
                        self.logger.info(f"🗑️  Invalidated feature cache due to data change")

                    if self._previous_close is not None:
                        price_change = latest_bar['close'] - self._previous_close
                        self.logger.info(f"   Price change: {price_change:.2f} pips")

                    self._previous_data_hash = data_hash
                    self._previous_close = latest_bar['close']

                    if time_diff > 10:
                        self.logger.warning(f"⚠️  Data may be stale: {time_diff:.1f} minutes old")
                else:
                    self.logger.warning("❌ No current price data available")
                
                # Generate features
                features = self._generate_features(latest_data)

                if features is None or features.empty:
                    self.logger.warning("Feature generation failed")
                    time.sleep(self.prediction_interval)
                    continue

                # 🔍 FEATURE VALIDATION LOGGING
                self.logger.info(f"🔍 FEATURE VALIDATION:")
                self.logger.info(f"   Features Shape: {features.shape}")
                self.logger.info(f"   Feature Columns: {len(features.columns)}")

                # Check for NaN values
                nan_count = features.isnull().sum().sum()
                if nan_count > 0:
                    self.logger.warning(f"⚠️  Features contain {nan_count} NaN values")
                    nan_cols = features.columns[features.isnull().any()].tolist()
                    self.logger.warning(f"   NaN columns: {nan_cols[:10]}...")  # Show first 10
                else:
                    self.logger.info(f"✅ No NaN values in features")

                # Show sample feature values
                if not features.empty:
                    sample_features = features.iloc[-1]  # Latest row
                    non_zero_features = sample_features[sample_features != 0]
                    self.logger.info(f"   Non-zero features: {len(non_zero_features)}/{len(sample_features)}")

                    # Show some key feature values
                    key_features = ['close', 'volume', 'rsi', 'macd', 'bb_upper', 'bb_lower']
                    available_key_features = [f for f in key_features if f in features.columns]
                    if available_key_features:
                        feature_sample = {f: sample_features.get(f, 'N/A') for f in available_key_features[:5]}
                        self.logger.info(f"   Sample features: {feature_sample}")
                
                # Make model prediction
                prediction_result = self.model_engine.predict(features)

                if not prediction_result['success']:
                    self.logger.error(f"Model prediction failed: {prediction_result.get('error', 'Unknown error')}")
                    self.loop_stats['failed_predictions'] += 1
                    time.sleep(self.prediction_interval)
                    continue

                # 🔍 MODEL PREDICTION VALIDATION LOGGING
                self.logger.info(f"🔍 MODEL PREDICTION VALIDATION:")
                self.logger.info(f"   Prediction Success: {prediction_result['success']}")

                if 'predictions' in prediction_result:
                    predictions = prediction_result['predictions']
                    self.logger.info(f"   Raw Predictions: {predictions}")

                    # Check individual model outputs
                    if hasattr(predictions, 'get'):
                        lightgbm_pred = predictions.get('lightgbm', {})
                        catboost_pred = predictions.get('catboost', {})
                        xgboost_pred = predictions.get('xgboost', {})
                        linear_pred = predictions.get('linear', {})

                        self.logger.info(f"   LightGBM Raw: {lightgbm_pred}")
                        self.logger.info(f"   CatBoost Raw: {catboost_pred}")
                        self.logger.info(f"   XGBoost Raw: {xgboost_pred}")
                        self.logger.info(f"   Linear Raw: {linear_pred}")

                if 'hierarchical_decision' in prediction_result:
                    hierarchical = prediction_result['hierarchical_decision']
                    self.logger.info(f"   Hierarchical Decision: {hierarchical.decision}")
                    self.logger.info(f"   Overall Confidence: {hierarchical.overall_confidence}")
                    self.logger.info(f"   Consensus Score: {hierarchical.consensus_score}")
                    self.logger.info(f"   Risk Score: {hierarchical.risk_score}")

                self.loop_stats['successful_predictions'] += 1

                # ENHANCED: Use hierarchical decision framework
                hierarchical_decision, model_predictions = self._make_hierarchical_decision(
                    prediction_result, latest_data
                )

                if hierarchical_decision.final_decision in ['BUY', 'SELL']:
                    self._attempt_enhanced_trade_execution(hierarchical_decision, latest_data, model_predictions)

                # Check multi-tier exits for active positions
                if hasattr(self.execution_engine, 'check_multi_tier_exits'):
                    self.execution_engine.check_multi_tier_exits()

                # Update positions
                self._update_existing_positions(latest_data)
                
                # Update loop statistics
                cycle_time_ms = (time.time() - cycle_start_time) * 1000
                self._update_loop_stats(cycle_time_ms)
                
                # Sleep until next cycle
                time.sleep(self.prediction_interval)
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {str(e)}")
                self.loop_stats['errors'].append({
                    'timestamp': datetime.now(),
                    'error': str(e)
                })
                time.sleep(5)  # Wait before retrying
        
        self.logger.info("Trading loop stopped")

    def _make_hierarchical_decision(self, prediction_result: Dict[str, Any],
                                  market_data: pd.DataFrame) -> tuple:
        """
        Make hierarchical decision using specialized ensemble architecture.

        Args:
            prediction_result: Raw model predictions
            market_data: Current market data

        Returns:
            Tuple of (HierarchicalDecision object, model_predictions dict)
        """
        try:
            # ENHANCED: Use HierarchicalDecisionFramework as PRIMARY decision system
            self.logger.info("✅ Using HierarchicalDecisionFramework as primary decision system")

            # Get model predictions from the ensemble if available for hierarchical analysis
            has_model_engine = hasattr(self, 'model_engine')
            has_ensemble_model = has_model_engine and hasattr(self.model_engine, 'ensemble_model')
            ensemble_not_none = has_ensemble_model and self.model_engine.ensemble_model is not None

            # Extract individual model predictions for hierarchical decision
            individual_predictions = {}
            if has_model_engine and has_ensemble_model and ensemble_not_none:
                # Get features for individual model predictions
                features = self._prepare_features_for_ensemble(market_data)
                if features is not None:
                    # Get individual model outputs from the ensemble
                    ensemble_outputs = self.model_engine.ensemble_model._gather_specialized_outputs(features)

                    # Extract predictions by model type
                    for output_type, output in ensemble_outputs.items():
                        model_name = output.model_name
                        individual_predictions[model_name] = {
                            'confidence': output.confidence,
                            'outputs': output.outputs,
                            'processing_time': output.processing_time
                        }
                        # 🔍 DEBUG: Log each model's detailed output
                        self.logger.info(f"🔍 {model_name.upper()} DETAILED OUTPUT:")
                        self.logger.info(f"   Confidence: {output.confidence}")
                        self.logger.info(f"   Outputs: {output.outputs}")
                        self.logger.info(f"   Processing Time: {output.processing_time}")

                    self.logger.info(f"🔍 Extracted predictions from {len(individual_predictions)} models for hierarchical analysis")
                else:
                    self.logger.warning("⚠️ Feature preparation failed, using prediction_result fallback")

            # Extract model predictions by type - use individual predictions if available
            if individual_predictions:
                model_predictions = individual_predictions
            else:
                # Fallback to prediction_result if individual predictions not available
                model_predictions = {
                    'lightgbm': prediction_result.get('predictions', {}),
                    'catboost': prediction_result.get('predictions', {}),
                    'xgboost': prediction_result.get('predictions', {}),
                    'linear': prediction_result.get('predictions', {})
                }

            # Create market context
            market_context = {
                'current_price': market_data['close'].iloc[-1],
                'volatility_regime': self._assess_volatility_regime(market_data),
                'market_regime': 'normal',  # Could be enhanced with regime detection
                'data_quality_score': 1.0,
                'emergency_stop_triggered': False,
                'asian_session_multiplier': 0.9,
                'european_session_multiplier': 1.0,
                'us_session_multiplier': 1.3
            }

            # Make hierarchical decision
            self.logger.info(f"🔍 Calling hierarchical decision with {len(model_predictions)} model predictions")

            # Log individual model outputs before hierarchical analysis
            self.logger.info("=" * 60)
            self.logger.info("📊 INDIVIDUAL MODEL OUTPUTS:")
            for model_name, prediction in model_predictions.items():
                print("prediction***: ", prediction)
                if isinstance(prediction, dict):
                    confidence = prediction.get('confidence', 0.0)
                    outputs = prediction.get('outputs', {})
                    key_metrics = []

                    # Extract key metrics for each model type
                    if 'signal_strength' in outputs:
                        key_metrics.append(f"signal_strength={outputs['signal_strength']:.3f}")
                    if 'confidence_score' in outputs:
                        key_metrics.append(f"confidence_score={outputs['confidence_score']:.3f}")
                    if 'direction_classification' in outputs:
                        key_metrics.append(f"direction={outputs['direction_classification']}")
                    if 'signal_probability' in outputs:
                        key_metrics.append(f"signal_prob={outputs['signal_probability']:.3f}")
                    if 'risk_classification' in outputs:
                        key_metrics.append(f"risk={outputs['risk_classification']}")
                    if 'market_regime' in outputs:
                        key_metrics.append(f"regime={outputs['market_regime']}")
                    if 'session_favorability' in outputs:
                        key_metrics.append(f"session={outputs['session_favorability']}")
                    if 'volatility_state' in outputs:
                        key_metrics.append(f"volatility={outputs['volatility_state']}")
                    if 'baseline_signal' in outputs:
                        key_metrics.append(f"baseline={outputs['baseline_signal']}")
                    if 'system_health_score' in outputs:
                        key_metrics.append(f"health={outputs['system_health_score']:.3f}")

                    metrics_str = ", ".join(key_metrics) if key_metrics else "no key metrics"
                    self.logger.info(f"📊 {model_name.upper()}: confidence={confidence:.3f} | {metrics_str}")
                else:
                    self.logger.info(f"📊 {model_name.upper()}: {prediction}")

            self.logger.info("=" * 60)

            hierarchical_decision = self.hierarchical_decision.make_decision(
                model_predictions, market_context
            )

            # Log detailed hierarchical decision results
            self.logger.info(f"🎯 Hierarchical decision received: {hierarchical_decision.final_decision}")
            self.logger.info(f"   Overall confidence: {hierarchical_decision.overall_confidence:.3f}")
            self.logger.info(f"   Consensus score: {hierarchical_decision.consensus_score:.3f}")
            self.logger.info(f"   Risk score: {hierarchical_decision.risk_score:.3f}")
            self.logger.info(f"   Entry timing: {hierarchical_decision.entry_timing}")
            self.logger.info(f"   Position sizing: {hierarchical_decision.position_sizing:.3f}")

            # Log level-by-level analysis
            for level_num, level_result in hierarchical_decision.decision_levels.items():
                level_status = "✅ PROCEED" if level_result.decision in ["PROCEED", "HOLD", "BUY", "SELL"] else "❌ VETO" 
                self.logger.info(f"🛡️  LEVEL {level_num} ({level_result.name}): {level_status}")
                self.logger.info(f"   Confidence: {level_result.confidence:.3f} | Model: {level_result.model_type}")
                if level_result.reasoning:
                    self.logger.info(f"   Reasoning: {level_result.reasoning}")
                if level_result.veto_reason:
                    self.logger.info(f"   ❌ VETO REASON: {level_result.veto_reason}")

            self.logger.info("=" * 60)

            # Update weight system with performance
            for model_type in model_predictions.keys():
                self.weight_system.update_model_performance(
                    model_type,
                    model_predictions[model_type]
                )

            return hierarchical_decision, model_predictions

        except Exception as e:
            self.logger.error(f"Hierarchical decision failed: {str(e)}")
            import traceback
            self.logger.error(f"Hierarchical decision traceback: {traceback.format_exc()}")
            # Return safe default decision
            from .hierarchical_decision import HierarchicalDecision
            return HierarchicalDecision(
                final_decision="HOLD",
                overall_confidence=0.0,
                decision_levels={},
                consensus_score=0.0,
                risk_score=1.0,
                entry_timing="WAIT",
                position_sizing=0.0,
                tp_sl_levels={},
                timestamp=datetime.now()
            ), {}

    def _attempt_enhanced_trade_execution(self, hierarchical_decision: Any,
                                        market_data: pd.DataFrame,
                                        model_predictions: Dict[str, Any]):
        """
        Attempt enhanced trade execution using model-driven optimization.

        Args:
            hierarchical_decision: HierarchicalDecision object
            market_data: Current market data
            model_predictions: Dictionary containing predictions from all models
        """
        try:
            self.loop_stats['trades_attempted'] += 1

            # CRITICAL FIX: Pass actual model predictions to entry decision
            # This ensures the entry decision uses actual model outputs instead of empty dictionaries
            entry_decision = self.execution_optimizer.analyze_entry_opportunity(
                hierarchical_decision.__dict__,
                model_predictions,  # Use actual model predictions instead of empty dicts
                market_data
            )

            if not entry_decision.should_enter:
                self.logger.info(f"Entry declined: {entry_decision.reasoning}")
                return

            # Get current price
            current_price = market_data['close'].iloc[-1]
            direction = hierarchical_decision.final_decision

            # CRITICAL FIX: Extract actual XGBoost model outputs directly from model predictions
            # This ensures we use the ACTUAL model predictions instead of hardcoded fallbacks

            self.logger.info("🔍 EXTRACTING ACTUAL XGBOOST MODEL OUTPUTS:")

            # Get XGBoost model predictions directly
            xgboost_prediction = model_predictions.get('xgboost', {})
            xgboost_outputs = xgboost_prediction.get('outputs', {})

            # Extract actual XGBoost TP/SL predictions
            actual_sl_pips = xgboost_outputs.get('stop_loss_pips', None)
            actual_tp_levels = xgboost_outputs.get('take_profit_levels', None)
            actual_position_multiplier = xgboost_outputs.get('position_size_multiplier', None)

            self.logger.info(f"   Raw XGBoost outputs: {xgboost_outputs}")

            if actual_sl_pips is not None:
                sl_distance = actual_sl_pips
                self.logger.info(f"   ✅ Using actual XGBoost SL: {sl_distance:.2f} pips")
            else:
                sl_distance = 80.0  # Conservative fallback only if model fails
                self.logger.warning(f"   ❌ No XGBoost SL found, using fallback: {sl_distance:.1f} pips")

            if actual_tp_levels is not None and len(actual_tp_levels) >= 3:
                tp1_distance = actual_tp_levels[0]
                tp2_distance = actual_tp_levels[1]
                tp3_distance = actual_tp_levels[2]
                self.logger.info(f"   ✅ Using actual XGBoost TP levels: {actual_tp_levels}")
            else:
                # Conservative fallbacks only if model fails
                tp1_distance = 60.0
                tp2_distance = 120.0
                tp3_distance = 180.0
                self.logger.warning(f"   ❌ No XGBoost TP levels found, using fallbacks: [{tp1_distance}, {tp2_distance}, {tp3_distance}]")

            self.logger.info(f"🎯 FINAL ACTUAL MODEL-DRIVEN TP/SL LEVELS:")
            self.logger.info(f"   SL: {sl_distance:.2f} pips (XGBoost actual)")
            self.logger.info(f"   TP1: {tp1_distance:.2f} pips (40% position)")
            self.logger.info(f"   TP2: {tp2_distance:.2f} pips (35% position)")
            self.logger.info(f"   TP3: {tp3_distance:.2f} pips (25% position)")

            # CRITICAL FIX: Use actual XGBoost position size multiplier
            if actual_position_multiplier is not None:
                final_position_multiplier = actual_position_multiplier
                self.logger.info(f"   ✅ Using actual XGBoost position multiplier: {final_position_multiplier:.6f}")
            else:
                final_position_multiplier = entry_decision.position_size_multiplier
                self.logger.warning(f"   ❌ No XGBoost position multiplier found, using entry decision: {final_position_multiplier:.6f}")

            # Create enhanced trading signal with ACTUAL XGBoost model outputs
            print("direction_received:", direction.lower())
            enhanced_signal = {
                'direction': direction.lower(),
                'confidence': hierarchical_decision.overall_confidence,
                'current_price': current_price,
                'entry_price': entry_decision.optimal_entry_price,
                'position_size_multiplier': final_position_multiplier,  # ACTUAL XGBoost output
                'tp1_distance': tp1_distance,  # ACTUAL XGBoost output
                'tp2_distance': tp2_distance,  # ACTUAL XGBoost output
                'tp3_distance': tp3_distance,  # ACTUAL XGBoost output
                'sl_distance': sl_distance,    # ACTUAL XGBoost output
                'volatility_adjustment': 1.0,  # Can be enhanced later
                'entry_timing': entry_decision.timing_strategy.value,
                'reasoning': f"Hierarchical: {hierarchical_decision.overall_confidence:.3f}, ACTUAL XGBoost TP/SL: [{tp1_distance:.2f}, {tp2_distance:.2f}, {tp3_distance:.2f}] pips, Pos Mult: {final_position_multiplier:.4f}"
            }

            # Execute trade using existing execution engine
            self._execute_enhanced_trade(enhanced_signal, market_data)

        except Exception as e:
            self.logger.error(f"Enhanced trade execution failed: {str(e)}")

    def _execute_enhanced_trade(self, enhanced_signal: Dict[str, Any],
                              market_data: pd.DataFrame):
        """Execute trade with enhanced signal parameters."""
        try:
            # 5-Layer Risk Assessment
            portfolio_metrics = self._get_portfolio_metrics()
            risk_result = self.five_layer_risk.assess_comprehensive_risk(
                enhanced_signal, self.active_trades, market_data, portfolio_metrics
            )

            # Check risk result
            if risk_result.final_action in ['EMERGENCY_STOP', 'CLOSE_POSITIONS', 'BLOCK_NEW']:
                self.logger.warning(f"5-Layer Risk blocked trade: {risk_result.reasoning}")
                return

            # Apply risk adjustments
            if risk_result.final_action == 'REDUCE_SIZE':
                enhanced_signal['position_size_multiplier'] *= risk_result.position_size_adjustment
                self.logger.info(f"Risk-adjusted position size: {enhanced_signal['position_size_multiplier']:.3f}")

            # Legacy risk check (backup)
            if not self.risk_manager.can_open_position(enhanced_signal):
                self.logger.warning("Legacy risk manager blocked trade")
                return

            # Execute multi-tier trade system
            trade_result = self.execution_engine.execute_trade(enhanced_signal)

            if trade_result.get('success', False):
                self.loop_stats['trades_executed'] += trade_result.get('num_trades', 1)

                # Handle multi-tier trade results
                trade_ids = trade_result.get('trade_ids', [])
                total_position_size = trade_result.get('total_position_size', 0)
                individual_sizes = trade_result.get('individual_sizes', [])

                self.logger.info(f"🎉 Multi-tier trade executed: {len(trade_ids)} trades, total {total_position_size:.2f} lots")

                # Create live trade records for each individual trade
                for i, trade_id in enumerate(trade_ids):
                    tier_name = f"TP{i+1}" if i < 3 else f"Trade{i+1}"
                    position_size = individual_sizes[i] if i < len(individual_sizes) else 0

                    live_trade = LiveTrade(
                        trade_id=trade_id,
                        symbol=self.config.get('symbol', 'XAUUSD!'),
                        direction=enhanced_signal['direction'],
                        entry_price=trade_result['entry_price'],
                        position_size=position_size,
                        tp1_price=enhanced_signal.get('tp1_price'),
                        tp2_price=enhanced_signal.get('tp2_price'),
                        tp3_price=enhanced_signal.get('tp3_price'),
                        sl_price=enhanced_signal.get('sl_price'),
                        model_confidence=enhanced_signal['confidence'],
                        model_consensus={},
                        signal_probability=enhanced_signal['confidence'],
                        entry_time=datetime.now(),
                        entry_type=OrderType.MARKET,
                        metadata={
                            'tier': tier_name,
                            'tier_index': i+1,
                            'entry_timing': enhanced_signal.get('entry_timing'),
                            'tp1_percentage': enhanced_signal.get('tp1_percentage'),
                            'tp2_percentage': enhanced_signal.get('tp2_percentage'),
                            'trailing_percentage': enhanced_signal.get('trailing_percentage'),
                            'max_hold_time_minutes': enhanced_signal.get('max_hold_time_minutes'),
                            'reasoning': enhanced_signal.get('reasoning')
                        }
                    )

                    self.active_trades.append(live_trade)
                    self.risk_manager.register_trade(live_trade)

                    self.logger.info(f"✅ {tier_name} trade executed: {trade_id} - "
                                   f"{enhanced_signal['direction']} {position_size:.2f} lots @ {trade_result['entry_price']:.2f}")

                self.logger.info(f"🎯 Multi-tier system complete: {len(trade_ids)} trades, "
                               f"total {total_position_size:.2f} lots, "
                               f"confidence: {enhanced_signal['confidence']:.3f}")
            else:
                self.logger.error(f"Trade execution failed: {trade_result.get('message', 'Unknown error')}")

        except Exception as e:
            self.logger.error(f"Enhanced trade execution error: {str(e)}")

    def _assess_volatility_regime(self, market_data: pd.DataFrame) -> str:
        """Assess current volatility regime."""
        try:
            # Calculate recent volatility
            returns = market_data['close'].pct_change().dropna()
            recent_vol = returns.tail(20).std() * np.sqrt(1440)  # Annualized

            # Simple volatility regime classification
            if recent_vol < 0.15:
                return 'low'
            elif recent_vol < 0.25:
                return 'normal'
            elif recent_vol < 0.35:
                return 'high'
            else:
                return 'extreme'

        except Exception:
            return 'normal'

    def _get_portfolio_metrics(self) -> Dict[str, Any]:
        """Get current portfolio metrics for risk assessment."""
        try:
            # Calculate basic portfolio metrics
            total_pnl = sum(trade.unrealized_pnl for trade in self.active_trades if hasattr(trade, 'unrealized_pnl'))
            total_exposure = len(self.active_trades) * 0.02  # Rough estimate

            # Get account balance from risk manager (now uses real MT5 balance)
            account_balance = self.risk_manager.current_balance if self.risk_manager else 470.0

            return {
                'daily_pnl': total_pnl / account_balance if account_balance > 0 else 0.0,
                'current_drawdown': 0.0,  # Would be calculated from equity curve
                'system_health_score': 1.0,  # Would be calculated from system metrics
                'total_exposure': total_exposure,
                'correlation_exposure': 0.1,  # Would be calculated from cross-asset correlations
                'dxy_correlation': 0.0,  # Would come from cross-asset analysis
                'regime_stability': 1.0,  # Would come from regime detection
                'vix_spike_detected': False  # Would come from VIX monitoring
            }

        except Exception as e:
            self.logger.error(f"Portfolio metrics calculation failed: {str(e)}")
            return {
                'daily_pnl': 0.0,
                'current_drawdown': 0.0,
                'system_health_score': 0.8,
                'total_exposure': 0.0,
                'correlation_exposure': 0.0,
                'dxy_correlation': 0.0,
                'regime_stability': 1.0,
                'vix_spike_detected': False
            }
    
    def _generate_features(self, market_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Generate features from market data with intelligent caching."""
        try:
            if len(market_data) < 50:  # Need sufficient history
                return None

            # 🔧 FEATURE CACHING: Check if we can reuse cached features
            import hashlib
            market_data_hash = hashlib.md5(str(market_data.iloc[-10:].values).encode()).hexdigest()[:8]
            current_time = time.time()

            # Use cached features if:
            # 1. Same market data (hash matches)
            # 2. Generated within the last 60 seconds (same trading cycle)
            # 3. Cached features exist
            if (self._cached_features is not None and
                self._cached_market_data_hash == market_data_hash and
                self._cached_features_timestamp is not None and
                current_time - self._cached_features_timestamp < 60):

                self.logger.info(f"🔄 Using cached features (hash: {market_data_hash}, age: {current_time - self._cached_features_timestamp:.1f}s)")
                return self._cached_features

            self.logger.info(f"🔧 Generating fresh features (hash: {market_data_hash})")

            # Use the latest row for feature generation
            latest_row = market_data.iloc[-1:]
            
            # Generate features using existing engines
            all_features = []
            
            for feature_type, engine in self.feature_engines.items():
                try:
                    result = engine.generate_features(market_data)
                    if result.features is not None and not result.features.empty:
                        # Get the latest features
                        latest_features = result.features.iloc[-1:]
                        all_features.append(latest_features)
                except Exception as e:
                    self.logger.warning(f"Feature generation failed for {feature_type}: {str(e)}")
            
            if not all_features:
                return None
            
            # Combine all features
            combined_features = pd.concat(all_features, axis=1)

            # TEMPORARILY DISABLED: Advanced data quality optimizer
            # (Testing if this is causing static predictions)
            # optimized_features, quality_metrics = self.data_quality_optimizer.optimize_features(
            #     combined_features, market_data
            # )

            # # Log quality improvement
            # if quality_metrics.nan_count_before > 0:
            #     improvement_pct = ((quality_metrics.nan_count_before - quality_metrics.nan_count_after) /
            #                      quality_metrics.nan_count_before) * 100
            #     self.logger.info(f"Data quality optimization: {improvement_pct:.1f}% NaN reduction, "
            #                    f"quality score: {quality_metrics.quality_score:.3f}")

            # Use advanced data quality optimizer for proper NaN handling
            self.logger.info(f"🔧 Applying advanced data quality optimization")
            self.logger.info(f"🔍 Raw features: {combined_features.shape}, NaN count: {combined_features.isnull().sum().sum()}")

            # Apply advanced data quality optimization
            optimized_features, quality_metrics = self.data_quality_optimizer.optimize_features(combined_features)

            # Log optimization results
            if quality_metrics.nan_count_before > 0:
                improvement_pct = ((quality_metrics.nan_count_before - quality_metrics.nan_count_after) /
                                 quality_metrics.nan_count_before) * 100
                self.logger.info(f"Data quality optimization: {improvement_pct:.1f}% NaN reduction, "
                               f"quality score: {quality_metrics.quality_score:.3f}")

            self.logger.info(f"🔍 Using raw features without NaN processing")

            # 🔍 CRITICAL: Track if features are changing
            import hashlib
            import numpy as np

            # Sample key features that should change with price
            key_features = ['close', 'high', 'low', 'open'] if any(col in optimized_features.columns for col in ['close', 'high', 'low', 'open']) else optimized_features.columns[:10]
            sample_features = optimized_features[key_features] if len(key_features) > 0 else optimized_features.iloc[:, :10]

            # Create hash of feature values
            feature_hash = hashlib.md5(str(sample_features.values).encode()).hexdigest()[:8]

            self.logger.info(f"🔍 FEATURE CHANGE TRACKING:")
            self.logger.info(f"   Feature matrix hash: {feature_hash}")
            self.logger.info(f"   Sample feature values: {sample_features.iloc[0].head(5).to_dict()}")

            # Compare with previous cycle
            if not hasattr(self, '_previous_feature_hash'):
                self._previous_feature_hash = None

            if self._previous_feature_hash == feature_hash:
                self.logger.error(f"🚨 CRITICAL: FEATURES NOT CHANGING! Hash {feature_hash} same as previous cycle")
                self.logger.error(f"🚨 This confirms why model predictions are identical!")
            else:
                self.logger.info(f"✅ Features changed: {self._previous_feature_hash} → {feature_hash}")

            self._previous_feature_hash = feature_hash

            # 🔧 CACHE FEATURES: Store for reuse within the same trading cycle
            self._cached_features = optimized_features.copy()
            self._cached_features_timestamp = current_time
            self._cached_market_data_hash = market_data_hash
            self.logger.info(f"💾 Cached features for reuse (hash: {market_data_hash})")

            return optimized_features
            
        except Exception as e:
            self.logger.error(f"Feature generation error: {str(e)}")
            return None

    def _handle_nan_values(self, features: pd.DataFrame) -> pd.DataFrame:
        """Handle NaN values in features with sophisticated approach."""
        try:
            original_nan_count = features.isnull().sum().sum()
            if original_nan_count == 0:
                return features

            self.logger.info(f"Handling {original_nan_count} NaN values in features")

            # Create a copy to avoid modifying original
            cleaned_features = features.copy()

            # Handle different types of features differently
            for col in cleaned_features.columns:
                if cleaned_features[col].isnull().any():
                    # Determine appropriate fill strategy based on feature type
                    fill_value = self._get_nan_fill_value(col, cleaned_features[col])

                    if fill_value == 'forward_fill':
                        # Forward fill for time series features
                        cleaned_features[col] = cleaned_features[col].fillna(method='ffill')
                    elif fill_value == 'interpolate':
                        # Interpolate for smooth features
                        cleaned_features[col] = cleaned_features[col].interpolate(method='linear')
                    else:
                        # Use specific fill value
                        cleaned_features[col] = cleaned_features[col].fillna(fill_value)

            # Final fallback - fill any remaining NaN with 0
            cleaned_features = cleaned_features.fillna(0.0)

            final_nan_count = cleaned_features.isnull().sum().sum()
            self.logger.info(f"NaN handling complete: {original_nan_count} -> {final_nan_count} NaN values")

            return cleaned_features

        except Exception as e:
            self.logger.error(f"Error handling NaN values: {str(e)}")
            # Fallback to simple fill
            return features.fillna(0.0)

    def _get_nan_fill_value(self, feature_name: str, feature_series: pd.Series):
        """Get appropriate fill value for NaN based on feature type."""

        # Price-based features
        if any(x in feature_name.lower() for x in ['return', 'roc', 'momentum']):
            return 0.0  # Neutral return

        # RSI and bounded indicators (0-100 range)
        elif any(x in feature_name.lower() for x in ['rsi', 'stoch', 'williams']):
            return 50.0  # Neutral RSI

        # Correlation features (-1 to 1 range)
        elif 'corr' in feature_name.lower():
            return 0.0  # No correlation

        # Volatility features
        elif any(x in feature_name.lower() for x in ['vol', 'atr', 'bb_width']):
            return feature_series.median() if not feature_series.isnull().all() else 0.01

        # Volume features
        elif 'volume' in feature_name.lower():
            return feature_series.median() if not feature_series.isnull().all() else 1000.0

        # Price level features (OHLC)
        elif any(x in feature_name.lower() for x in ['open', 'high', 'low', 'close', 'price']):
            return 'forward_fill'  # Use forward fill for price levels

        # Moving averages and smooth indicators
        elif any(x in feature_name.lower() for x in ['sma', 'ema', 'mean', 'vwap']):
            return 'interpolate'  # Interpolate smooth indicators

        # Regime and classification features
        elif any(x in feature_name.lower() for x in ['regime', 'cluster', 'class']):
            return 0.0  # Neutral regime

        # Percentage and ratio features
        elif any(x in feature_name.lower() for x in ['pct', 'ratio', 'percent']):
            return 0.0  # Neutral percentage

        # Boolean/binary features
        elif any(x in feature_name.lower() for x in ['is_', 'has_', 'above_', 'below_']):
            return 0.0  # False

        # Default fallback
        else:
            return 0.0
    
    def _attempt_trade_execution(self, trading_signal: Dict[str, Any], market_data: pd.DataFrame):
        """Attempt to execute a trade based on signal."""
        try:
            self.loop_stats['trades_attempted'] += 1
            
            # Get current price
            current_price_info = self.data_collector.get_current_price()
            if not current_price_info:
                self.logger.warning("Could not get current price for trade execution")
                return
            
            current_price = current_price_info['ask'] if trading_signal['direction'] == 'long' else current_price_info['bid']
            
            # Risk validation
            risk_validation = self.risk_manager.validate_trade_risk(
                trading_signal, 
                self.risk_manager.current_balance,
                list(self.active_trades.values())
            )
            
            if not risk_validation['approved']:
                self.logger.info(f"Trade rejected by risk manager: {risk_validation['rejections']}")
                return
            
            # Execute trade
            trade = self.execution_engine.execute_model_driven_trade(
                trading_signal,
                current_price,
                self.risk_manager.current_balance
            )
            
            if trade:
                self.add_trade(trade)
                self.loop_stats['trades_executed'] += 1
                self.logger.info(f"✅ Trade executed: {trade.trade_id}")
            else:
                self.logger.warning("Trade execution failed")
                
        except Exception as e:
            self.logger.error(f"Trade execution attempt failed: {str(e)}")
    
    def _update_existing_positions(self, market_data: pd.DataFrame):
        """Update existing positions and check exit conditions."""
        if not self.active_trades:
            return
        
        try:
            current_price_info = self.data_collector.get_current_price()
            if not current_price_info:
                return
            
            current_price = (current_price_info['bid'] + current_price_info['ask']) / 2
            
            for trade_id, trade in list(self.active_trades.items()):
                # Update unrealized P&L
                trade.update_unrealized_pnl(current_price)
                
                # Check exit conditions (simplified - could be enhanced with model-driven exits)
                should_exit, exit_reason = self._check_exit_conditions(trade, current_price)
                
                if should_exit:
                    self.close_trade(trade_id, current_price, exit_reason)
                    self.risk_manager.update_trade_result(trade)
                    
        except Exception as e:
            self.logger.error(f"Position update error: {str(e)}")
    
    def _check_exit_conditions(self, trade: LiveTrade, current_price: float) -> tuple:
        """Check if trade should be exited."""
        # Simple exit logic - could be enhanced with model-driven exits
        
        # Check stop loss
        if trade.direction == trade.direction.LONG:
            if current_price <= trade.sl_price:
                return True, "stop_loss"
            if current_price >= trade.tp1_price:
                return True, "take_profit_1"
        else:
            if current_price >= trade.sl_price:
                return True, "stop_loss"
            if current_price <= trade.tp1_price:
                return True, "take_profit_1"
        
        # Check time-based exit (simplified)
        time_in_trade = datetime.now() - trade.entry_time
        if time_in_trade > timedelta(hours=24):  # Max 24 hours
            return True, "time_exit"
        
        return False, None
    
    def _monitoring_loop(self):
        """Monitoring loop for performance tracking."""
        while not self.stop_event.is_set():
            try:
                # Log performance summary every 5 minutes
                if self.loop_stats['total_cycles'] % 10 == 0 and self.loop_stats['total_cycles'] > 0:
                    self._log_performance_summary()
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {str(e)}")
                time.sleep(60)
    
    def _update_loop_stats(self, cycle_time_ms: float):
        """Update trading loop statistics."""
        self.loop_stats['total_cycles'] += 1
        
        # Update average cycle time
        total_cycles = self.loop_stats['total_cycles']
        self.loop_stats['avg_cycle_time_ms'] = (
            (self.loop_stats['avg_cycle_time_ms'] * (total_cycles - 1) + cycle_time_ms) / total_cycles
        )
        
        self.loop_stats['last_cycle_time'] = datetime.now()
    
    def _log_performance_summary(self):
        """Log performance summary."""
        summary = self.get_performance_summary()
        
        self.logger.info(f"📊 Performance Summary - "
                        f"Cycles: {self.loop_stats['total_cycles']}, "
                        f"Predictions: {self.loop_stats['successful_predictions']}, "
                        f"Trades: {self.loop_stats['trades_executed']}, "
                        f"Active: {len(self.active_trades)}, "
                        f"P&L: ${summary.get('total_pnl', 0):.2f}")
    
    def execute_trade(self, signal: Dict[str, Any]) -> LiveTradingResult:
        """Execute a trade (interface method)."""
        # This method is called by the base class interface
        # The actual execution happens in the trading loop
        return LiveTradingResult(
            success=True,
            message="Trade signal queued for execution"
        )

    def _prepare_features_for_ensemble(self, market_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Prepare features for ensemble decision making using the same process as main trading loop."""
        try:
            if market_data.empty:
                return None

            # Use the same feature generation process as the main trading loop
            features = self._generate_features(market_data)

            if features is not None and not features.empty:
                # CRITICAL FIX: Apply feature alignment BEFORE ensemble prediction
                # This ensures ensemble gets exactly 354 features that match training data
                if hasattr(self.model_engine, 'feature_alignment') and self.model_engine.feature_alignment:
                    # Extract market data for OHLCV features if needed
                    market_data_for_alignment = None
                    if not market_data.empty:
                        market_data_for_alignment = market_data[['open', 'high', 'low', 'close', 'volume']].copy() if all(col in market_data.columns for col in ['open', 'high', 'low', 'close', 'volume']) else None

                    # Apply feature alignment to reduce 409 → 354 features
                    features_aligned = self.model_engine.feature_alignment.align_features(features, market_data_for_alignment)
                    self.logger.info(f"✅ Prepared features for ensemble: {features.shape} → {features_aligned.shape}")
                    return features_aligned
                else:
                    self.logger.warning("Feature alignment not available, using raw features")
                    return features
            else:
                self.logger.warning("Feature generation failed for ensemble")
                return None

        except Exception as e:
            self.logger.error(f"Failed to prepare features for ensemble: {str(e)}")
            return None

    def _convert_ensemble_to_hierarchical_decision(self, ensemble_decision) -> Any:
        """Convert EnsembleDecision to HierarchicalDecision format."""
        try:
            from .hierarchical_decision import HierarchicalDecision, DecisionLevel

            # Map ensemble direction to hierarchical decision
            direction_map = {
                'STRONG_BUY': 'BUY',
                'BUY': 'BUY',
                'STRONG_SELL': 'SELL',
                'SELL': 'SELL',
                'HOLD': 'HOLD'
            }

            final_decision = direction_map.get(ensemble_decision.trade_direction, 'HOLD')

            # Create decision levels based on ensemble components
            decision_levels = {
                1: DecisionLevel(
                    level=1, name="Safety Check", model_type="linear",
                    weight=0.10, veto_power=True,
                    decision=final_decision, confidence=ensemble_decision.system_health,
                    reasoning=f"System health: {ensemble_decision.system_health:.3f}"
                ),
                2: DecisionLevel(
                    level=2, name="Market Context", model_type="catboost",
                    weight=0.25, veto_power=True,
                    decision=final_decision, confidence=ensemble_decision.market_suitability,
                    reasoning=f"Market suitability: {ensemble_decision.market_suitability:.3f}"
                ),
                3: DecisionLevel(
                    level=3, name="Signal Quality", model_type="lightgbm",
                    weight=0.40, veto_power=True,
                    decision=final_decision, confidence=ensemble_decision.signal_quality,
                    reasoning=f"Signal quality: {ensemble_decision.signal_quality:.3f}"
                ),
                4: DecisionLevel(
                    level=4, name="Risk Management", model_type="xgboost",
                    weight=0.25, veto_power=False,
                    decision=final_decision, confidence=ensemble_decision.risk_assessment,
                    reasoning=f"Risk assessment: {ensemble_decision.risk_assessment:.3f}"
                )
            }

            # Create hierarchical decision with ensemble position sizing
            hierarchical_decision = HierarchicalDecision(
                final_decision=final_decision,
                overall_confidence=ensemble_decision.overall_confidence,
                decision_levels=decision_levels,
                consensus_score=ensemble_decision.overall_confidence,  # Use overall confidence as consensus
                risk_score=1.0 - ensemble_decision.risk_assessment,  # Invert risk assessment
                entry_timing="PATIENT" if ensemble_decision.entry_timing > 0 else "IMMEDIATE",
                position_sizing=ensemble_decision.position_size,  # CRITICAL: Use ensemble position sizing!
                tp_sl_levels={
                    'stop_loss_pips': ensemble_decision.stop_loss_pips,
                    'tp1_pips': ensemble_decision.take_profit_levels[0] if ensemble_decision.take_profit_levels else 15.0,
                    'tp2_pips': ensemble_decision.take_profit_levels[1] if len(ensemble_decision.take_profit_levels) > 1 else 25.0,
                    'tp3_pips': ensemble_decision.take_profit_levels[2] if len(ensemble_decision.take_profit_levels) > 2 else 35.0
                },
                timestamp=ensemble_decision.timestamp
            )

            self.logger.info(f"Converted ensemble decision: position_sizing={hierarchical_decision.position_sizing:.3f}")
            return hierarchical_decision

        except Exception as e:
            self.logger.error(f"Failed to convert ensemble to hierarchical decision: {str(e)}")
            # Return safe fallback
            from .hierarchical_decision import HierarchicalDecision
            return HierarchicalDecision(
                final_decision="HOLD",
                overall_confidence=0.0,
                decision_levels={},
                consensus_score=0.0,
                risk_score=1.0,
                entry_timing="WAIT",
                position_sizing=0.0,
                tp_sl_levels={},
                timestamp=datetime.now()
            )
    
    def update_positions(self) -> LiveTradingResult:
        """Update positions (interface method)."""
        # Position updates happen in the trading loop
        return LiveTradingResult(
            success=True,
            message="Positions updated in trading loop",
            data={
                'active_trades': len(self.active_trades),
                'last_update': datetime.now().isoformat()
            }
        )
    
    def get_account_info(self) -> Dict[str, Any]:
        """Get current account information."""
        return {
            'balance': self.risk_manager.current_balance if self.risk_manager else 0.0,
            'equity': self.risk_manager.current_balance + sum(t.unrealized_pnl for t in self.active_trades.values()),
            'margin_used': len(self.active_trades) * 1000,  # Simplified
            'free_margin': 10000,  # Simplified
            'active_trades': len(self.active_trades),
            'daily_pnl': self.risk_manager.daily_pnl if self.risk_manager else 0.0
        }
